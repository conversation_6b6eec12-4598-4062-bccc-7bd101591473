[workspace]
members = [
  "crates/bin/app",

  "crates/lib/core",
  "crates/lib/data",
  "crates/lib/account",
  "crates/lib/env",
  "crates/lib/learn",
]
resolver = "2"
[workspace.package]
authors = ["huang <<EMAIL>>"]
edition = "2024"
homepage = "https://home.risc-v.pro"
license = "Apache-2.0"
publish = false
repository = ""
version = "0.1.0"

[profile.release]
debug = true
opt-level = 3 # Optimize for size. "z"
panic = 'abort'
strip = true
lto = "thin"

[workspace.dependencies]
drl-trading-core = { path = "crates/lib/core" }
drl-trading-data = { path = "crates/lib/data" }
drl-trading-account = { path = "crates/lib/account" }
drl-trading-env = { path = "crates/lib/env" }
drl-trading-learn = { path = "crates/lib/learn" }

# 命令行和应用
clap = { version = "4.5.21", features = ["derive"] }
console = "0.15.0"
env_logger = "0.11.6"
burn = { version = "0.17.1", features = ["wgpu", "autodiff", "dataset", "ndarray"] }
log = "0.4.27"
log4rs = "1.3.0"
toml = "0.8.22"
once_cell = "1.21.3"

tokio = { version = "1.45.0", features = ["full"] }

# 序列化/反序列化
serde = { version = "1", features = ["derive"] }
serde_json = "1.0.140"
serde_yaml = "0.9"
dotenvy = "0.15.7"

# 异步与并发
futures = "0.3.31"
async-trait = "0.1.88"
# 错误处理
anyhow = "1.0.98"
thiserror = "2.0.8"

# 工具库
taos = { version = "0.12.3", default-features = false, features = ["ws"] }

dashmap = "7.0.0-rc2"
chrono = { version = "0.4", features = ["serde"] }
rs-snowflake = "0.6.0"
lazy_static = "1.5.0"

# 网络与API
reqwest = { version = "0.12.15", features = ["json"] }
tonic = { version = "0.13.1", features = ["transport"] }

tracing = "0.1"
tracing-subscriber = "0.3.19"
tempfile = "3.20.0"
nacos-sdk = { version = "0.5.0", features = ["naming"] }
structopt = "0.3.26"
rand = "0.9.1"
rand_distr = "0.5.1"  # 提供正态分布支持

# 数值计算
rust_decimal = { version = "1.36", features = ["serde", "db-postgres"] }
rust_decimal_macros = "1.36"

# 数据处理
polars = { version = "0.48.1", features = ["lazy", "csv", "parquet"] }

# 技术分析指标
ta = "0.5.0"

csv = "1.3.1"




