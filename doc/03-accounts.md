# 模块三：`account` - 账户与投资组合模块

## 1. 模块分析

### 1.1 模块功能与设计哲学

`account` Crate 是整个交易系统的 **状态核心** 和 **事实的唯一来源 (Single Source of Truth)**。它的核心职责是精确地跟踪、计算和报告所有与资金、资产和历史业绩相关的状态。

其设计哲学是 **"响应式状态更新"**:

-   它不产生任何主动的业务行为（如创建订单）。
-   它被动地接收外部事件（主要是 `Trade` 和 `Tick`），并基于这些事件可靠地更新其内部状态。
-   任何时候，当系统的其他部分需要了解"我们现在有多少钱？"或"我们的持仓是什么？"时，`account` 模块是唯一可信的答案来源。

### 1.2 模块评价

-   **优点**:
    1.  **关注点分离 (SoC)**: 模块内部被清晰地划分为 `state`（资金）、`portfolio`（持仓）、`performance`（业绩）三个部分，每个部分职责单一，高度内聚。
    2.  **响应式设计**: 被动更新的设计使得状态管理非常可靠，易于测试和推理，避免了复杂的双向依赖。
    3.  **计算精确**: 所有货币计算都使用 `rust_decimal::Decimal`，确保了金融场景下的计算精度。
    4.  **优秀的接口设计**: 提供了 `get_encoding_features_readonly` 方法，为下游的 `env` 模块提供了一个稳定、只读的数据视图，避免了下游模块的业务逻辑耦合，这是非常出色的架构设计。
-   **潜在问题**:
    -   未发现明显问题。该模块是整个项目中设计最严谨、最核心的模块之一，实现质量非常高。

## 2. 文件功能解析

### 2.1 `lib.rs`

-   **职能**: Crate 的入口，负责组合并导出公共组件。
-   **实现细节**: 声明了 `account`, `state`, `portfolio`, `performance` 四个核心子模块，并使用 `pub use` 导出了顶层的 `Account` 结构体以及各个子模块的关键类型。

### 2.2 `errors.rs`

-   **职能**: 定义与账户相关的特定错误类型。
-   **实现细节**: `AccountError` 枚举覆盖了资金不足、持仓未找到、状态不一致等多种业务场景，使得错误信息非常明确。

### 2.3 `state.rs` - 资金状态机

-   **职能**: `AccountState` 作为原子化的资金状态机，只负责跟踪和管理最核心的资金数字。
-   **实现细节**:
    -   管理 `total_balance`, `available_balance`, `realized_pnl`, `total_fees` 等核心资金字段。
    -   提供原子的资金操作方法，如 `deposit`, `withdraw`, `freeze_funds`, `unfreeze_funds`, `pay_fee`。这些方法都包含了必要的校验逻辑，确保资金状态的正确性。

### 2.4 `portfolio.rs` - 持仓管理器

-   **职能**: `Portfolio` 专门负责管理 **所有当前持仓**。
-   **实现细节**:
    -   **多品种支持**: 使用 `HashMap<Symbol, Position>` 来存储持仓，天然支持多品种同时交易。
    -   **逻辑委托**: 其 `on_trade` 方法的核心是获取对应的 `Position` 结构体（来自 `core` 模块），然后调用其 `update_with_trade` 方法。这种设计将"管理多个持仓"的职责（`Portfolio`）和"更新单个持仓"的职责（`Position`）清晰地分离开。
    -   **价格缓存与计算**: 缓存了每个品种的最新价格，并实现了一套简单的缓存失效机制（`cache_valid`），用于高效地计算总未实现盈亏（`unrealized_pnl`），避免了不必要的重复计算。

### 2.5 `performance.rs` - 业绩分析器

-   **职能**: `Performance` 专门负责跟踪历史业绩并计算关键指标。
-   **实现细节**:
    -   **权益曲线**: 内部维护一个 `Vec<EquityPoint>`，记录了权益随时间变化的完整历史（即权益曲线）。
    -   **实时指标计算**: 在每次 `add_equity_point` 时，都会实时更新 `peak_equity`（权益峰值），并以此计算出**最大回撤（Max Drawdown）**和**最大回撤持续时间**，这些都是衡量策略风险的关键指标。
    -   **业绩报告**: `get_performance_metrics` 方法提供了一个包含总回报率、最大回撤等关键指标的业绩摘要结构体。

### 2.6 `account.rs` - 顶层聚合器

-   **职能**: `Account` 是整个模块的"指挥官"和对外唯一入口。
-   **实现细节**:
    -   **组合**: 它将 `AccountState`, `Portfolio`, `Performance` 三个核心组件组合在一起。
    -   **事件路由**: 提供了 `on_trade` 和 `on_tick` 两个核心的事件处理方法。它负责协调内部组件的更新顺序，确保数据流的正确性。例如，`on_trade` 事件会触发 `Portfolio` 的更新，然后可能会产生已实现盈亏，这个盈亏结果会被用来更新 `AccountState`。
    -   **统一视图**: 提供了 `get_equity`, `get_account_summary` 等方法，将内部多个组件的数据整合起来，为外部调用者提供一个统一、全面的账户信息视图。

## 3. 模块连接关系

-   **上游依赖**: `drl-trading-core`。这是最关键的依赖，账户模块的所有核心计算都建立在 `core` 模块定义的 `Position`, `Trade` 等结构体及其方法之上。
-   **下游被依赖**: 主要被 `execution` 和 `env` 模块依赖。
    -   `execution` 模块需要查询账户状态（如可用资金）来决定是否可以执行交易。
    -   `env` 模块在每个时间步都需要从 `account` 获取状态信息，作为强化学习 Agent 观测的一部分。
-   **逻辑检查**:
    -   模块内部的组件（`state`, `portfolio`, `performance`）之间的逻辑连接是单向、清晰的，由顶层的 `Account` 结构体统一协调，没有循环依赖或混乱的调用关系。
    -   与 `core` 模块的连接堪称典范：`account` 模块消费 `core` 模块定义的**数据结构**，并利用其自带的**无状态计算方法**（如 `Position::update_with_trade`）来构建自身的**有状态服务**。这是分层架构的最佳实践。
    -   未发现逻辑连接问题。 