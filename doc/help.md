
cargo run -p drl-trading -- run --mode train --config config/real_train.toml


cargo test --test training_validation -p drl-trading-learn -- --nocapture


cargo run -p drl-trading -- init-config --mode train --output train_config.toml


drl-trading  run --mode train --config config/real_train.toml


// let action_vector = if total_steps < warmup_steps {
            //     generate_random_action(config)
            // } else {
            //     agent.select_action(&current_state_vector, true)?
            // };