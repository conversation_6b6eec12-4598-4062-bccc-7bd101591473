# Workspace依赖管理指南

## 总览

本项目采用Cargo Workspace模式进行依赖管理，所有子项目的依赖必须在根目录的`Cargo.toml`中统一声明，子项目不得独立管理依赖版本。

## 依赖管理原则

### ✅ 正确做法

1. **统一版本管理**：所有外部依赖版本在根目录`[workspace.dependencies]`中定义
2. **子项目引用**：子项目使用`dependency.workspace = true`引用依赖
3. **内部依赖**：子项目间依赖使用`crate-name.workspace = true`

### ❌ 错误做法

1. **独立版本声明**：子项目不得独立指定依赖版本
2. **重复依赖**：不同子项目使用相同依赖的不同版本
3. **路径依赖**：子项目间不使用相对路径依赖

## 配置示例

### 根目录 Cargo.toml

```toml
[workspace]
members = [
  "crates/lib/core",
  "crates/lib/data",
  # 其他成员...
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["huang <<EMAIL>>"]

[workspace.dependencies]
# 内部 crates
drl-trading-core = { path = "crates/lib/core" }
drl-trading-data = { path = "crates/lib/data" }

# 外部依赖
tokio = { version = "1.45.0", features = ["full"] }
serde = { version = "1", features = ["derive"] }
rust_decimal = { version = "1.36", features = ["serde", "db-postgres"] }
async-trait = "0.1.88"
thiserror = "2.0.8"
```

### 子项目 Cargo.toml

```toml
[package]
name = "drl-trading-data"
version.workspace = true
edition = "2021"
authors.workspace = true

[dependencies]
# 内部依赖
drl-trading-core.workspace = true

# 外部依赖
tokio.workspace = true
serde.workspace = true
rust_decimal.workspace = true
async-trait.workspace = true
thiserror.workspace = true

[dev-dependencies]
tempfile.workspace = true
```

## 新模块开发规范

### 1. 创建新模块时

1. 在根目录`[workspace.dependencies]`中添加所需依赖
2. 在子项目中使用`.workspace = true`引用
3. 不要在子项目中直接指定版本

### 2. 添加新依赖时

1. 首先在根目录添加依赖及版本
2. 然后在需要的子项目中引用
3. 考虑是否其他子项目也需要此依赖

### 3. 更新依赖版本时

1. 只在根目录更新版本号
2. 所有子项目自动使用新版本
3. 运行`cargo update`确保更新生效

## 特殊依赖处理

### Features配置

```toml
# 根目录定义基础features
tokio = { version = "1.45.0", features = ["full"] }

# 子项目可以引用并添加额外features
tokio = { workspace = true, features = ["rt-multi-thread"] }
```

### 可选依赖

```toml
# 根目录
optional-dep = { version = "1.0", optional = true }

# 子项目
optional-dep = { workspace = true, optional = true }
```

### 开发依赖

```toml
# 测试相关依赖也应在workspace中统一管理
[workspace.dependencies]
tempfile = "3.20.0"
rust_decimal_macros = "1.36"

# 子项目引用
[dev-dependencies]
tempfile.workspace = true
rust_decimal_macros.workspace = true
```

## 验证清单

新模块开发完成后，请确认：

- [ ] 子项目`Cargo.toml`中无直接版本号
- [ ] 所有依赖都在根目录workspace中声明
- [ ] 版本号使用`.workspace = true`
- [ ] `cargo check`通过
- [ ] `cargo test`通过

## 常见错误修复

### 错误1：版本冲突
```
error: failed to select a version for `dependency-name`
```

**解决**：检查是否有子项目独立声明了版本，移除并使用workspace引用

### 错误2：找不到依赖
```
error: package `dependency-name` not found
```

**解决**：在根目录`[workspace.dependencies]`中添加该依赖

### 错误3：Feature不兼容
```
error: feature `feature-name` is not available
```

**解决**：在根目录依赖声明中添加所需feature

## 未来扩展

当需要添加新的库模块时：

1. 在`workspace.members`中添加新路径
2. 在`workspace.dependencies`中添加新的内部依赖
3. 确保新模块遵循相同的依赖管理规范

这种方式确保了：
- 依赖版本的一致性
- 构建缓存的最大化利用
- 依赖管理的中心化
- 版本升级的简化 