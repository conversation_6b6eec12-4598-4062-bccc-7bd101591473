
use super::common::*;
use serde::{Deserialize, Serialize};
use crate::OrderSide;

/// 模拟交易结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeResult {
    pub success: bool,
    pub executed_price: f64,
    pub executed_quantity: f64,
    pub fees: f64,
    pub executed_at: chrono::DateTime<chrono::Utc>,
    pub error_message: Option<String>,
}


/// 成交记录
///
/// 表示订单执行后产生的成交信息。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Trade {
    /// 成交ID
    pub id: String,
    /// 关联订单ID
    pub order_id: String,
    /// 交易对
    pub symbol: Symbol,
    /// 交易方向
    pub side: OrderSide,
    /// 成交数量
    pub quantity: Quantity,
    /// 成交价格
    pub price: Price,
    /// 手续费
    pub fee: Option<Price>,
    /// 成交时间戳
    pub timestamp: Timestamp,
}

impl Trade {
    /// 创建新的成交记录
    pub fn new<S1: Into<String>, S2: Into<String>>(
        id: S1,
        order_id: S2,
        symbol: Symbol,
        side: OrderSide,
        quantity: Quantity,
        price: Price,
        timestamp: Timestamp,
    ) -> Self {
        Self {
            id: id.into(),
            order_id: order_id.into(),
            symbol,
            side,
            quantity,
            price,
            fee: None,
            timestamp,
        }
    }

    /// 设置手续费
    pub fn with_fee(mut self, fee: Price) -> Self {
        self.fee = Some(fee);
        self
    }

    /// 计算交易金额（不含手续费）
    pub fn notional_amount(&self) -> Price {
        self.quantity * self.price
    }

    /// 计算净交易金额（含手续费）
    pub fn net_amount(&self) -> Price {
        let notional = self.notional_amount();
        match self.fee {
            Some(fee) => notional - fee,
            None => notional,
        }
    }

    /// 判断成交记录是否有效
    pub fn is_valid(&self) -> bool {
        !self.id.is_empty()
            && !self.order_id.is_empty()
            && !self.symbol.is_empty()
            && self.quantity > Quantity::ZERO
            && self.price > Price::ZERO
            && self.timestamp > 0
    }
}
