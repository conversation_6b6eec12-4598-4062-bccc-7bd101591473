//! 空间定义模块
//! 
//! 定义观测空间和动作空间，描述环境与智能体之间的数据交换格式。

use rand::Rng;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use log::log;
pub use crate::ActionType;

/// 动作空间定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ActionSpace {
    /// 离散动作空间
    Discrete {
        /// 动作数量
        n: usize,
        /// 动作描述
        actions: Vec<ActionType>,
    },
    
    /// 连续动作空间
    Continuous {
        /// 动作维度
        shape: Vec<usize>,
        /// 最小值
        low: Vec<f64>,
        /// 最大值
        high: Vec<f64>,
    },
    
    /// 混合动作空间
    Mixed {
        /// 离散部分
        discrete: Box<ActionSpace>,
        /// 连续部分
        continuous: Box<ActionSpace>,
    },
    
    /// 多资产动作空间
    MultiAsset {
        /// 每个资产的动作空间
        assets: HashMap<String, ActionSpace>,
    },
}

impl ActionSpace {
    /// 创建离散动作空间
    pub fn discrete(actions: Vec<ActionType>) -> Self {
        let n = actions.len();
        Self::Discrete { n, actions }
    }
    
    /// 创建连续动作空间
    pub fn continuous(shape: Vec<usize>, low: Vec<f64>, high: Vec<f64>) -> Self {
        assert_eq!(shape.len(), low.len());
        assert_eq!(shape.len(), high.len());
        Self::Continuous { shape, low, high }
    }
    
    /// 创建简单的买入/卖出/持有动作空间
    pub fn simple_trading() -> Self {
        Self::discrete(vec![
            ActionType::Hold,
            ActionType::Buy,
            ActionType::Close,
        ])
    }
    
    /// 创建离散交易动作空间（推荐使用）
    pub fn discrete_trading() -> Self {
        Self::discrete(vec![
            ActionType::Hold,  // 0: 持有不动
            ActionType::Buy,   // 1: 买入（开仓/加仓，固定两单）
            ActionType::Sell,  // 2: 卖出（减仓，固定两单）
            ActionType::Close, // 3: 平仓（全部）
        ])
    }
    
    /// 创建带有仓位比例的连续动作空间
    pub fn continuous_position() -> Self {
        Self::continuous(
            vec![1],
            vec![-1.0], // -1表示全仓卖空
            vec![1.0],  // 1表示全仓买入
        )
    }
    
    /// 随机采样一个动作
    pub fn sample(&self) -> ActionType {
        let mut rng = rand::thread_rng();
        
        match self {
            ActionSpace::Discrete { actions, .. } => {
                let idx = rng.gen_range(0..actions.len());
                actions[idx].clone()
            }
            ActionSpace::Continuous { .. } => {
                // 为连续空间随机选择一个离散动作
                let actions = [
                    ActionType::Hold,
                    ActionType::Buy,
                    ActionType::Sell,
                    ActionType::Close,
                ];
                let idx = rng.gen_range(0..actions.len());
                actions[idx].clone()
            }
            ActionSpace::Mixed { discrete, .. } => {
                // 简化处理，优先使用离散部分
                discrete.sample()
            }
            ActionSpace::MultiAsset { assets } => {
                // 随机选择一个资产进行操作
                if let Some((_, action_space)) = assets.iter().next() {
                    action_space.sample()
                } else {
                    ActionType::Hold
                }
            }
        }
    }
    
    /// 检查动作是否有效
    pub fn is_valid(&self, action: &ActionType) -> bool {
        let result = match self {
            ActionSpace::Discrete { actions, .. } => {
                actions.contains(action)
            }
            ActionSpace::Continuous { .. } => {
                // 保留连续空间的验证逻辑以向后兼容
                match action {
                    ActionType::Buy | ActionType::Sell | ActionType::Close => {
                        // 新的离散动作在连续空间中也认为有效
                        true
                    }
                    ActionType::Hold => true,
                    ActionType::Complex { .. } => true, // 复杂动作暂时认为有效
                }
            }
            ActionSpace::Mixed { discrete, continuous } => {
                discrete.is_valid(action) || continuous.is_valid(action)
            }
            ActionSpace::MultiAsset { assets } => {
                assets.values().any(|space| space.is_valid(action))
            }
        };
        log::debug!("动作空间类型: {:?}, 动作: {:?}, 验证结果: {}", 
                   std::mem::discriminant(self), action, result);
        result
    }
    
    /// 获取动作空间的维度
    pub fn shape(&self) -> Vec<usize> {
        match self {
            ActionSpace::Discrete { n, .. } => vec![*n],
            ActionSpace::Continuous { shape, .. } => shape.clone(),
            ActionSpace::Mixed { discrete, continuous } => {
                let mut shape = discrete.shape();
                shape.extend(continuous.shape());
                shape
            }
            ActionSpace::MultiAsset { assets } => {
                vec![assets.len()]
            }
        }
    }
}

/// 观测空间定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ObservationSpace {
    /// 观测维度
    pub shape: Vec<usize>,
    /// 最小值
    pub low: Vec<f64>,
    /// 最大值
    pub high: Vec<f64>,
    /// 特征名称
    pub feature_names: Vec<String>,
    /// 特征类型
    pub feature_types: Vec<FeatureType>,
}

impl ObservationSpace {
    /// 创建新的观测空间
    pub fn new(
        shape: Vec<usize>,
        low: Vec<f64>,
        high: Vec<f64>,
        feature_names: Vec<String>,
    ) -> Self {
        let feature_types = vec![FeatureType::Continuous; feature_names.len()];
        Self {
            shape,
            low,
            high,
            feature_names,
            feature_types,
        }
    }
    
    /// 创建带有特征类型的观测空间
    pub fn with_types(
        shape: Vec<usize>,
        low: Vec<f64>,
        high: Vec<f64>,
        feature_names: Vec<String>,
        feature_types: Vec<FeatureType>,
    ) -> Self {
        Self {
            shape,
            low,
            high,
            feature_names,
            feature_types,
        }
    }
    
    /// 获取总特征数
    pub fn total_features(&self) -> usize {
        self.shape.iter().product()
    }
    
    /// 检查观测是否在有效范围内
    pub fn is_valid(&self, observation: &Observation) -> bool {
        let values = observation.to_vector();
        if values.len() != self.total_features() {
            return false;
        }
        
        for (i, &value) in values.iter().enumerate() {
            if value < self.low[i] || value > self.high[i] {
                return false;
            }
        }
        
        true
    }
    
    /// 标准化观测值
    pub fn normalize(&self, observation: &Observation) -> Observation {
        let mut values = observation.to_vector();
        
        for (i, value) in values.iter_mut().enumerate() {
            let range = self.high[i] - self.low[i];
            if range > 0.0 {
                *value = (*value - self.low[i]) / range;
            }
        }
        
        Observation::from_vector(values, observation.feature_names.clone())
    }
}

/// 特征类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FeatureType {
    /// 连续特征
    Continuous,
    /// 离散特征
    Discrete,
    /// 二进制特征
    Binary,
    /// 分类特征
    Categorical(Vec<String>),
}



/// 观测数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Observation {
    /// 市场特征
    pub market_features: Vec<f64>,
    
    /// 账户特征
    pub account_features: Vec<f64>,
    
    /// 持仓特征
    pub position_features: Vec<f64>,
    
    /// 技术指标特征
    pub technical_features: Vec<f64>,
    
    /// 时间特征
    pub time_features: Vec<f64>,
    
    /// 编码后的特征（用于复杂编码器如统计序列编码器的输出）
    pub encoded_features: Vec<f64>,
    
    /// 特征名称
    pub feature_names: Vec<String>,
    
    /// 时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
    
    /// 额外信息
    pub extra: HashMap<String, f64>,
}

impl Observation {
    /// 创建新的空观测
    pub fn new() -> Self {
        Self {
            market_features: Vec::new(),
            account_features: Vec::new(),
            position_features: Vec::new(),
            technical_features: Vec::new(),
            time_features: Vec::new(),
            encoded_features: Vec::new(),
            feature_names: Vec::new(),
            timestamp: chrono::Utc::now(),
            extra: HashMap::new(),
        }
    }
    
    /// 从特征向量创建观测
    pub fn from_features(features: Vec<f64>) -> Self {
        let feature_names = (0..features.len())
            .map(|i| format!("feature_{}", i))
            .collect();
            
        Self {
            market_features: features,
            account_features: Vec::new(),
            position_features: Vec::new(),
            technical_features: Vec::new(),
            time_features: Vec::new(),
            encoded_features: Vec::new(),
            feature_names,
            timestamp: chrono::Utc::now(),
            extra: HashMap::new(),
        }
    }
    
    /// 获取所有特征数据作为向量
    pub fn data(&self) -> Vec<f64> {
        self.to_vector()
    }
    
    /// 转换为特征向量
    pub fn to_vector(&self) -> Vec<f64> {
        // 如果有编码后的特征，优先使用编码后的特征
        if !self.encoded_features.is_empty() {
            return self.encoded_features.clone();
        }
        
        // 否则使用原始特征的组合
        let mut features = Vec::new();
        features.extend_from_slice(&self.market_features);
        features.extend_from_slice(&self.account_features);
        features.extend_from_slice(&self.position_features);
        features.extend_from_slice(&self.technical_features);
        features.extend_from_slice(&self.time_features);
        features
    }
    
    /// 从特征向量创建观测
    pub fn from_vector(values: Vec<f64>, feature_names: Vec<String>) -> Self {
        let mut observation = Self::new();
        
        // 检查feature_names是否表明这是编码后的特征
        let is_encoded = feature_names.iter().any(|name| name.starts_with("agg_feature_"));
        
        if is_encoded {
            // 如果是编码后的特征，存储到encoded_features字段
            observation.encoded_features = values;
        } else {
            // 否则存储到market_features字段（保持向后兼容）
            observation.market_features = values;
        }
        
        observation.feature_names = feature_names;
        observation
    }
    
    /// 获取特征总数
    pub fn total_features(&self) -> usize {
        // 如果有编码后的特征，返回编码后特征的数量
        if !self.encoded_features.is_empty() {
            return self.encoded_features.len();
        }
        
        // 否则返回原始特征的总数
        self.market_features.len() 
            + self.account_features.len() 
            + self.position_features.len() 
            + self.technical_features.len() 
            + self.time_features.len()
    }
    
    /// 添加市场特征
    pub fn add_market_feature(&mut self, name: String, value: f64) {
        self.market_features.push(value);
        self.feature_names.push(format!("market_{}", name));
    }
    
    /// 添加账户特征
    pub fn add_account_feature(&mut self, name: String, value: f64) {
        self.account_features.push(value);
        self.feature_names.push(format!("account_{}", name));
    }
    
    /// 添加持仓特征
    pub fn add_position_feature(&mut self, name: String, value: f64) {
        self.position_features.push(value);
        self.feature_names.push(format!("position_{}", name));
    }
    
    /// 添加技术指标特征
    pub fn add_technical_feature(&mut self, name: String, value: f64) {
        self.technical_features.push(value);
        self.feature_names.push(format!("technical_{}", name));
    }
    
    /// 添加时间特征
    pub fn add_time_feature(&mut self, name: String, value: f64) {
        self.time_features.push(value);
        self.feature_names.push(format!("time_{}", name));
    }
    
    /// 设置编码后的特征（用于复杂编码器的输出）
    pub fn set_encoded_features(&mut self, features: Vec<f64>, feature_names: Vec<String>) {
        self.encoded_features = features;
        self.feature_names = feature_names;
    }
    
    /// 检查是否使用了编码后的特征
    pub fn is_encoded(&self) -> bool {
        !self.encoded_features.is_empty()
    }
    
    /// 归一化所有特征值到[0, 1]范围
    pub fn normalize(&mut self) {
        Self::normalize_features_static(&mut self.market_features);
        Self::normalize_features_static(&mut self.account_features);
        Self::normalize_features_static(&mut self.position_features);
        Self::normalize_features_static(&mut self.technical_features);
        Self::normalize_features_static(&mut self.time_features);
    }
    
    /// 创建归一化的观测副本（用于训练流程，不修改原始观测）
    pub fn create_normalized_copy(&self, observation_space: &ObservationSpace) -> Self {
        let values = self.to_vector();
        let normalized_values = Self::safe_normalize_vector(&values, observation_space);
        Self::from_vector(normalized_values, self.feature_names.clone())
    }
    
    /// 安全的向量归一化方法，处理边界情况
    fn safe_normalize_vector(values: &[f64], observation_space: &ObservationSpace) -> Vec<f64> {
        let mut normalized = Vec::with_capacity(values.len());
        
        for (i, &value) in values.iter().enumerate() {
            if i >= observation_space.low.len() || i >= observation_space.high.len() {
                // 如果超出边界定义范围，保持原值
                log::warn!("特征索引{}超出观测空间边界定义，保持原值: {}", i, value);
                normalized.push(value);
                continue;
            }
            
            let low = observation_space.low[i];
            let high = observation_space.high[i];
            let range = high - low;
            
            // 处理边界情况
            if range <= 0.0 || !range.is_finite() {
                // 如果范围无效，使用简单的裁剪策略
                let normalized_value = if value.is_finite() {
                    value.clamp(-1.0, 1.0)
                } else {
                    0.0
                };
                normalized.push(normalized_value);
                log::debug!("特征{}范围无效(low:{}, high:{}), 使用裁剪策略: {} -> {}", 
                           i, low, high, value, normalized_value);
            } else {
                // 标准min-max归一化到[0,1]范围
                let normalized_value = if value.is_finite() {
                    ((value - low) / range).clamp(0.0, 1.0)
                } else {
                    0.5 // 对于非有限值，使用中间值
                };
                normalized.push(normalized_value);
                log::debug!("特征{}标准化: {} -> {} (范围: [{}, {}])", 
                           i, value, normalized_value, low, high);
            }
        }
        
        normalized
    }
    
    /// 归一化特征向量
    fn normalize_features_static(features: &mut Vec<f64>) {
        if features.is_empty() {
            return;
        }
        
        let min_val = features.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_val = features.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        
        if max_val != min_val {
            for feature in features.iter_mut() {
                *feature = (*feature - min_val) / (max_val - min_val);
            }
        }
    }
}
