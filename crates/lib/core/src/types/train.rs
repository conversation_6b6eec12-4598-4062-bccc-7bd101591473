
use super::common::*;
use serde::{Deserialize, Serialize};

/// 训练结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TrainingResult {
    /// 总训练步数
    pub total_steps: usize,
    /// 总训练回合数
    pub total_episodes: usize,
    /// 平均奖励
    pub average_reward: f64,
    /// 最佳回合奖励
    pub best_episode_reward: f64,
    /// 训练时间（秒）
    pub training_duration: f64,
    /// 最终损失值
    pub final_critic_loss: f64,
    pub final_actor_loss: f64,
    pub episode_rewards: Vec<f64>,
    pub episode_lengths: Vec<usize>,
}