use std::collections::VecDeque;
use serde::{Deserialize, Serialize};
use crate::{ActionType, Tick};
use crate::space::Observation;
use crate::trade::TradeResult;

/// 环境配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvConfig {
    /// 初始资金
    pub initial_balance: i64,

    /// 最大步数
    pub max_steps: Option<usize>,

    /// 是否记录历史
    pub record_history: bool,

    /// 历史记录容量
    pub history_capacity: usize,

    /// 随机种子
    pub seed: Option<u64>,

    /// 最大可接受回撤百分比 (例如: 0.2 表示 20%)
    pub max_drawdown_percent: f64,
}

impl Default for EnvConfig {
    fn default() -> Self {
        Self {
            initial_balance: 10000,
            max_steps: Some(10000),
            record_history: true,
            history_capacity: 1000,
            seed: None,
            max_drawdown_percent: 0.2,
        }
    }
}

/// 环境状态
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EnvState {
    /// 当前时间步
    pub current_step: usize,

    /// 当前市场数据
    pub current_market_data: Option<Tick>,

    /// 上一次的账户状态
    pub previous_account_value: f64,

    /// 最高账户价值
    pub peak_value: f64,

    /// 连续亏损次数
    pub consecutive_losses: usize,

    /// 是否结束
    pub done: bool,

    /// 结束原因
    pub termination_reason: Option<String>,
}

impl Default for EnvState {
    fn default() -> Self {
        Self {
            current_step: 0,
            current_market_data: None,
            previous_account_value: 0.0,
            peak_value: 0.0,
            consecutive_losses: 0,
            done: false,
            termination_reason: None,
        }
    }
}

/// 环境历史记录
#[derive(Debug, Clone)]
pub struct EnvHistory {
    /// 动作历史
    pub actions: VecDeque<ActionType>,

    /// 奖励历史
    pub rewards: VecDeque<f64>,

    /// 观测历史
    pub observations: VecDeque<Observation>,

    /// 账户价值历史
    pub account_values: VecDeque<f64>,

    /// 交易历史
    pub trades: VecDeque<TradeResult>,

    /// 容量限制
    capacity: usize,
}

impl EnvHistory {
    /// 创建新的历史记录
    pub fn new(capacity: usize) -> Self {
        Self {
            actions: VecDeque::with_capacity(capacity),
            rewards: VecDeque::with_capacity(capacity),
            observations: VecDeque::with_capacity(capacity),
            account_values: VecDeque::with_capacity(capacity),
            trades: VecDeque::with_capacity(capacity),
            capacity,
        }
    }

    /// 添加记录
    pub fn add_record(&mut self, action: ActionType, reward: f64, observation: Observation, account_value: f64) {
        if self.actions.len() >= self.capacity {
            self.actions.pop_front();
            self.rewards.pop_front();
            self.observations.pop_front();
            self.account_values.pop_front();
        }

        self.actions.push_back(action);
        self.rewards.push_back(reward);
        self.observations.push_back(observation);
        self.account_values.push_back(account_value);
    }

    /// 添加交易记录
    pub fn add_trade(&mut self, trade: TradeResult) {
        if self.trades.len() >= self.capacity {
            self.trades.pop_front();
        }
        self.trades.push_back(trade);
    }

    /// 清空历史
    pub fn clear(&mut self) {
        self.actions.clear();
        self.rewards.clear();
        self.observations.clear();
        self.account_values.clear();
        self.trades.clear();
    }
}
