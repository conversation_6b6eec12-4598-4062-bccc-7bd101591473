//! # 动作类型定义
//! 
//! 定义强化学习智能体可以执行的动作类型

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 动作类型枚举
///
/// 定义智能体在交易环境中可以执行的所有动作类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ActionType {
    /// 持有（不操作）
    Hold,

    /// 买入（无持仓时开仓，有持仓时加仓，固定两单）
    Buy,

    /// 卖出（减仓，固定两单）
    Sell,

    /// 平仓（平掉所有数量）
    Close,

    /// 连续仓位动作（新增：支持仓位比例控制）
    ///
    /// position_ratio: 目标仓位比例
    /// - 0.0: 空仓
    /// - 1.0: 满仓做多
    /// - -1.0: 满仓做空
    /// - 0.5: 半仓做多
    ContinuousPosition {
        /// 目标仓位比例 [-1.0, 1.0]
        position_ratio: f64,
    },

    /// 复杂动作（多资产）- 保留以兼容现有代码
    Complex {
        /// 资产符号
        symbol: String,
        /// 动作类型
        action: Box<ActionType>,
        /// 额外参数
        params: HashMap<String, f64>,
    },
}

impl ActionType {
    /// 获取动作的权重/比例（为兼容性保留）
    pub fn weight(&self) -> f64 {
        match self {
            ActionType::Hold => 0.0,
            ActionType::Buy => 1.0,
            ActionType::Sell => -0.5,
            ActionType::Close => -1.0,
            ActionType::ContinuousPosition { position_ratio } => *position_ratio,
            ActionType::Complex { action, .. } => action.weight(),
        }
    }

    /// 获取目标仓位比例（新增方法）
    pub fn position_ratio(&self) -> f64 {
        match self {
            ActionType::Hold => 0.0,
            ActionType::Buy => 1.0,
            ActionType::Sell => -0.5,
            ActionType::Close => 0.0,
            ActionType::ContinuousPosition { position_ratio } => *position_ratio,
            ActionType::Complex { action, .. } => action.position_ratio(),
        }
    }

    /// 检查是否为连续仓位动作
    pub fn is_continuous_position(&self) -> bool {
        matches!(self, ActionType::ContinuousPosition { .. })
    }
    
    /// 检查是否为买入类动作
    pub fn is_buy(&self) -> bool {
        matches!(self, ActionType::Buy)
    }
    
    /// 检查是否为卖出类动作
    pub fn is_sell(&self) -> bool {
        matches!(self, ActionType::Sell | ActionType::Close)
    }
    
    /// 检查是否为持有动作
    pub fn is_hold(&self) -> bool {
        matches!(self, ActionType::Hold)
    }
    
    /// 检查是否为平仓动作
    pub fn is_close(&self) -> bool {
        matches!(self, ActionType::Close)
    }
    
    /// 检查是否需要执行交易
    pub fn requires_trading(&self) -> bool {
        !matches!(self, ActionType::Hold | ActionType::Complex { .. })
    }
    
    /// 获取动作描述
    pub fn description(&self) -> &'static str {
        match self {
            ActionType::Hold => "持有",
            ActionType::Buy => "买入",
            ActionType::Sell => "卖出",
            ActionType::Close => "平仓",
            ActionType::Complex { .. } => "复杂动作",
        }
    }
    
    /// 检查动作是否有效
    pub fn is_valid(&self) -> bool {
        match self {
            ActionType::Complex { action, .. } => action.is_valid(),
            _ => true,
        }
    }
}

impl Default for ActionType {
    fn default() -> Self {
        ActionType::Hold
    }
}

impl std::fmt::Display for ActionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.description())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_action_weights() {
        assert_eq!(ActionType::Hold.weight(), 0.0);
        assert_eq!(ActionType::Buy.weight(), 1.0);
        assert_eq!(ActionType::Sell.weight(), -0.5);
        assert_eq!(ActionType::Close.weight(), -1.0);
    }

    #[test]
    fn test_action_types() {
        assert!(ActionType::Buy.is_buy());
        assert!(ActionType::Sell.is_sell());
        assert!(ActionType::Close.is_sell());
        assert!(ActionType::Close.is_close());
        assert!(ActionType::Hold.is_hold());
    }

    #[test]
    fn test_requires_trading() {
        assert!(!ActionType::Hold.requires_trading());
        assert!(ActionType::Buy.requires_trading());
        assert!(ActionType::Sell.requires_trading());
        assert!(ActionType::Close.requires_trading());
    }

    #[test]
    fn test_action_validity() {
        assert!(ActionType::Hold.is_valid());
        assert!(ActionType::Buy.is_valid());
        assert!(ActionType::Sell.is_valid());
        assert!(ActionType::Close.is_valid());
    }
} 