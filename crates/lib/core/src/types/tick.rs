
use super::common::*;
use serde::{Deserialize, Serialize};


/// OHLCV K线数据
/// 
/// 表示某个时间周期内的开盘价、最高价、最低价、收盘价和成交量。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Tick {
    /// 交易对标识
    pub symbol: Symbol,
    /// 时间戳（通常是周期开始时间）
    pub timestamp: Timestamp,
    /// 开盘价
    pub open: f64,
    /// 最高价
    pub high: f64,
    /// 最低价
    pub low: f64,
    /// 收盘价
    pub close: f64,
    /// 成交量
    pub volume: f64,
}

impl Tick {
    /// 创建新的 OHLCV 数据
    pub fn new(
        symbol: Symbol,
        timestamp: Timestamp,
        open: f64,
        high: f64,
        low: f64,
        close: f64,
        volume: f64,
    ) -> Self {
        Self {
            symbol,
            timestamp,
            open,
            high,
            low,
            close,
            volume,
        }
    }
}
