//! 基础通用类型定义
//! 
//! 定义与业务领域无关的通用类型，如价格、数量、时间戳等。

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use std::fmt;

/// 价格类型，使用定点数以保证精度
/// 
/// 在金融计算中，使用 `rust_decimal::Decimal` 而不是 `f64` 
/// 可以避免浮点数精度问题。
pub type Price = Decimal;

/// 数量类型，使用定点数
/// 
/// 表示交易数量、持仓数量等，确保计算精度。
pub type Quantity = Decimal;

/// 时间戳类型 (Unix epoch, milliseconds)
/// 
/// 使用毫秒级精度的 Unix 时间戳表示时间。
pub type Timestamp = i64;

/// 交易对标识
/// 
/// 用于标识不同的交易对，如 "BTC/USDT", "ETH/BTC" 等。
#[derive(Debug, <PERSON><PERSON>, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct Symbol(pub String);

impl Symbol {
    /// 创建新的交易对标识
    pub fn new<S: Into<String>>(symbol: S) -> Self {
        Self(symbol.into())
    }

    /// 获取交易对字符串
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.0.is_empty()
    }
}

impl fmt::Display for Symbol {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<String> for Symbol {
    fn from(s: String) -> Self {
        Self(s)
    }
}

impl From<&str> for Symbol {
    fn from(s: &str) -> Self {
        Self(s.to_string())
    }
}

impl AsRef<str> for Symbol {
    fn as_ref(&self) -> &str {
        &self.0
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_symbol_creation() {
        let symbol1 = Symbol::new("BTC/USDT");
        let symbol2 = Symbol::from("ETH/BTC");
        let symbol3: Symbol = "ADA/USDT".into();

        assert_eq!(symbol1.as_str(), "BTC/USDT");
        assert_eq!(symbol2.as_str(), "ETH/BTC");
        assert_eq!(symbol3.as_str(), "ADA/USDT");
    }

    #[test]
    fn test_symbol_display() {
        let symbol = Symbol::new("BTC/USDT");
        assert_eq!(format!("{}", symbol), "BTC/USDT");
    }

    #[test]
    fn test_symbol_hash() {
        use std::collections::HashMap;
        
        let mut map = HashMap::new();
        let symbol = Symbol::new("BTC/USDT");
        map.insert(symbol.clone(), 100);
        
        assert_eq!(map.get(&symbol), Some(&100));
    }
} 