//! 交易执行相关类型定义
//! 
//! 定义与交易执行相关的结构体，如订单、成交记录、持仓等。

use super::common::*;
use serde::{Deserialize, Serialize};
use crate::trade::Trade;

/// 订单方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderSide {
    /// 买入
    Buy,
    /// 卖出
    Sell,
}

impl OrderSide {
    /// 获取相反方向
    pub fn opposite(&self) -> Self {
        match self {
            Self::Buy => Self::Sell,
            Self::Sell => Self::Buy,
        }
    }

    /// 判断是否为买入
    pub fn is_buy(&self) -> bool {
        matches!(self, Self::Buy)
    }

    /// 判断是否为卖出
    pub fn is_sell(&self) -> bool {
        matches!(self, Self::Sell)
    }
}

/// 订单状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderStatus {
    /// 新建订单
    New,
    /// 已提交
    Submitted,
    /// 部分成交
    PartiallyFilled,
    /// 完全成交
    Filled,
    /// 已取消
    Canceled,
    /// 已拒绝
    Rejected,
    /// 已过期
    Expired,
}

impl OrderStatus {
    /// 判断是否为终态（不会再变化的状态）
    pub fn is_terminal(&self) -> bool {
        matches!(
            self,
            Self::Filled | Self::Canceled | Self::Rejected | Self::Expired
        )
    }

    /// 判断是否为活跃状态（可能继续变化）
    pub fn is_active(&self) -> bool {
        matches!(self, Self::New | Self::Submitted | Self::PartiallyFilled)
    }

    /// 判断是否有成交
    pub fn has_fills(&self) -> bool {
        matches!(self, Self::PartiallyFilled | Self::Filled)
    }
}

/// 订单类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderType {
    /// 市价单
    Market,
    /// 限价单
    Limit,
    /// 止损单
    Stop,
    /// 止损限价单
    StopLimit,
}

/// 订单结构体
/// 
/// 表示一个交易订单的完整信息。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Order {
    /// 订单ID
    pub id: String,
    /// 交易对
    pub symbol: Symbol,
    /// 订单方向
    pub side: OrderSide,
    /// 订单类型
    pub order_type: OrderType,
    /// 订单数量
    pub quantity: Quantity,
    /// 订单价格（市价单为 None）
    pub price: Option<Price>,
    /// 订单状态
    pub status: OrderStatus,
    /// 已成交数量
    pub filled_quantity: Quantity,
    /// 平均成交价格
    pub avg_fill_price: Option<Price>,
    /// 创建时间戳
    pub created_at: Timestamp,
    /// 更新时间戳
    pub updated_at: Timestamp,
}

impl Order {
    /// 创建新订单
    pub fn new<S: Into<String>>(
        id: S,
        symbol: Symbol,
        side: OrderSide,
        order_type: OrderType,
        quantity: Quantity,
        price: Option<Price>,
        timestamp: Timestamp,
    ) -> Self {
        Self {
            id: id.into(),
            symbol,
            side,
            order_type,
            quantity,
            price,
            status: OrderStatus::New,
            filled_quantity: Quantity::ZERO,
            avg_fill_price: None,
            created_at: timestamp,
            updated_at: timestamp,
        }
    }

    /// 创建市价买单
    pub fn market_buy<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(id, symbol, OrderSide::Buy, OrderType::Market, quantity, None, timestamp)
    }

    /// 创建市价卖单
    pub fn market_sell<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(id, symbol, OrderSide::Sell, OrderType::Market, quantity, None, timestamp)
    }

    /// 创建限价买单
    pub fn limit_buy<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        price: Price,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(
            id,
            symbol,
            OrderSide::Buy,
            OrderType::Limit,
            quantity,
            Some(price),
            timestamp,
        )
    }

    /// 创建限价卖单
    pub fn limit_sell<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        price: Price,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(
            id,
            symbol,
            OrderSide::Sell,
            OrderType::Limit,
            quantity,
            Some(price),
            timestamp,
        )
    }

    /// 获取剩余未成交数量
    pub fn remaining_quantity(&self) -> Quantity {
        self.quantity - self.filled_quantity
    }

    /// 计算成交百分比
    pub fn fill_percentage(&self) -> Price {
        if self.quantity == Quantity::ZERO {
            Price::ZERO
        } else {
            self.filled_quantity / self.quantity * Price::from(100)
        }
    }

    /// 判断订单是否有效
    pub fn is_valid(&self) -> bool {
        !self.id.is_empty()
            && !self.symbol.is_empty()
            && self.quantity > Quantity::ZERO
            && self.filled_quantity <= self.quantity
            && self.created_at > 0
            && self.updated_at >= self.created_at
            && (self.order_type == OrderType::Market || self.price.is_some())
    }
}

/// 持仓状态
/// 
/// 表示某个交易对的当前持仓情况。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    /// 交易对
    pub symbol: Symbol,
    /// 持仓数量（正数为多头，负数为空头，零为无持仓）
    pub quantity: Quantity,
    /// 平均开仓价格
    pub average_entry_price: Price,
    /// 最后更新时间戳
    pub updated_at: Timestamp,
}

impl Position {
    /// 创建新的持仓
    pub fn new(symbol: Symbol, quantity: Quantity, entry_price: Price, timestamp: Timestamp) -> Self {
        Self {
            symbol,
            quantity,
            average_entry_price: entry_price,
            updated_at: timestamp,
        }
    }

    /// 创建空持仓
    pub fn empty(symbol: Symbol, timestamp: Timestamp) -> Self {
        Self {
            symbol,
            quantity: Quantity::ZERO,
            average_entry_price: Price::ZERO,
            updated_at: timestamp,
        }
    }

    /// 判断是否为多头持仓
    pub fn is_long(&self) -> bool {
        self.quantity > Quantity::ZERO
    }

    /// 判断是否为空头持仓
    pub fn is_short(&self) -> bool {
        self.quantity < Quantity::ZERO
    }

    /// 判断是否为空仓
    pub fn is_flat(&self) -> bool {
        self.quantity == Quantity::ZERO
    }

    /// 获取持仓绝对数量
    pub fn abs_quantity(&self) -> Quantity {
        if self.quantity < Quantity::ZERO {
            -self.quantity
        } else {
            self.quantity
        }
    }

    /// 计算当前市值
    pub fn market_value(&self, current_price: Price) -> Price {
        self.quantity * current_price
    }

    /// 计算未实现盈亏
    pub fn unrealized_pnl(&self, current_price: Price) -> Price {
        if self.quantity == Quantity::ZERO {
            Price::ZERO
        } else {
            self.quantity * (current_price - self.average_entry_price)
        }
    }

    /// 计算未实现盈亏百分比
    pub fn unrealized_pnl_pct(&self, current_price: Price) -> Price {
        if self.average_entry_price == Price::ZERO {
            Price::ZERO
        } else {
            (current_price - self.average_entry_price) / self.average_entry_price * Price::from(100)
        }
    }

    /// 更新持仓（根据新的交易）
    pub fn update_with_trade(&mut self, trade: &Trade) {
        if trade.symbol != self.symbol {
            return;
        }

        let trade_quantity = match trade.side {
            OrderSide::Buy => trade.quantity,
            OrderSide::Sell => -trade.quantity,
        };

        let new_quantity = self.quantity + trade_quantity;

        // 如果持仓方向发生变化或从零持仓开始，更新平均价格
        if (self.quantity >= Quantity::ZERO && new_quantity >= Quantity::ZERO && trade_quantity > Quantity::ZERO)
            || (self.quantity <= Quantity::ZERO && new_quantity <= Quantity::ZERO && trade_quantity < Quantity::ZERO)
        {
            // 同方向增仓，计算新的平均价格
            if self.quantity != Quantity::ZERO {
                let total_cost = self.quantity * self.average_entry_price + trade.quantity * trade.price;
                self.average_entry_price = total_cost / new_quantity.abs();
            } else {
                // 从零持仓开始
                self.average_entry_price = trade.price;
            }
        }
        // 如果是减仓或平仓，保持原平均价格不变
        self.quantity = new_quantity;
        self.updated_at = trade.timestamp;

        // 如果完全平仓，重置平均价格
        if self.quantity == Quantity::ZERO {
            self.average_entry_price = Price::ZERO;
        }
    }
}