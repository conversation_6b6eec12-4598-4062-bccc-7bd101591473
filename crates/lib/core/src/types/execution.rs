//! 交易执行相关类型定义
//! 
//! 定义与交易执行相关的结构体，如订单、成交记录、持仓等。

use super::common::*;
use serde::{Deserialize, Serialize};
use crate::trade::Trade;
use rust_decimal::prelude::{Signed, ToPrimitive};

/// 订单方向
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderSide {
    /// 买入
    Buy,
    /// 卖出
    Sell,
}

impl OrderSide {
    /// 获取相反方向
    pub fn opposite(&self) -> Self {
        match self {
            Self::Buy => Self::Sell,
            Self::Sell => Self::Buy,
        }
    }

    /// 判断是否为买入
    pub fn is_buy(&self) -> bool {
        matches!(self, Self::Buy)
    }

    /// 判断是否为卖出
    pub fn is_sell(&self) -> bool {
        matches!(self, Self::Sell)
    }
}

/// 订单状态
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderStatus {
    /// 新建订单
    New,
    /// 已提交
    Submitted,
    /// 部分成交
    PartiallyFilled,
    /// 完全成交
    Filled,
    /// 已取消
    Canceled,
    /// 已拒绝
    Rejected,
    /// 已过期
    Expired,
}

impl OrderStatus {
    /// 判断是否为终态（不会再变化的状态）
    pub fn is_terminal(&self) -> bool {
        matches!(
            self,
            Self::Filled | Self::Canceled | Self::Rejected | Self::Expired
        )
    }

    /// 判断是否为活跃状态（可能继续变化）
    pub fn is_active(&self) -> bool {
        matches!(self, Self::New | Self::Submitted | Self::PartiallyFilled)
    }

    /// 判断是否有成交
    pub fn has_fills(&self) -> bool {
        matches!(self, Self::PartiallyFilled | Self::Filled)
    }
}

/// 订单类型
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum OrderType {
    /// 市价单
    Market,
    /// 限价单
    Limit,
    /// 止损单
    Stop,
    /// 止损限价单
    StopLimit,
}

/// 订单结构体
/// 
/// 表示一个交易订单的完整信息。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Order {
    /// 订单ID
    pub id: String,
    /// 交易对
    pub symbol: Symbol,
    /// 订单方向
    pub side: OrderSide,
    /// 订单类型
    pub order_type: OrderType,
    /// 订单数量
    pub quantity: Quantity,
    /// 订单价格（市价单为 None）
    pub price: Option<Price>,
    /// 订单状态
    pub status: OrderStatus,
    /// 已成交数量
    pub filled_quantity: Quantity,
    /// 平均成交价格
    pub avg_fill_price: Option<Price>,
    /// 创建时间戳
    pub created_at: Timestamp,
    /// 更新时间戳
    pub updated_at: Timestamp,
}

impl Order {
    /// 创建新订单
    pub fn new<S: Into<String>>(
        id: S,
        symbol: Symbol,
        side: OrderSide,
        order_type: OrderType,
        quantity: Quantity,
        price: Option<Price>,
        timestamp: Timestamp,
    ) -> Self {
        Self {
            id: id.into(),
            symbol,
            side,
            order_type,
            quantity,
            price,
            status: OrderStatus::New,
            filled_quantity: Quantity::ZERO,
            avg_fill_price: None,
            created_at: timestamp,
            updated_at: timestamp,
        }
    }

    /// 创建市价买单
    pub fn market_buy<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(id, symbol, OrderSide::Buy, OrderType::Market, quantity, None, timestamp)
    }

    /// 创建市价卖单
    pub fn market_sell<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(id, symbol, OrderSide::Sell, OrderType::Market, quantity, None, timestamp)
    }

    /// 创建限价买单
    pub fn limit_buy<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        price: Price,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(
            id,
            symbol,
            OrderSide::Buy,
            OrderType::Limit,
            quantity,
            Some(price),
            timestamp,
        )
    }

    /// 创建限价卖单
    pub fn limit_sell<S: Into<String>>(
        id: S,
        symbol: Symbol,
        quantity: Quantity,
        price: Price,
        timestamp: Timestamp,
    ) -> Self {
        Self::new(
            id,
            symbol,
            OrderSide::Sell,
            OrderType::Limit,
            quantity,
            Some(price),
            timestamp,
        )
    }

    /// 获取剩余未成交数量
    pub fn remaining_quantity(&self) -> Quantity {
        self.quantity - self.filled_quantity
    }

    /// 计算成交百分比
    pub fn fill_percentage(&self) -> Price {
        if self.quantity == Quantity::ZERO {
            Price::ZERO
        } else {
            self.filled_quantity / self.quantity * Price::from(100)
        }
    }

    /// 判断订单是否有效
    pub fn is_valid(&self) -> bool {
        !self.id.is_empty()
            && !self.symbol.is_empty()
            && self.quantity > Quantity::ZERO
            && self.filled_quantity <= self.quantity
            && self.created_at > 0
            && self.updated_at >= self.created_at
            && (self.order_type == OrderType::Market || self.price.is_some())
    }
}

/// 持仓状态
/// 
/// 表示某个交易对的当前持仓情况。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    /// 交易对
    pub symbol: Symbol,
    /// 持仓数量（正数为多头，负数为空头，零为无持仓）
    pub quantity: Quantity,
    /// 平均开仓价格
    pub average_entry_price: Price,
    /// 最后更新时间戳
    pub updated_at: Timestamp,
}

impl Position {
    /// 创建新的持仓
    pub fn new(symbol: Symbol, quantity: Quantity, entry_price: Price, timestamp: Timestamp) -> Self {
        Self {
            symbol,
            quantity,
            average_entry_price: entry_price,
            updated_at: timestamp,
        }
    }

    /// 创建空持仓
    pub fn empty(symbol: Symbol, timestamp: Timestamp) -> Self {
        Self {
            symbol,
            quantity: Quantity::ZERO,
            average_entry_price: Price::ZERO,
            updated_at: timestamp,
        }
    }

    /// 判断是否为多头持仓
    pub fn is_long(&self) -> bool {
        self.quantity > Quantity::ZERO
    }

    /// 判断是否为空头持仓
    pub fn is_short(&self) -> bool {
        self.quantity < Quantity::ZERO
    }

    /// 判断是否为空仓
    pub fn is_flat(&self) -> bool {
        self.quantity == Quantity::ZERO
    }

    /// 获取持仓绝对数量
    pub fn abs_quantity(&self) -> Quantity {
        if self.quantity < Quantity::ZERO {
            -self.quantity
        } else {
            self.quantity
        }
    }

    /// 计算当前市值
    pub fn market_value(&self, current_price: Price) -> Price {
        self.quantity * current_price
    }

    /// 计算未实现盈亏
    pub fn unrealized_pnl(&self, current_price: Price) -> Price {
        if self.quantity == Quantity::ZERO {
            Price::ZERO
        } else {
            self.quantity * (current_price - self.average_entry_price)
        }
    }

    /// 计算未实现盈亏百分比
    pub fn unrealized_pnl_pct(&self, current_price: Price) -> Price {
        if self.average_entry_price == Price::ZERO {
            Price::ZERO
        } else {
            (current_price - self.average_entry_price) / self.average_entry_price * Price::from(100)
        }
    }

    /// 根据交易更新持仓（重构版本，支持准确的多空计算）
    ///
    /// 处理开仓、加仓、减仓、平仓、多空转换等各种情况，正确计算平均价格和已实现盈亏。
    ///
    /// # 返回
    /// 返回此次交易产生的已实现盈亏
    pub fn update_with_trade(&mut self, trade: &Trade) -> Price {
        if trade.symbol != self.symbol {
            return Price::ZERO;
        }

        let trade_quantity = match trade.side {
            OrderSide::Buy => trade.quantity,
            OrderSide::Sell => -trade.quantity,
        };

        let old_quantity = self.quantity;
        let new_quantity = old_quantity + trade_quantity;
        let mut realized_pnl = Price::ZERO;

        // 分情况处理持仓更新
        if old_quantity == Quantity::ZERO {
            // 情况1: 从空仓开始建仓
            self.quantity = new_quantity;
            self.average_entry_price = trade.price;
            log::debug!("开仓: {} @ {}, 新持仓: {}", trade_quantity, trade.price, new_quantity);
        } else if old_quantity.signum() == trade_quantity.signum() {
            // 情况2: 同方向加仓
            let old_cost = old_quantity.abs() * self.average_entry_price;
            let new_cost = trade_quantity.abs() * trade.price;
            let total_cost = old_cost + new_cost;
            self.quantity = new_quantity;

            // 修复：确保均价始终为正数
            if new_quantity != Quantity::ZERO {
                self.average_entry_price = total_cost / new_quantity.abs();
            } else {
                self.average_entry_price = Price::ZERO;
            }

            log::debug!("加仓: {} @ {}, 新持仓: {}, 新均价: {}",
                       trade_quantity, trade.price, new_quantity, self.average_entry_price);
        } else {
            // 情况3: 反方向交易（减仓、平仓或多空转换）
            let close_quantity = if old_quantity.abs() >= trade_quantity.abs() {
                // 部分或完全平仓
                trade_quantity
            } else {
                // 多空转换：先平掉原持仓，再开新仓
                -old_quantity
            };

            // 计算平仓部分的已实现盈亏
            if close_quantity != Quantity::ZERO {
                realized_pnl = self.calculate_realized_pnl(close_quantity.abs(), trade.price);
                log::debug!("平仓: {} @ {}, 已实现盈亏: {}", close_quantity, trade.price, realized_pnl);
            }

            // 更新持仓
            self.quantity = new_quantity;

            if new_quantity == Quantity::ZERO {
                // 完全平仓
                self.average_entry_price = Price::ZERO;
                log::debug!("完全平仓，持仓清零");
            } else if old_quantity.signum() != new_quantity.signum() {
                // 多空转换：新持仓的均价就是当前交易价格
                self.average_entry_price = trade.price;
                log::debug!("多空转换: 新持仓: {} @ {}", new_quantity, trade.price);
            }
            // 如果是同方向减仓，保持原均价不变
        }

        self.updated_at = trade.timestamp;
        realized_pnl
    }

    /// 计算平仓的已实现盈亏
    ///
    /// # 参数
    /// * `close_quantity` - 平仓数量（绝对值）
    /// * `close_price` - 平仓价格
    fn calculate_realized_pnl(&self, close_quantity: Quantity, close_price: Price) -> Price {
        if self.quantity == Quantity::ZERO || close_quantity == Quantity::ZERO {
            return Price::ZERO;
        }

        let price_diff = close_price - self.average_entry_price;
        let pnl = if self.quantity > Quantity::ZERO {
            // 多头持仓：价格上涨盈利
            close_quantity * price_diff
        } else {
            // 空头持仓：价格下跌盈利
            -(close_quantity * price_diff)
        };

        pnl
    }

    /// 计算达到目标仓位比例需要的交易量
    ///
    /// # 参数
    /// * `target_ratio` - 目标仓位比例 [-1.0, 1.0]
    /// * `account_equity` - 账户总权益
    /// * `current_price` - 当前价格
    ///
    /// # 返回
    /// 需要交易的数量（正数为买入，负数为卖出）
    pub fn calculate_trade_quantity_for_ratio(
        &self,
        target_ratio: f64,
        account_equity: Price,
        current_price: Price,
    ) -> Quantity {
        if current_price <= Price::ZERO || account_equity <= Price::ZERO {
            return Quantity::ZERO;
        }

        // 计算目标持仓数量
        let target_value = account_equity * Quantity::try_from(target_ratio.abs()).unwrap_or(Quantity::ZERO);
        let target_quantity = if target_ratio >= 0.0 {
            // 做多：正数量
            target_value / current_price
        } else {
            // 做空：负数量
            -(target_value / current_price)
        };

        // 计算需要交易的数量
        let trade_quantity = target_quantity - self.quantity;

        log::debug!(
            "仓位比例计算: 目标比例={:.2}%, 当前持仓={}, 目标持仓={}, 需要交易={}",
            target_ratio * 100.0, self.quantity, target_quantity, trade_quantity
        );

        trade_quantity
    }

    /// 获取当前仓位比例
    ///
    /// # 参数
    /// * `account_equity` - 账户总权益
    /// * `current_price` - 当前价格
    ///
    /// # 返回
    /// 当前仓位比例 [-1.0, 1.0]
    pub fn get_position_ratio(&self, account_equity: Price, current_price: Price) -> f64 {
        if account_equity <= Price::ZERO || current_price <= Price::ZERO {
            return 0.0;
        }

        let position_value = self.quantity * current_price;
        let ratio = (position_value / account_equity).to_f64().unwrap_or(0.0);

        // 确保返回值在合理范围内
        ratio.clamp(-1.0, 1.0)
    }

    /// 获取持仓方向描述
    pub fn position_direction(&self) -> &'static str {
        if self.quantity > Quantity::ZERO {
            "多头"
        } else if self.quantity < Quantity::ZERO {
            "空头"
        } else {
            "空仓"
        }
    }

    /// 检查是否需要交易（基于最小变化阈值）
    ///
    /// # 参数
    /// * `trade_quantity` - 计划交易数量
    /// * `min_trade_value` - 最小交易价值
    /// * `current_price` - 当前价格
    pub fn should_trade(&self, trade_quantity: Quantity, min_trade_value: Price, current_price: Price) -> bool {
        let trade_value = trade_quantity.abs() * current_price;
        trade_value >= min_trade_value
    }
}