use serde::{Deserialize, Serialize};

/// 奖励组件分解
/// 
/// 将总奖励分解为各个具体组件，提供可解释性和调试能力
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RewardComponents {
    /// 主干奖励：基于权益变化的核心奖励
    pub portfolio_return: f64,
    
    /// 交易成本惩罚
    pub transaction_cost: f64,
    
    /// 引导奖励：基于持仓状态的塑形奖励（让利润奔跑）
    pub holding_reward: f64,
    
    /// 风险惩罚总和
    pub risk_penalty: f64,
    
    /// 持仓集中度惩罚
    pub concentration_penalty: f64,
    
    /// 回撤惩罚
    pub drawdown_penalty: f64,
    
    /// 波动率惩罚
    pub volatility_penalty: f64,
    
    /// 持仓惩罚
    pub position_penalty: f64,
}

impl RewardComponents {
    /// 计算总奖励（统一加法：奖励项为正值，惩罚项为负值）
    pub fn total_reward(&self) -> f64 {
        self.portfolio_return 
            + self.transaction_cost      // 已经是负值（惩罚）
            + self.holding_reward        // 可正可负
            + self.risk_penalty          // 已经是负值（惩罚）
            + self.concentration_penalty // 已经是负值（惩罚）
            + self.drawdown_penalty      // 已经是负值（惩罚）
            + self.volatility_penalty    // 已经是负值（惩罚）
            + self.position_penalty      // 已经是负值（惩罚）
    }
    
    /// 创建空的奖励组件
    pub fn zero() -> Self {
        Self {
            portfolio_return: 0.0,
            transaction_cost: 0.0,
            holding_reward: 0.0,
            risk_penalty: 0.0,
            concentration_penalty: 0.0,
            drawdown_penalty: 0.0,
            volatility_penalty: 0.0,
            position_penalty: 0.0,
        }
    }
}

/// 奖励计算配置
/// 
/// 配置奖励计算的各种权重和阈值参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RewardCalculationConfig {
    // 主要权重配置
    /// 权益变化权重
    pub portfolio_change_weight: f64,
    /// 交易成本权重
    pub transaction_cost_weight: f64,
    
    // 持仓状态奖励配置
    /// 盈利持仓权重（让利润奔跑）
    pub profitable_holding_weight: f64,
    /// 亏损持仓权重（及时止损）
    pub losing_holding_weight: f64,
    /// 持仓奖励上限
    pub holding_reward_cap: f64,
    
    // 风险控制配置
    /// 最大持仓集中度阈值
    pub max_position_concentration: f64,
    /// 集中度惩罚系数
    pub concentration_penalty_coeff: f64,
    
    /// 最大回撤阈值
    pub max_drawdown_threshold: f64,
    /// 回撤惩罚系数
    pub drawdown_penalty_coeff: f64,
    
    /// 最大波动率阈值
    pub max_volatility_threshold: f64,
    /// 波动率惩罚系数
    pub volatility_penalty_coeff: f64,
}

impl Default for RewardCalculationConfig {
    fn default() -> Self {
        Self {
            // 主要权重配置（重新设计，让奖励更直观）
            portfolio_change_weight: 1.0,      // 恢复权益奖励权重，但改用百分比
            transaction_cost_weight: 0.1,      // 降低交易成本权重，避免过度惩罚

            // 持仓状态奖励配置（简化）
            profitable_holding_weight: 0.001,  // 微小持仓奖励
            losing_holding_weight: 0.001,      // 微小持仓惩罚
            holding_reward_cap: 0.1,           // 持仓奖励上限

            // 风险控制配置（适度）
            max_position_concentration: 0.8,   // 80%集中度阈值
            concentration_penalty_coeff: 1.0,  // 适度集中度惩罚

            max_drawdown_threshold: 0.1,       // 10%回撤阈值
            drawdown_penalty_coeff: 2.0,       // 适度回撤惩罚

            max_volatility_threshold: 0.2,     // 20%波动率阈值
            volatility_penalty_coeff: 1.0,     // 适度波动率惩罚
        }
    }
}

/// 投资组合快照
/// 
/// 用于奖励计算的投资组合状态快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortfolioSnapshot {
    /// 总权益
    pub total_equity: f64,
    /// 现金余额
    pub cash_balance: f64,
    /// 持仓市值
    pub position_value: f64,
    /// 已实现盈亏
    pub realized_pnl: f64,
    /// 未实现盈亏
    pub unrealized_pnl: f64,
    /// 快照时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl Default for PortfolioSnapshot {
    fn default() -> Self {
        Self {
            total_equity: 0.0,
            cash_balance: 0.0,
            position_value: 0.0,
            realized_pnl: 0.0,
            unrealized_pnl: 0.0,
            timestamp: chrono::Utc::now(),
        }
    }
} 