//! 全局错误类型定义
//! 
//! 定义整个项目共享的顶层错误类型，所有Crate的错误都可以转换到这个类型。

use thiserror::Error;

/// 项目顶层错误类型
/// 
/// 所有模块的错误都可以通过 `From` trait 转换到这个错误类型，
/// 实现统一的错误处理。
#[derive(Debug, Error)]
pub enum ProjectError {
    #[error("数据模块错误: {0}")]
    DataError(String),

    #[error("账户模块错误: {0}")]
    AccountError(String),
    
    #[error("执行模块错误: {0}")]
    ExecutionError(String),

    #[error("学习模块错误: {0}")]
    LearnError(String),

    #[error("配置错误: {0}")]
    ConfigError(String),

    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("解析错误: {0}")]
    ParseError(String),

    #[error("网络错误: {0}")]
    NetworkError(String),

    #[error("认证错误: {0}")]
    AuthError(String),

    #[error("未知错误: {0}")]
    Unknown(String),
}

impl ProjectError {
    /// 创建数据模块错误
    pub fn data<T: std::fmt::Display>(msg: T) -> Self {
        Self::DataError(msg.to_string())
    }

    /// 创建账户模块错误
    pub fn account<T: std::fmt::Display>(msg: T) -> Self {
        Self::AccountError(msg.to_string())
    }

    /// 创建执行模块错误
    pub fn execution<T: std::fmt::Display>(msg: T) -> Self {
        Self::ExecutionError(msg.to_string())
    }

    /// 创建学习模块错误
    pub fn learn<T: std::fmt::Display>(msg: T) -> Self {
        Self::LearnError(msg.to_string())
    }

    /// 创建配置错误
    pub fn config<T: std::fmt::Display>(msg: T) -> Self {
        Self::ConfigError(msg.to_string())
    }

    /// 创建解析错误
    pub fn parse<T: std::fmt::Display>(msg: T) -> Self {
        Self::ParseError(msg.to_string())
    }

    /// 创建网络错误
    pub fn network<T: std::fmt::Display>(msg: T) -> Self {
        Self::NetworkError(msg.to_string())
    }

    /// 创建认证错误
    pub fn auth<T: std::fmt::Display>(msg: T) -> Self {
        Self::AuthError(msg.to_string())
    }

    /// 创建未知错误
    pub fn unknown<T: std::fmt::Display>(msg: T) -> Self {
        Self::Unknown(msg.to_string())
    }
}

/// 为字符串类型实现到 ProjectError 的转换
impl From<&str> for ProjectError {
    fn from(msg: &str) -> Self {
        Self::Unknown(msg.to_string())
    }
}

impl From<String> for ProjectError {
    fn from(msg: String) -> Self {
        Self::Unknown(msg)
    }
}

// 为数据模块错误类型预留转换支持
// 注意：这里使用字符串转换以避免循环依赖
impl ProjectError {
    /// 从数据模块错误字符串创建项目错误
    pub fn from_data_error(error_msg: String) -> Self {
        Self::DataError(error_msg)
    }
} 