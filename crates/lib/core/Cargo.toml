[package]
name = "drl-trading-core"
version.workspace = true
edition.workspace = true
description = "DRL量化交易系统的核心类型与特征定义"
authors.workspace = true

[dependencies]
rust_decimal = { workspace = true, features = ["serde-str"] }
serde = { workspace = true, features = ["derive"] }
thiserror.workspace = true

rand.workspace = true
chrono.workspace = true
log.workspace = true


[dev-dependencies]
rust_decimal_macros.workspace = true 