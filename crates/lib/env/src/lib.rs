pub mod action;
pub mod action_validator;
pub mod reward;
pub mod encoders;
pub mod env;
pub mod env_trait;
pub mod errors;
pub mod indicators;
pub mod simple_reward;

mod advanced_reward;

// 核心类型导出
pub use env::TradingEnvironment;

pub use errors::{EnvError, EnvResult};

pub use env_trait::{DataProvider, Env, ExecutionHandler, RenderMode, StateEncoder};

pub use drl_trading_core::types::space::{ActionSpace, ActionType, FeatureType, Observation, ObservationSpace};

pub use encoders::StatisticalSequenceEncoder;

// 导出reward和action模块
pub use action::{ActionExecutor, ActionExecutorConfig, ActionExecutorStats};
pub use reward::{CompositeRewardStrategy, EquityRewardStrategy, PnlRewardStrategy, ReturnBasedRewardStrategy, RewardStrategy, RiskAdjustedRewardStrategy};
pub use simple_reward::{SimpleRewardCalculator, SimpleRewardComponents};

// 导出技术指标模块
pub use indicators::{TechnicalIndicatorBounds, TechnicalIndicatorCalculator};


// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!VERSION.is_empty());
    }
}

// 工具函数和便利方法
pub mod utils {
    //! 环境相关的工具函数

    use drl_trading_core::env::EnvConfig;
    use super::*;

    /// 创建简单的离散动作空间
    pub fn create_simple_action_space() -> ActionSpace {
        ActionSpace::simple_trading()
    }

    /// 创建连续动作空间
    pub fn create_continuous_action_space() -> ActionSpace {
        ActionSpace::continuous_position()
    }

    /// 创建默认的观测空间
    pub fn create_default_observation_space(feature_count: usize) -> ObservationSpace {
        ObservationSpace::new(
            vec![feature_count],
            vec![0.0; feature_count],
            vec![1.0; feature_count],
            (0..feature_count).map(|i| format!("feature_{}", i)).collect(),
        )
    }

    /// 验证环境配置
    pub fn validate_env_config(config: &EnvConfig) -> EnvResult<()> {
        if config.initial_balance <= 0 {
            return Err(EnvError::configuration("初始资金必须大于0"));
        }

        // 简化配置验证，删除已经移除的字段检查
        if let Some(max_steps) = config.max_steps {
            if max_steps == 0 {
                return Err(EnvError::configuration("最大步数必须大于0"));
            }
        }

        Ok(())
    }
}

// 预定义配置
pub mod presets {
    //! 预定义的环境配置

    use drl_trading_core::env::EnvConfig;

    /// 保守型交易配置
    pub fn conservative_config() -> EnvConfig {
        EnvConfig {
            initial_balance: 10000,
            max_steps: Some(5000),
            record_history: true,
            history_capacity: 1000,
            seed: Some(42),
            max_drawdown_percent: 0.1,
        }
    }

    /// 激进型交易配置
    pub fn aggressive_config() -> EnvConfig {
        EnvConfig {
            initial_balance: 10000,
            max_steps: Some(10000),
            record_history: true,
            history_capacity: 2000,
            seed: Some(123),
            max_drawdown_percent: 0.2,
        }
    }

    /// 研究型配置（用于回测和研究）
    pub fn research_config() -> EnvConfig {
        EnvConfig {
            initial_balance: 10000,
            max_steps: None, // 无限制
            record_history: true,
            history_capacity: 10000,
            seed: None,
            max_drawdown_percent: 0.15,
        }
    }
}

pub use action_validator::{ActionValidator, ActionValidatorConfig, TradingActionValidator, ValidationContext, ValidationResult};
