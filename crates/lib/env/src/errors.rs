//! 环境错误处理模块
//! 
//! 定义环境运行过程中可能遇到的各种错误类型。

// 删除未使用的导入
use thiserror::Error;

/// 环境操作结果类型别名
pub type EnvResult<T> = Result<T, EnvError>;

/// 环境错误类型
#[derive(Error, Debug, Clone)]
pub enum EnvError {
    /// 数据提供器错误
    #[error("数据提供器错误: {0}")]
    DataProvider(String),
    
    /// 执行处理器错误
    #[error("执行处理器错误: {0}")]
    ExecutionHandler(String),
    
    /// 状态编码器错误
    #[error("状态编码器错误: {0}")]
    StateEncoder(String),
    
    /// 奖励计算错误
    #[error("奖励计算错误: {0}")]
    RewardCalculation(String),
    
    /// 环境配置错误
    #[error("环境配置错误: {0}")]
    Configuration(String),
    
    /// 环境状态错误
    #[error("环境状态无效: {0}")]
    InvalidState(String),
    
    /// 动作无效错误
    #[error("动作无效: {0}")]
    InvalidAction(String),
    
    /// 数据不足错误
    #[error("数据不足: {0}")]
    InsufficientData(String),
    
    /// 市场已关闭错误
    #[error("市场已关闭: {0}")]
    MarketClosed(String),
    
    /// 账户状态错误
    #[error("账户状态错误: {0}")]
    AccountState(String),
    
    /// 序列化/反序列化错误
    #[error("序列化错误: {0}")]
    Serialization(String),
    
    /// 文件操作错误
    #[error("文件操作错误: {0}")]
    FileOperation(String),
    
    /// 网络错误
    #[error("网络错误: {0}")]
    Network(String),
    
    /// 数学计算错误
    #[error("数学计算错误: {0}")]
    Mathematical(String),
    
    /// 其他错误
    #[error("其他错误: {0}")]
    Other(String),
}

impl EnvError {
    /// 创建数据提供器错误
    pub fn data_provider<T: Into<String>>(msg: T) -> Self {
        Self::DataProvider(msg.into())
    }
    
    /// 创建执行处理器错误
    pub fn execution_handler<T: Into<String>>(msg: T) -> Self {
        Self::ExecutionHandler(msg.into())
    }
    
    /// 创建状态编码器错误
    pub fn state_encoder<T: Into<String>>(msg: T) -> Self {
        Self::StateEncoder(msg.into())
    }
    
    /// 创建奖励计算错误
    pub fn reward_calculation<T: Into<String>>(msg: T) -> Self {
        Self::RewardCalculation(msg.into())
    }
    
    /// 创建配置错误
    pub fn configuration<T: Into<String>>(msg: T) -> Self {
        Self::Configuration(msg.into())
    }
    
    /// 创建无效状态错误
    pub fn invalid_state<T: Into<String>>(msg: T) -> Self {
        Self::InvalidState(msg.into())
    }
    
    /// 创建无效动作错误
    pub fn invalid_action<T: Into<String>>(msg: T) -> Self {
        Self::InvalidAction(msg.into())
    }
    
    /// 创建动作执行错误（简化版，用于action模块）
    pub fn action<T: Into<String>>(msg: T) -> Self {
        Self::InvalidAction(msg.into())
    }
    
    /// 创建数据不足错误
    pub fn insufficient_data<T: Into<String>>(msg: T) -> Self {
        Self::InsufficientData(msg.into())
    }
    
    /// 创建市场关闭错误
    pub fn market_closed<T: Into<String>>(msg: T) -> Self {
        Self::MarketClosed(msg.into())
    }
    
    /// 创建账户状态错误
    pub fn account_state<T: Into<String>>(msg: T) -> Self {
        Self::AccountState(msg.into())
    }
    
    /// 创建序列化错误
    pub fn serialization<T: Into<String>>(msg: T) -> Self {
        Self::Serialization(msg.into())
    }
    
    /// 创建文件操作错误
    pub fn file_operation<T: Into<String>>(msg: T) -> Self {
        Self::FileOperation(msg.into())
    }
    
    /// 创建网络错误
    pub fn network<T: Into<String>>(msg: T) -> Self {
        Self::Network(msg.into())
    }
    
    /// 创建数学计算错误
    pub fn mathematical<T: Into<String>>(msg: T) -> Self {
        Self::Mathematical(msg.into())
    }
    
    /// 创建其他错误
    pub fn other<T: Into<String>>(msg: T) -> Self {
        Self::Other(msg.into())
    }
}

/// 从其他错误类型转换
impl From<serde_json::Error> for EnvError {
    fn from(error: serde_json::Error) -> Self {
        Self::Serialization(error.to_string())
    }
}

impl From<std::io::Error> for EnvError {
    fn from(error: std::io::Error) -> Self {
        Self::FileOperation(error.to_string())
    }
}

impl From<toml::de::Error> for EnvError {
    fn from(error: toml::de::Error) -> Self {
        Self::Configuration(error.to_string())
    }
}

impl From<drl_trading_data::DataError> for EnvError {
    fn from(error: drl_trading_data::DataError) -> Self {
        Self::DataProvider(error.to_string())
    }
}

impl From<drl_trading_account::errors::AccountError> for EnvError {
    fn from(error: drl_trading_account::errors::AccountError) -> Self {
        Self::AccountState(error.to_string())
    }
}

// 移除了execution模块依赖

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = EnvError::data_provider("测试错误");
        assert!(error.to_string().contains("测试错误"));
        
        let error = EnvError::invalid_action("无效动作");
        assert!(error.to_string().contains("无效动作"));
    }
    
    #[test]
    fn test_error_conversion() {
        let json_error = serde_json::from_str::<i32>("invalid_json");
        assert!(json_error.is_err());
        
        let env_error: EnvError = json_error.unwrap_err().into();
        assert!(matches!(env_error, EnvError::Serialization(_)));
    }
} 