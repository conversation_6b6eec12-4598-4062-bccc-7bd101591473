use std::collections::HashMap;
use drl_trading_account::Account;
use drl_trading_core::types::common::Symbol;
use rust_decimal::prelude::ToPrimitive;
use drl_trading_core::reward::{PortfolioSnapshot, RewardCalculationConfig, RewardComponents};
use drl_trading_core::trade::Trade;


/// 高级奖励计算器
/// 
/// 实现基于Python投资组合管理器风格的复合奖励计算策略。
/// 直接从 Account 模块读取业绩数据，专注于奖励计算逻辑。
#[derive(Debug)]
pub struct AdvancedRewardCalculator {
    /// 奖励计算配置
    config: RewardCalculationConfig,
    
    /// 是否已初始化
    initialized: bool,
    
    /// 缓存持仓价格
    last_prices: Option<HashMap<Symbol, f64>>,
    
    /// 缓存持仓账户
    last_account: Option<Account>,
    
    /// 最后一次计算的奖励组件
    last_reward_components: Option<RewardComponents>,
    
    /// 本回合累积统计
    episode_stats: EpisodeRewardStats,
}

/// 回合奖励统计
#[derive(Debu<PERSON>, <PERSON><PERSON>, Default)]
struct EpisodeRewardStats {
    /// 步数计数
    step_count: u64,
    
    /// 累积奖励值
    total_portfolio_return: f64,
    total_transaction_cost: f64,
    total_holding_reward: f64,
    total_risk_penalty: f64,
    total_reward: f64,
    
    /// 动作统计
    hold_count: u64,
    buy_count: u64,
    sell_count: u64,
    close_count: u64,
}

impl AdvancedRewardCalculator {
    /// 创建新的高级奖励计算器
    pub fn new(config: RewardCalculationConfig) -> Self {
        Self {
            config,
            initialized: false,
            last_prices: None,
            last_account: None,
            last_reward_components: None,
            episode_stats: EpisodeRewardStats::default(),
        }
    }
    
    /// 使用默认配置创建
    pub fn default() -> Self {
        Self::new(RewardCalculationConfig::default())
    }
    
    /// 计算步骤奖励
    /// 
    /// # 参数
    /// * `prev_account` - 前一状态的账户
    /// * `current_account` - 当前状态的账户
    /// * `current_prices` - 当前价格映射
    /// * `action` - 执行的动作（用于统计）
    /// 
    /// # 返回
    /// (总奖励, 奖励组件详情)
    pub fn calculate_step_reward(
        &mut self,
        prev_account: &Account,
        current_account: &Account,
        current_prices: &HashMap<Symbol, f64>,
        action: &drl_trading_core::ActionType,
    ) -> (f64, RewardComponents) {
        
        // 初始化检查
        if !self.initialized {
            self.initialized = true;
            log::debug!("高级奖励计算器已初始化");
        }
        
        // 统计动作类型
        match action {
            drl_trading_core::ActionType::Hold => self.episode_stats.hold_count += 1,
            drl_trading_core::ActionType::Buy => self.episode_stats.buy_count += 1,
            drl_trading_core::ActionType::Sell => self.episode_stats.sell_count += 1,
            drl_trading_core::ActionType::Close => self.episode_stats.close_count += 1,
            drl_trading_core::ActionType::ContinuousPosition { .. } => {
                // 连续仓位动作统计为买入或卖出
                self.episode_stats.buy_count += 1; // 简化统计
            },
            drl_trading_core::ActionType::Complex { .. } => {
                // Complex动作暂时不统计，或者可以统计为其他类型
                log::warn!("遇到Complex动作，暂不统计");
            }
        }
        
        // 计算主干奖励：基于权益变化
        let portfolio_return = self.calculate_portfolio_return_reward(prev_account, current_account);
        
        // 计算交易成本惩罚
        let transaction_cost = self.calculate_transaction_cost_penalty(prev_account, current_account);
        
        // 计算持仓状态奖励
        let holding_reward = self.calculate_holding_reward(current_account, current_prices);
        
        // 计算风险惩罚
        let (concentration_penalty, drawdown_penalty, volatility_penalty) = 
            self.calculate_risk_penalties(current_account);
        
        // 计算持仓数量惩罚（可选）
        let position_penalty = self.calculate_position_penalty(current_account);
        
        // 汇总风险惩罚（各项已经是负值，直接相加）
        let risk_penalty = concentration_penalty + drawdown_penalty + volatility_penalty;
      
        // 缓存当前账户和价格信息，用于后续的风险指标计算
        self.last_account = Some(current_account.clone());
        self.last_prices = Some(current_prices.clone());
        
        let components = RewardComponents {
            portfolio_return,
            transaction_cost,
            holding_reward,
            risk_penalty,
            concentration_penalty,
            drawdown_penalty,
            volatility_penalty,
            position_penalty,
        };
        
        let total_reward = components.total_reward();
        
        log::debug!("奖励计算完成: 总奖励={:.4}, 权益奖励={:.4}, 交易成本={:.4}, 持仓奖励={:.4}, 风险惩罚={:.4}",
                   total_reward, portfolio_return, transaction_cost, holding_reward, risk_penalty);
        
        self.last_reward_components = Some(components.clone());
        
        self.episode_stats.step_count += 1;
        self.episode_stats.total_portfolio_return += portfolio_return;
        self.episode_stats.total_transaction_cost += transaction_cost;
        self.episode_stats.total_holding_reward += holding_reward;
        self.episode_stats.total_risk_penalty += risk_penalty;
        self.episode_stats.total_reward += total_reward;
        
        (total_reward, components)
    }
    
    /// 计算基于权益变化的主干奖励
    fn calculate_portfolio_return_reward(
        &self,
        prev_account: &Account,
        current_account: &Account,
    ) -> f64 {
        // 🎯 主干奖励 = 已实现盈亏变化（主要） + 权益变化（辅助）
        
        // 1. 计算已实现盈亏变化（最重要的奖励）
        let prev_realized_pnl = prev_account.state.realized_pnl.to_f64().unwrap_or(0.0);
        let current_realized_pnl = current_account.state.realized_pnl.to_f64().unwrap_or(0.0);
        let realized_pnl_change = current_realized_pnl - prev_realized_pnl;
        
        // 2. 计算总权益变化（辅助奖励，权重较小）
        let prev_equity = prev_account.get_equity_f64();
        let current_equity = current_account.get_equity_f64();
        let equity_change_rate = if prev_equity > 0.0 {
            (current_equity - prev_equity) / prev_equity
        } else {
            0.0
        };
        
        // 3. 组合奖励：已实现盈亏为主（权重80%），权益变化为辅（权重20%）
        let realized_pnl_reward = realized_pnl_change * self.config.portfolio_change_weight * 0.8;
        let equity_change_reward = equity_change_rate * self.config.portfolio_change_weight * 0.2;
        
        let total_reward = realized_pnl_reward + equity_change_reward;
        
        log::trace!("主干奖励: 已实现盈亏变化={:.4} -> 奖励={:.4}, 权益变化率={:.4} -> 奖励={:.4}, 总计={:.4}",
                   realized_pnl_change, realized_pnl_reward, equity_change_rate, equity_change_reward, total_reward);
        
        total_reward
    }
    
    /// 计算持仓状态奖励（让利润奔跑，及时止损）
    fn calculate_holding_reward(
        &self,
        account: &Account,
        current_prices: &HashMap<Symbol, f64>,
    ) -> f64 {
        let mut total_holding_reward = 0.0;
        
        // 遍历所有持仓，计算每个持仓的状态奖励
        for (symbol, position) in account.portfolio.get_all_positions() {
            if let Some(&current_price) = current_prices.get(symbol) {
                let current_price_decimal = rust_decimal::Decimal::try_from(current_price).unwrap_or_default();
                
                // 计算未实现盈亏率
                let unrealized_pnl = (current_price_decimal - position.average_entry_price) * position.quantity;
                let position_value = position.quantity * position.average_entry_price;
                
                if position_value > rust_decimal::Decimal::ZERO {
                    let pnl_rate = (unrealized_pnl / position_value).to_f64().unwrap_or(0.0);
                    
                    if pnl_rate > 0.0 {
                        // 盈利持仓：给予小额正奖励（让利润奔跑）
                        let reward = (pnl_rate * self.config.profitable_holding_weight).min(self.config.holding_reward_cap);
                        total_holding_reward += reward;
                        log::trace!("持仓 {} 盈利奖励: {:.4}", symbol, reward);
                    } else if pnl_rate < -0.05 {
                        // 显著亏损持仓：给予负奖励（促进止损）
                        let penalty = pnl_rate.abs() * self.config.losing_holding_weight;
                        total_holding_reward -= penalty;
                        log::trace!("持仓 {} 亏损惩罚: {:.4}", symbol, penalty);
                    }
                }
            }
        }
        
        total_holding_reward
    }
    
    /// 计算交易成本惩罚
    fn calculate_transaction_cost_penalty(
        &self,
        prev_account: &Account,
        current_account: &Account,
    ) -> f64 {
        // 💰 交易成本惩罚 = 仅包含手续费，不包含已实现盈亏
        
        // 计算手续费增量
        let prev_fees = prev_account.state.total_fees.to_f64().unwrap_or(0.0);
        let current_fees = current_account.state.total_fees.to_f64().unwrap_or(0.0);
        let fee_increase = current_fees - prev_fees;
        
        // 返回负的手续费（这是真正的交易成本）
        let transaction_cost_penalty = -(fee_increase * self.config.transaction_cost_weight);
        log::trace!("交易成本惩罚: 手续费增量={:.4} -> 惩罚={:.4}", fee_increase, transaction_cost_penalty);
        
        transaction_cost_penalty
    }
    
    /// 计算风险惩罚（集中度、回撤、波动率）
    fn calculate_risk_penalties(
        &self,
        account: &Account
    ) -> (f64, f64, f64) {
        // 1. 持仓集中度惩罚
        let concentration = self.calculate_position_concentration(account);
        let concentration_penalty = if concentration > self.config.max_position_concentration {
            let excess = concentration - self.config.max_position_concentration;
            -(excess * self.config.concentration_penalty_coeff) // 返回负值
        } else {
            0.0
        };
        
        // 2. 回撤惩罚
        let current_drawdown = account.performance.get_current_drawdown();
        let drawdown_penalty = if current_drawdown > self.config.max_drawdown_threshold {
            let excess = current_drawdown - self.config.max_drawdown_threshold;
            -(excess * self.config.drawdown_penalty_coeff) // 返回负值
        } else {
            0.0
        };
        
        // 3. 波动率惩罚
        let volatility = account.performance.calculate_volatility(Some(20)); // 最近20个数据点
        let volatility_penalty = if volatility > self.config.max_volatility_threshold {
            let excess = volatility - self.config.max_volatility_threshold;
            -(excess * self.config.volatility_penalty_coeff) // 返回负值
        } else {
            0.0
        };
        
        (concentration_penalty, drawdown_penalty, volatility_penalty)
    }

    /// 计算持仓集中度
    fn calculate_position_concentration(
        &self,
        account: &Account
    ) -> f64 {
        // 使用 portfolio 内置的持仓集中度计算方法
        account.portfolio.calculate_position_concentration()
    }

    /// 计算持仓数量惩罚（过多持仓的复杂性惩罚）
    fn calculate_position_penalty(&self, account: &Account) -> f64 {
        let position_count = account.portfolio.position_count();

        // 如果持仓过多，给予小额惩罚
        if position_count > 5 {
            -((position_count as f64 - 5.0) * 0.1) // 返回负值
        } else {
            0.0
        }
    }
    
    /// 重置奖励计算器（用于新回合开始）
    pub fn reset(&mut self) {
        self.initialized = false;
        self.last_account = None;
        self.last_prices = None;
        self.last_reward_components = None;
        self.episode_stats = EpisodeRewardStats::default();
        log::debug!("高级奖励计算器已重置");
    }
    
    /// 更新配置
    pub fn update_config(&mut self, config: RewardCalculationConfig) {
        self.config = config;
        log::debug!("高级奖励计算器配置已更新");
    }
    
    /// 获取最后一次奖励组件（向后兼容）
    pub fn get_last_reward_components(&self) -> Option<&RewardComponents> {
        self.last_reward_components.as_ref()
    }
    
    /// 获取本回合平均奖励组成
    pub fn get_episode_average_reward_components(&self) -> Option<RewardComponents> {
        if self.episode_stats.step_count == 0 {
            return None;
        }
        
        let step_count = self.episode_stats.step_count as f64;
        Some(RewardComponents {
            portfolio_return: self.episode_stats.total_portfolio_return / step_count,
            transaction_cost: self.episode_stats.total_transaction_cost / step_count,
            holding_reward: self.episode_stats.total_holding_reward / step_count,
            risk_penalty: self.episode_stats.total_risk_penalty / step_count,
            // 对于细分的风险惩罚项，由于没有单独累积，暂时使用0
            concentration_penalty: 0.0,
            drawdown_penalty: 0.0,
            volatility_penalty: 0.0,
            position_penalty: 0.0,
        })
    }
    
    /// 获取本回合步数统计
    pub fn get_episode_step_count(&self) -> u64 {
        self.episode_stats.step_count
    }
    
    /// 获取本回合动作统计
    pub fn get_episode_action_stats(&self) -> (u64, u64, u64, u64) {
        (
            self.episode_stats.hold_count,
            self.episode_stats.buy_count,
            self.episode_stats.sell_count,
            self.episode_stats.close_count,
        )
    }
}
