//! # 状态编码器实现
//! 
//! 将环境状态转换为神经网络可处理的数值特征向量

use drl_trading_core::types::space::{Observation, ObservationSpace};
use crate::env_trait::StateEncoder;
use crate::errors::EnvResult;
use crate::indicators::TechnicalIndicatorCalculator;
use drl_trading_account::Account;
use drl_trading_core::Tick;
use rust_decimal::prelude::ToPrimitive;

use std::collections::VecDeque;



/// 统计序列状态编码器
/// 
/// 通过对历史时间窗口内的数据进行统计聚合（均值、标准差、趋势等）
/// 来编码历史市场数据序列和账户状态。
/// 这是一种比简单拼接更强大的特征工程方法，但并非真正的Transformer。
#[derive(Debug, Clone)]
pub struct StatisticalSequenceEncoder {
    /// 历史窗口大小（序列长度）
    window_size: usize,
    /// 观测空间
    observation_space: ObservationSpace,
    /// 技术指标计算器
    indicator_calculator: TechnicalIndicatorCalculator,
    /// 历史特征缓存（用于构建序列）
    feature_history: VecDeque<Vec<f32>>,
    /// 统计聚合输出维度
    output_dim: usize,
}

/// 特征维度常量定义
const MARKET_FEATURE_COUNT: usize = 5;
const ACCOUNT_FEATURE_COUNT: usize = 4;  
const POSITION_FEATURE_COUNT: usize = 4;
const TECH_INDICATOR_COUNT: usize = 30; // 与TechnicalIndicatorCalculator::get_feature_count()保持一致
const TOTAL_FEATURE_COUNT: usize = MARKET_FEATURE_COUNT + ACCOUNT_FEATURE_COUNT + POSITION_FEATURE_COUNT + TECH_INDICATOR_COUNT;

impl StatisticalSequenceEncoder {
    /// 创建新的统计序列状态编码器
    /// 
    /// # 参数
    /// * `window_size` - 历史窗口大小（序列长度）
    /// * `output_dim` - 输出维度（如果为None则使用默认256）
    pub fn new(
        window_size: usize,
        output_dim: Option<usize>
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // 创建技术指标计算器
        let indicator_calculator = TechnicalIndicatorCalculator::new(window_size)?;
        
        // 验证技术指标数量与常量定义一致
        assert_eq!(TechnicalIndicatorCalculator::get_feature_count(), TECH_INDICATOR_COUNT,
                   "技术指标数量常量定义与实际返回不符");
        
        // 输出维度
        let output_dim = output_dim.unwrap_or(256);
        
        // 计算潜在的统计特征总数：每个原始特征维度产生5个统计量
        let potential_stat_features = TOTAL_FEATURE_COUNT * 5;
        
        // 检查是否会发生截断并发出警告
        if output_dim < potential_stat_features {
            log::warn!(
                "⚠️  StatisticalSequenceEncoder截断警告: 输出维度({})小于潜在统计特征总数({}×5={})",
                output_dim, TOTAL_FEATURE_COUNT, potential_stat_features
            );
            log::warn!(
                "   这可能导致技术指标等后续特征的统计信息被丢弃。建议设置output_dim>={}", 
                potential_stat_features
            );
        }
        
        // 创建观测空间
        let observation_space = ObservationSpace::new(
            vec![output_dim],
            vec![f64::NEG_INFINITY; output_dim],
            vec![f64::INFINITY; output_dim],
            (0..output_dim).map(|i| format!("agg_feature_{}", i)).collect(),
        );
        
        Ok(Self { 
            window_size,
            observation_space,
            indicator_calculator,
            feature_history: VecDeque::with_capacity(window_size),
            output_dim,
        })
    }
    
    /// 从单个tick和账户状态提取特征向量
    fn extract_features(&mut self, tick: &Tick, account: &Account) -> Result<Vec<f32>, Box<dyn std::error::Error>> {
        let mut features = Vec::with_capacity(TOTAL_FEATURE_COUNT);
        
        // 1. 市场特征 (5个特征)
        features.extend_from_slice(&[
            tick.open as f32,
            tick.high as f32,
            tick.low as f32,
            tick.close as f32,
            tick.volume as f32,
        ]);
        
        // 2. 账户特征 (4个特征)
        let account_summary = account.get_account_summary_f64();
        features.extend_from_slice(&[
            account_summary.total_equity as f32,
            account_summary.net_value as f32,
            account_summary.total_return as f32,
            account_summary.available_balance as f32,
        ]);
        
        // 3. 持仓特征 (4个特征) - 修正：只获取当前交易对的持仓
        let position_features = if let Some(position) = account.get_portfolio().get_position(&tick.symbol) {
            let current_price = tick.close as f32;
            let quantity = position.quantity.to_f32().unwrap_or(0.0);
            let avg_price = position.average_entry_price.to_f32().unwrap_or(0.0);
            
            let market_value = quantity * current_price;
            let unrealized_pnl = (current_price - avg_price) * quantity;
            let cost_basis = quantity.abs() * avg_price;
            let pnl_ratio = if cost_basis > 1e-6 { unrealized_pnl / cost_basis } else { 0.0 };

            [quantity, market_value, unrealized_pnl, pnl_ratio]
        } else {
            [0.0; 4] // 无持仓时返回零向量
        };
        features.extend_from_slice(&position_features);
        
        // 4. 技术指标特征 (30个特征)
        let tech_features = self.indicator_calculator.update(tick);
        features.extend(tech_features.into_iter().map(|x| x as f32));
        
        // 验证特征数量
        assert_eq!(features.len(), TOTAL_FEATURE_COUNT, 
                   "提取的特征数量({})与预期({})不符", 
                   features.len(), TOTAL_FEATURE_COUNT);
        
        Ok(features)
    }
    
    /// 对序列特征进行统计聚合
    /// 
    /// 计算序列中每个特征维度的统计量（均值、标准差、最大值、最小值、趋势）
    fn aggregate_features(&self, sequence_features: &[Vec<f32>]) -> Vec<f32> {
        let seq_len = sequence_features.len();
        
        if seq_len == 0 {
            return vec![0.0; self.output_dim];
        }
        
        let mut encoded = Vec::with_capacity(self.output_dim);
        
        // 为每个特征维度计算统计量
        for feat_idx in 0..TOTAL_FEATURE_COUNT {
            if encoded.len() >= self.output_dim { break; }
            
            // 计算均值 - 优化：避免创建中间Vec
            let mean = sequence_features.iter()
                .map(|frame| frame.get(feat_idx).copied().unwrap_or(0.0))
                .sum::<f32>() / seq_len as f32;
            encoded.push(mean);
            
            // 计算标准差
            if encoded.len() < self.output_dim {
                let variance = sequence_features.iter()
                    .map(|frame| {
                        let val = frame.get(feat_idx).copied().unwrap_or(0.0);
                        (val - mean).powi(2)
                    })
                    .sum::<f32>() / seq_len as f32;
                encoded.push(variance.sqrt());
            }
            
            // 计算最大值和最小值
            if encoded.len() < self.output_dim {
                let max_val = sequence_features.iter()
                    .map(|frame| frame.get(feat_idx).copied().unwrap_or(0.0))
                    .fold(f32::NEG_INFINITY, |a, b| a.max(b));
                encoded.push(max_val);
            }
            
            if encoded.len() < self.output_dim {
                let min_val = sequence_features.iter()
                    .map(|frame| frame.get(feat_idx).copied().unwrap_or(0.0))
                    .fold(f32::INFINITY, |a, b| a.min(b));
                encoded.push(min_val);
            }
            
            // 计算趋势（最后值 - 第一值）
            if encoded.len() < self.output_dim && seq_len > 1 {
                let first_val = sequence_features.first()
                    .and_then(|frame| frame.get(feat_idx).copied())
                    .unwrap_or(0.0);
                let last_val = sequence_features.last()
                    .and_then(|frame| frame.get(feat_idx).copied())
                    .unwrap_or(0.0);
                encoded.push(last_val - first_val);
            }
        }
        
        // 填充或截断到指定维度
        encoded.resize(self.output_dim, 0.0);
        encoded
    }
}

impl StateEncoder for StatisticalSequenceEncoder {
    fn encode(&mut self, tick: &Tick, account: &Account) -> EnvResult<Observation> {
        // 提取当前时刻的特征
        let current_features = self.extract_features(tick, account)
            .map_err(|e| crate::errors::EnvError::state_encoder(format!("特征提取失败: {}", e)))?;
        
        // 添加到历史缓存
        self.feature_history.push_back(current_features);
        
        // 保持窗口大小
        if self.feature_history.len() > self.window_size {
            self.feature_history.pop_front();
        }
        
        // 构建序列特征 - 优化：减少不必要的clone操作
        let sequence_features: Vec<Vec<f32>> = if self.feature_history.len() < self.window_size {
            // 数据不足时，用零填充前面的时间步
            let padding_count = self.window_size - self.feature_history.len();
            let padding = vec![vec![0.0f32; TOTAL_FEATURE_COUNT]; padding_count];
            padding.into_iter().chain(self.feature_history.iter().cloned()).collect()
        } else {
            // 数据充足时，直接使用历史数据
            self.feature_history.iter().cloned().collect()
        };
        
        // 使用统计聚合编码器
        let encoded_output = self.aggregate_features(&sequence_features);
        
        // 转换为f64类型
        let encoded_features_f64: Vec<f64> = encoded_output.iter().map(|&x| x as f64).collect();
        
        // 创建观测 - 使用专用的编码特征字段
        let observation = Observation {
            market_features: vec![], // 不再使用market_features存储聚合结果
            account_features: vec![],
            position_features: vec![],
            technical_features: vec![],
            time_features: vec![],
            encoded_features: encoded_features_f64, // 使用专用字段存储编码结果
            feature_names: self.observation_space.feature_names.clone(),
            timestamp: chrono::Utc::now(),
            extra: std::collections::HashMap::new(),
        };
        
        Ok(observation)
    }
    
    fn observation_space(&self) -> &ObservationSpace {
        &self.observation_space
    }
}
