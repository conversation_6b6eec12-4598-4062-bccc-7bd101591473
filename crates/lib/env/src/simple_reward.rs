//! # 简化奖励函数
//! 
//! 专门用于正弦波数据训练的简化奖励计算

use drl_trading_account::Account;
use drl_trading_core::{ActionType, Tick};
use rust_decimal::prelude::ToPrimitive;

/// 简化奖励计算器
/// 
/// 专注于基本的盈亏奖励，去除复杂的风险惩罚
#[derive(Debug, Clone)]
pub struct SimpleRewardCalculator {
    /// 奖励缩放因子
    pub reward_scale: f64,
    /// 交易成本权重
    pub transaction_cost_weight: f64,
    /// 基准收益率（用于相对奖励计算）
    pub benchmark_return: f64,
    /// 上一步的账户权益
    last_equity: Option<f64>,
    /// 累计基准收益
    cumulative_benchmark: f64,
}

impl SimpleRewardCalculator {
    /// 创建新的简化奖励计算器
    pub fn new(reward_scale: f64, transaction_cost_weight: f64) -> Self {
        Self {
            reward_scale,
            transaction_cost_weight,
            benchmark_return: 0.0,
            last_equity: None,
            cumulative_benchmark: 0.0,
        }
    }
    
    /// 使用默认参数创建
    pub fn default() -> Self {
        Self::new(1.0, 0.1)
    }
    
    /// 计算步骤奖励
    /// 
    /// # 参数
    /// * `prev_account` - 前一状态的账户
    /// * `current_account` - 当前状态的账户
    /// * `action` - 执行的动作
    /// * `tick` - 当前市场数据
    /// 
    /// # 返回
    /// (总奖励, 奖励组件详情)
    pub fn calculate_reward(
        &mut self,
        prev_account: &Account,
        current_account: &Account,
        action: &ActionType,
        tick: &Tick,
    ) -> (f64, SimpleRewardComponents) {
        
        // 计算权益变化奖励
        let equity_reward = self.calculate_equity_reward(prev_account, current_account);
        
        // 计算交易成本惩罚
        let transaction_cost = self.calculate_transaction_cost(action);
        
        // 计算相对基准的超额收益奖励
        let excess_return_reward = self.calculate_excess_return_reward(
            prev_account, 
            current_account, 
            tick
        );
        
        // 计算方向性奖励（奖励正确的方向判断）
        let direction_reward = self.calculate_direction_reward(action, tick);
        
        let components = SimpleRewardComponents {
            equity_reward,
            transaction_cost,
            excess_return_reward,
            direction_reward,
        };
        
        let total_reward = components.total_reward() * self.reward_scale;
        
        log::debug!(
            "简化奖励: 总计={:.4}, 权益={:.4}, 成本={:.4}, 超额={:.4}, 方向={:.4}",
            total_reward, equity_reward, transaction_cost, excess_return_reward, direction_reward
        );
        
        (total_reward, components)
    }
    
    /// 计算权益变化奖励
    fn calculate_equity_reward(&mut self, prev_account: &Account, current_account: &Account) -> f64 {
        let prev_equity = prev_account.get_equity_f64();
        let current_equity = current_account.get_equity_f64();
        
        // 更新记录
        self.last_equity = Some(current_equity);
        
        if prev_equity > 0.0 {
            // 计算收益率
            let return_rate = (current_equity - prev_equity) / prev_equity;
            return_rate * 100.0 // 放大到合理的奖励范围
        } else {
            0.0
        }
    }
    
    /// 计算交易成本惩罚
    fn calculate_transaction_cost(&self, action: &ActionType) -> f64 {
        let cost = match action {
            ActionType::Hold => 0.0,
            ActionType::Buy | ActionType::Sell | ActionType::Close => {
                -self.transaction_cost_weight // 固定交易成本
            }
            ActionType::ContinuousPosition { position_ratio: _ } => {
                -self.transaction_cost_weight * 0.5 // 连续动作的成本较低
            }
            ActionType::Complex { .. } => -self.transaction_cost_weight,
        };
        
        cost
    }
    
    /// 计算相对基准的超额收益奖励
    fn calculate_excess_return_reward(
        &mut self,
        prev_account: &Account,
        current_account: &Account,
        tick: &Tick,
    ) -> f64 {
        // 简单的买入持有基准：假设一直持有资产
        let price_change_rate = if let Some(last_equity) = self.last_equity {
            // 使用价格变化作为基准收益
            let prev_price = last_equity; // 简化：使用权益作为价格代理
            let current_price = tick.close;
            if prev_price > 0.0 {
                (current_price - prev_price) / prev_price
            } else {
                0.0
            }
        } else {
            0.0
        };
        
        self.benchmark_return = price_change_rate;
        self.cumulative_benchmark += price_change_rate;
        
        // 计算策略收益
        let prev_equity = prev_account.get_equity_f64();
        let current_equity = current_account.get_equity_f64();
        let strategy_return = if prev_equity > 0.0 {
            (current_equity - prev_equity) / prev_equity
        } else {
            0.0
        };
        
        // 超额收益奖励
        let excess_return = strategy_return - self.benchmark_return;
        excess_return * 50.0 // 放大超额收益的奖励
    }
    
    /// 计算方向性奖励（奖励正确的市场方向判断）
    fn calculate_direction_reward(&self, action: &ActionType, tick: &Tick) -> f64 {
        // 简化：基于价格的简单趋势判断
        // 在实际应用中，可以使用更复杂的趋势指标
        
        let price_momentum = if tick.close > tick.open {
            1.0 // 上涨
        } else if tick.close < tick.open {
            -1.0 // 下跌
        } else {
            0.0 // 横盘
        };
        
        let action_direction = match action {
            ActionType::Buy => 1.0,
            ActionType::Sell | ActionType::Close => -1.0,
            ActionType::Hold => 0.0,
            ActionType::ContinuousPosition { position_ratio } => {
                position_ratio.signum()
            }
            ActionType::Complex { .. } => 0.0,
        };
        
        // 方向一致时给予奖励
        let direction_alignment = price_momentum * action_direction;
        direction_alignment * 0.5 // 适度的方向奖励
    }
    
    /// 重置计算器状态
    pub fn reset(&mut self) {
        self.last_equity = None;
        self.cumulative_benchmark = 0.0;
        self.benchmark_return = 0.0;
    }
    
    /// 获取累计基准收益
    pub fn get_cumulative_benchmark(&self) -> f64 {
        self.cumulative_benchmark
    }
}

/// 简化奖励组件
#[derive(Debug, Clone)]
pub struct SimpleRewardComponents {
    /// 权益变化奖励
    pub equity_reward: f64,
    /// 交易成本惩罚
    pub transaction_cost: f64,
    /// 超额收益奖励
    pub excess_return_reward: f64,
    /// 方向性奖励
    pub direction_reward: f64,
}

impl SimpleRewardComponents {
    /// 计算总奖励
    pub fn total_reward(&self) -> f64 {
        self.equity_reward + self.transaction_cost + self.excess_return_reward + self.direction_reward
    }
    
    /// 获取主要奖励（权益+超额收益）
    pub fn main_reward(&self) -> f64 {
        self.equity_reward + self.excess_return_reward
    }
    
    /// 获取惩罚总和
    pub fn total_penalty(&self) -> f64 {
        self.transaction_cost.min(0.0) // 只计算负值
    }
}

impl Default for SimpleRewardComponents {
    fn default() -> Self {
        Self {
            equity_reward: 0.0,
            transaction_cost: 0.0,
            excess_return_reward: 0.0,
            direction_reward: 0.0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use drl_trading_core::{Symbol, Tick};
    use drl_trading_account::{Account, AccountConfig};
    use rust_decimal::Decimal;
    
    #[test]
    fn test_simple_reward_calculation() {
        let mut calculator = SimpleRewardCalculator::default();
        
        // 创建测试账户
        let config = AccountConfig::default();
        let mut prev_account = Account::new(config.clone());
        let mut current_account = Account::new(config);
        
        // 模拟权益变化
        // 这里需要根据实际的Account API来设置权益
        
        let action = ActionType::Buy;
        let tick = Tick {
            symbol: Symbol::new("TESTUSDT"),
            timestamp: 0,
            open: 100.0,
            high: 101.0,
            low: 99.0,
            close: 101.0,
            volume: 1000.0,
        };
        
        let (reward, components) = calculator.calculate_reward(
            &prev_account,
            &current_account,
            &action,
            &tick,
        );
        
        // 基本验证
        assert!(reward.is_finite());
        assert_eq!(components.total_reward(), components.equity_reward + components.transaction_cost + components.excess_return_reward + components.direction_reward);
    }
}
