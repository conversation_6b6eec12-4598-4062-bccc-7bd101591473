use drl_trading_account::account::AccountSnapshot;
use rust_decimal::prelude::ToPrimitive;

/// 奖励策略接口
/// 
/// 基于账户快照计算奖励，实现模块化设计原则。
/// 不同的奖励策略可以关注不同的指标：
/// - 已实现盈亏变化
/// - 总权益变化  
/// - 风险调整收益
/// - 收益率变化
pub trait RewardStrategy: Send + Sync {
    /// 根据执行一个动作前后的账户快照变化来计算奖励
    /// 
    /// # 参数
    /// * `before` - 执行动作前的账户快照
    /// * `after` - 执行动作后的账户快照
    /// 
    /// # 返回值
    /// 计算出的奖励值，正值表示好的行为，负值表示坏的行为
    fn calculate_reward(&self, before: &AccountSnapshot, after: &AccountSnapshot) -> f64;
    
    /// 获取策略名称
    fn name(&self) -> &str;
    
    /// 获取策略描述
    fn description(&self) -> &str {
        "基础奖励策略"
    }
}

/// 基于已实现盈亏变动的奖励策略
/// 
/// 这是最直接的奖励策略，奖励值等于已实现盈亏的变化量。
/// 适用于关注短期交易盈亏的场景。
#[derive(Debug, Clone)]
pub struct PnlRewardStrategy;

impl RewardStrategy for PnlRewardStrategy {
    fn calculate_reward(&self, before: &AccountSnapshot, after: &AccountSnapshot) -> f64 {
        // 计算已实现盈亏的变化
        let pnl_change = after.realized_pnl - before.realized_pnl;
        pnl_change.to_f64().unwrap_or(0.0)
    }
    
    fn name(&self) -> &str {
        "PnL奖励策略"
    }
    
    fn description(&self) -> &str {
        "基于已实现盈亏变化的简单奖励策略"
    }
}

/// 基于总权益变动的奖励策略
/// 
/// 考虑总权益（包括未实现盈亏）的变化，更全面地反映账户价值变化。
/// 适用于关注整体资产价值的场景。
#[derive(Debug, Clone)]
pub struct EquityRewardStrategy;

impl RewardStrategy for EquityRewardStrategy {
    fn calculate_reward(&self, before: &AccountSnapshot, after: &AccountSnapshot) -> f64 {
        let equity_change = after.equity_change(before);
        equity_change.to_f64().unwrap_or(0.0)
    }
    
    fn name(&self) -> &str {
        "权益奖励策略"
    }
    
    fn description(&self) -> &str {
        "基于总权益变化的奖励策略，包含未实现盈亏"
    }
}

/// 风险调整的奖励策略
/// 
/// 在权益变化的基础上，考虑最大回撤的影响。
/// 如果回撤增加，会减少奖励；如果回撤减少，会增加奖励。
#[derive(Debug, Clone)]
pub struct RiskAdjustedRewardStrategy {
    /// 回撤惩罚系数
    pub drawdown_penalty_factor: f64,
}

impl Default for RiskAdjustedRewardStrategy {
    fn default() -> Self {
        Self {
            drawdown_penalty_factor: 2.0, // 回撤的惩罚权重
        }
    }
}

impl RiskAdjustedRewardStrategy {
    /// 创建风险调整奖励策略
    pub fn new(drawdown_penalty_factor: f64) -> Self {
        Self {
            drawdown_penalty_factor,
        }
    }
}

impl RewardStrategy for RiskAdjustedRewardStrategy {
    fn calculate_reward(&self, before: &AccountSnapshot, after: &AccountSnapshot) -> f64 {
        // 基础权益变化奖励
        let equity_change = after.equity_change(before);
        let base_reward = equity_change.to_f64().unwrap_or(0.0);
        
        // 简化：基于权益变化幅度的风险调整
        // 如果权益变化过大，施加适当惩罚以鼓励稳定增长
        let equity_ratio_change = if before.total_equity > rust_decimal::Decimal::ZERO {
            (equity_change / before.total_equity).to_f64().unwrap_or(0.0).abs()
        } else {
            0.0
        };
        
        // 对过大的权益波动施加惩罚
        let volatility_penalty = if equity_ratio_change > 0.1 { // 10%以上的波动
            equity_ratio_change * self.drawdown_penalty_factor
        } else {
            0.0
        };
        
        base_reward - (volatility_penalty * base_reward.abs())
    }
    
    fn name(&self) -> &str {
        "风险调整奖励策略"
    }
    
    fn description(&self) -> &str {
        "基于权益变化并考虑最大回撤的风险调整奖励策略"
    }
}

/// 基于收益率的奖励策略
/// 
/// 使用收益率而不是绝对金额，使奖励与账户规模无关。
/// 适用于需要标准化奖励的场景。
#[derive(Debug, Clone)]
pub struct ReturnBasedRewardStrategy;

impl RewardStrategy for ReturnBasedRewardStrategy {
    fn calculate_reward(&self, before: &AccountSnapshot, after: &AccountSnapshot) -> f64 {
        // 计算收益率变化
        let before_return = before.total_return();
        let after_return = after.total_return();
        let return_change = after_return - before_return;
        return_change.to_f64().unwrap_or(0.0)
    }
    
    fn name(&self) -> &str {
        "收益率奖励策略"
    }
    
    fn description(&self) -> &str {
        "基于收益率变化的标准化奖励策略"
    }
}

/// 组合奖励策略
/// 
/// 结合多个奖励策略，使用加权平均计算最终奖励。
/// 允许同时考虑多个指标。
pub struct CompositeRewardStrategy {
    /// 策略和权重的组合
    strategies: Vec<(Box<dyn RewardStrategy>, f64)>,
}

impl CompositeRewardStrategy {
    /// 创建新的组合策略
    pub fn new() -> Self {
        Self {
            strategies: Vec::new(),
        }
    }
    
    /// 添加一个策略和权重
    pub fn add_strategy(mut self, strategy: Box<dyn RewardStrategy>, weight: f64) -> Self {
        self.strategies.push((strategy, weight));
        self
    }
}

impl Default for CompositeRewardStrategy {
    fn default() -> Self {
        Self::new()
    }
}

impl RewardStrategy for CompositeRewardStrategy {
    fn calculate_reward(&self, before: &AccountSnapshot, after: &AccountSnapshot) -> f64 {
        if self.strategies.is_empty() {
            return 0.0;
        }
        
        let total_weight: f64 = self.strategies.iter().map(|(_, weight)| weight).sum();
        
        if total_weight == 0.0 {
            return 0.0;
        }
        
        let weighted_sum: f64 = self.strategies
            .iter()
            .map(|(strategy, weight)| {
                strategy.calculate_reward(before, after) * weight
            })
            .sum();
        
        weighted_sum / total_weight
    }
    fn name(&self) -> &str {
        "组合奖励策略"
    }
    
    fn description(&self) -> &str {
        "结合多个奖励策略的加权组合策略"
    }
}
