//! # 动作验证器模块
//! 
//! 实现动作掩码技术，从源头阻止智能体选择无效动作，提升训练效率和样本质量。
//! 
//! ## 核心理念
//! - **预防胜于惩罚**：从源头阻止无效动作，而不是事后惩罚
//! - **100%样本效率**：确保每个经验都是有效的策略决策
//! - **分层验证**：支持基础验证、风险控制、合规性检查等多层次验证

use drl_trading_account::Account;
use drl_trading_core::{ActionType, Symbol};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::Timelike;
use rust_decimal::prelude::*;

/// 验证上下文
/// 
/// 包含验证动作合法性所需的所有信息
#[derive(Debug, Clone)]
pub struct ValidationContext<'a> {
    /// 账户状态
    pub account: &'a Account,
    /// 当前价格映射
    pub current_prices: &'a HashMap<Symbol, f64>,
    /// 当前交易标的
    pub current_symbol: &'a Symbol,
    /// 当前价格
    pub current_price: f64,
    /// 时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 验证结果
#[derive(Debug, Clone)]
pub struct ValidationResult {
    /// 是否有效
    pub is_valid: bool,
    /// 失败原因
    pub reason: Option<String>,
    /// 建议的替代动作
    pub suggested_action: Option<ActionType>,
}

impl ValidationResult {
    pub fn valid() -> Self {
        Self {
            is_valid: true,
            reason: None,
            suggested_action: None,
        }
    }
    
    pub fn invalid(reason: String) -> Self {
        Self {
            is_valid: false,
            reason: Some(reason),
            suggested_action: Some(ActionType::Hold),
        }
    }
    
    pub fn invalid_with_suggestion(reason: String, suggested: ActionType) -> Self {
        Self {
            is_valid: false,
            reason: Some(reason),
            suggested_action: Some(suggested),
        }
    }
}

/// 动作验证器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActionValidatorConfig {
    /// 启用基础验证（资金、持仓检查）
    pub enable_basic_validation: bool,
    
    /// 启用风险控制验证
    pub enable_risk_validation: bool,
    
    /// 启用合规性验证
    pub enable_compliance_validation: bool,
    
    /// 最小交易金额（USDT）
    pub min_trade_amount: f64,
    
    /// 最大单次交易金额（USDT）
    pub max_trade_amount: f64,
    
    /// 最大持仓集中度（0.0-1.0）
    pub max_position_concentration: f64,
    
    /// 最大单日交易次数
    pub max_daily_trades: usize,
    
    /// 交易时间限制（UTC小时，如果为空则不限制）
    pub trading_hours: Option<(u32, u32)>,
    
    /// 紧急情况下是否允许平仓
    pub allow_emergency_close: bool,
}

impl Default for ActionValidatorConfig {
    fn default() -> Self {
        Self {
            enable_basic_validation: true,
            enable_risk_validation: true,
            enable_compliance_validation: false,
            min_trade_amount: 10.0,
            max_trade_amount: 10000.0,
            max_position_concentration: 0.8,
            max_daily_trades: 100,
            trading_hours: None, // 24小时交易
            allow_emergency_close: true,
        }
    }
}

/// 动作验证器接口
pub trait ActionValidator {
    /// 验证单个动作是否合法
    fn validate_action(&self, action: &ActionType, context: &ValidationContext) -> ValidationResult;
    
    /// 获取动作掩码（布尔向量，true表示合法）
    fn get_action_mask(&self, context: &ValidationContext) -> Vec<bool>;
    
    /// 获取所有合法动作
    fn get_valid_actions(&self, context: &ValidationContext) -> Vec<ActionType>;
    
    /// 获取推荐动作（如果当前动作无效）
    fn get_recommended_action(&self, context: &ValidationContext) -> ActionType;
}

/// 交易动作验证器
/// 
/// 实现交易环境中的动作验证逻辑
pub struct TradingActionValidator {
    config: ActionValidatorConfig,
    /// 今日交易计数器（简化实现，实际应该基于日期）
    daily_trade_count: usize,
}

impl TradingActionValidator {
    /// 创建新的交易动作验证器
    pub fn new(config: ActionValidatorConfig) -> Self {
        Self {
            config,
            daily_trade_count: 0,
        }
    }
    
    /// 使用默认配置创建验证器
    pub fn default() -> Self {
        Self::new(ActionValidatorConfig::default())
    }
    
    /// 重置日交易计数器（通常在新的交易日开始时调用）
    pub fn reset_daily_count(&mut self) {
        self.daily_trade_count = 0;
    }
    
    /// 增加交易计数
    pub fn increment_trade_count(&mut self) {
        self.daily_trade_count += 1;
    }
    
    /// 获取配置的引用
    pub fn config(&self) -> &ActionValidatorConfig {
        &self.config
    }
    
    /// 更新配置
    pub fn update_config(&mut self, config: ActionValidatorConfig) {
        self.config = config;
    }
    
    /// 基础验证：检查资金和持仓
    fn validate_basic(&self, action: &ActionType, context: &ValidationContext) -> ValidationResult {
        if !self.config.enable_basic_validation {
            return ValidationResult::valid();
        }
        
        match action {
            ActionType::Hold => ValidationResult::valid(),
            
            ActionType::Buy => {
                let available_balance = context.account.state.available_balance.to_f64().unwrap_or(0.0);
                let required_amount = 2.0 * context.current_price;
                
                if available_balance < required_amount {
                    return ValidationResult::invalid(
                        format!("资金不足：需要 {:.2} USDT，可用 {:.2} USDT", 
                               required_amount, available_balance)
                    );
                }
                
                if required_amount < self.config.min_trade_amount {
                    return ValidationResult::invalid(
                        format!("交易金额过小：{:.2} < {:.2}", 
                               required_amount, self.config.min_trade_amount)
                    );
                }
                
                if required_amount > self.config.max_trade_amount {
                    return ValidationResult::invalid(
                        format!("交易金额过大：{:.2} > {:.2}", 
                               required_amount, self.config.max_trade_amount)
                    );
                }
                
                ValidationResult::valid()
            }
            
            ActionType::Sell => {
                // 获取当前持仓
                let current_position = context.account.portfolio
                    .get_position(context.current_symbol)
                    .map(|pos| pos.quantity.to_f64().unwrap_or(0.0))
                    .unwrap_or(0.0);
                
                if current_position <= 0.0 {
                    return ValidationResult::invalid("无持仓可卖出".to_string());
                }
                
                let sell_quantity = 2.0_f64.min(current_position); // 最多卖出2单位或全部持仓
                let sell_amount = sell_quantity * context.current_price;
                
                if sell_amount < self.config.min_trade_amount {
                    return ValidationResult::invalid(
                        format!("卖出金额过小：{:.2} < {:.2}", 
                               sell_amount, self.config.min_trade_amount)
                    );
                }
                
                ValidationResult::valid()
            }
            
            ActionType::Close => {
                // 检查是否有持仓需要平仓
                let current_position = context.account.portfolio
                    .get_position(context.current_symbol)
                    .map(|pos| pos.quantity.to_f64().unwrap_or(0.0))
                    .unwrap_or(0.0);
                
                if current_position == 0.0 {
                    return ValidationResult::invalid("无持仓可平仓".to_string());
                }
                
                ValidationResult::valid()
            }
            
            ActionType::Complex { .. } => {
                // 复杂动作暂时不支持
                ValidationResult::invalid("暂不支持复杂动作".to_string())
            }
        }
    }
    
    /// 风险控制验证
    fn validate_risk(&self, action: &ActionType, context: &ValidationContext) -> ValidationResult {
        if !self.config.enable_risk_validation {
            return ValidationResult::valid();
        }
        
        match action {
            ActionType::Hold | ActionType::Close => ValidationResult::valid(),
            
            ActionType::Buy => {
                // 检查持仓集中度
                let total_equity = context.account.get_equity_f64();
                let current_position_value = context.account.portfolio
                    .get_position_market_value(context.current_symbol)
                    .map(|v| v.to_f64().unwrap_or(0.0))
                    .unwrap_or(0.0);
                
                let buy_amount = 2.0 * context.current_price;
                let new_position_value = current_position_value + buy_amount;
                let concentration = new_position_value / total_equity;
                
                if concentration > self.config.max_position_concentration {
                    return ValidationResult::invalid(
                        format!("持仓集中度过高：{:.1}% > {:.1}%",
                               concentration * 100.0,
                               self.config.max_position_concentration * 100.0)
                    );
                }
                
                // 检查日交易次数限制
                if self.daily_trade_count >= self.config.max_daily_trades {
                    return ValidationResult::invalid(
                        format!("超出日交易次数限制：{} >= {}",
                               self.daily_trade_count, self.config.max_daily_trades)
                    );
                }
                
                ValidationResult::valid()
            }
            
            ActionType::Sell => {
                // 卖出操作一般风险较低，但仍检查交易次数
                if self.daily_trade_count >= self.config.max_daily_trades {
                    return ValidationResult::invalid(
                        format!("超出日交易次数限制：{} >= {}",
                               self.daily_trade_count, self.config.max_daily_trades)
                    );
                }
                
                ValidationResult::valid()
            }
            
            ActionType::Complex { .. } => {
                ValidationResult::invalid("暂不支持复杂动作的风险验证".to_string())
            }
        }
    }
    
    /// 合规性验证
    fn validate_compliance(&self, action: &ActionType, context: &ValidationContext) -> ValidationResult {
        if !self.config.enable_compliance_validation {
            return ValidationResult::valid();
        }
        
        // 检查交易时间限制
        if let Some((start_hour, end_hour)) = self.config.trading_hours {
            let current_hour = context.timestamp.hour();
            let is_trading_hours = if start_hour <= end_hour {
                current_hour >= start_hour && current_hour < end_hour
            } else {
                // 跨夜交易时间
                current_hour >= start_hour || current_hour < end_hour
            };
            
            if !is_trading_hours {
                // 紧急情况下允许平仓
                if matches!(action, ActionType::Close) && self.config.allow_emergency_close {
                    return ValidationResult::valid();
                }
                
                return ValidationResult::invalid_with_suggestion(
                    format!("非交易时间：{:02}:00 不在 {:02}:00-{:02}:00 范围内", 
                           current_hour, start_hour, end_hour),
                    ActionType::Hold
                );
            }
        }
        
        ValidationResult::valid()
    }
}

impl ActionValidator for TradingActionValidator {
    fn validate_action(&self, action: &ActionType, context: &ValidationContext) -> ValidationResult {
        // 分层验证：基础 -> 风险 -> 合规
        let basic_result = self.validate_basic(action, context);
        if !basic_result.is_valid {
            return basic_result;
        }
        
        let risk_result = self.validate_risk(action, context);
        if !risk_result.is_valid {
            return risk_result;
        }
        
        let compliance_result = self.validate_compliance(action, context);
        if !compliance_result.is_valid {
            return compliance_result;
        }
        
        ValidationResult::valid()
    }
    
    fn get_action_mask(&self, context: &ValidationContext) -> Vec<bool> {
        // 标准4动作空间：[Hold, Buy, Sell, Close]
        let actions = [
            ActionType::Hold,
            ActionType::Buy,
            ActionType::Sell,
            ActionType::Close,
        ];
        
        actions.iter()
            .map(|action| self.validate_action(action, context).is_valid)
            .collect()
    }
    
    fn get_valid_actions(&self, context: &ValidationContext) -> Vec<ActionType> {
        let actions = [
            ActionType::Hold,
            ActionType::Buy,
            ActionType::Sell,
            ActionType::Close,
        ];
        
        actions.into_iter()
            .filter(|action| self.validate_action(action, context).is_valid)
            .collect()
    }
    
    fn get_recommended_action(&self, context: &ValidationContext) -> ActionType {
        // 优先级：Hold > Close > Sell > Buy
        let actions = [
            ActionType::Hold,
            ActionType::Close,
            ActionType::Sell,
            ActionType::Buy,
        ];
        
        for action in actions {
            if self.validate_action(&action, context).is_valid {
                return action;
            }
        }
        
        // 保底：总是返回Hold（Hold应该总是有效的）
        ActionType::Hold
    }
}
