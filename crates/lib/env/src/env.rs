//! 核心环境实现模块
//!
//! 实现核心的交易环境，整合所有组件。

use chrono::{DateTime, TimeZone, Utc};
use log::{debug, info, warn};
use rand::SeedableRng;
use rand_chacha::ChaCha8Rng;
use rust_decimal::prelude::ToPrimitive;
use std::fs;

use crate::env_trait::{Env, RenderMode, StateEncoder};
use crate::errors::{EnvError, EnvResult};
use drl_trading_core::types::space::{ActionSpace, ActionType, Observation, ObservationSpace};

use crate::action::ActionExecutor;
use crate::action_validator::{ActionValidator, TradingActionValidator, ValidationContext};


use drl_trading_account::Account;
use drl_trading_core::env::{EnvConfig, EnvState};
use drl_trading_core::trade::Trade;
use drl_trading_core::DimensionInfo;
use drl_trading_core::reward::RewardCalculationConfig;
use drl_trading_data::DataProvider;
use crate::advanced_reward::AdvancedRewardCalculator;

/// 交易环境核心实现
pub struct TradingEnvironment {
    /// 数据提供器
    data_provider: Box<dyn DataProvider>,

    /// 账户管理器
    account: Account,

    /// 高级奖励计算器（现在是必需的）
    advanced_reward_calculator: AdvancedRewardCalculator,

    /// 动作验证器
    action_validator: TradingActionValidator,

    /// 状态编码器
    state_encoder: Box<dyn StateEncoder>,

    /// 动作执行器（负责动作转订单）
    action_executor: ActionExecutor,

    /// 动作空间
    action_space: ActionSpace,

    /// 观测空间
    observation_space: ObservationSpace,

    /// 环境配置
    config: EnvConfig,

    /// 当前状态
    current_state: EnvState,

    /// 随机数生成器
    rng: ChaCha8Rng,

    /// 累计奖励
    total_reward: f64,

    /// 上一次的账户状态（用于高级奖励计算）
    prev_account: Option<Account>,

    /// 执行动作前的权益（用于正确计算奖励）
    equity_before_action: f64,
}

impl TradingEnvironment {
    /// 创建带有高级奖励计算器的交易环境
    pub fn new(
        data_provider: Box<dyn DataProvider>,
        account: Account,
        state_encoder: Box<dyn StateEncoder>,
        config: EnvConfig,
        reward_config: RewardCalculationConfig,
    ) -> EnvResult<Self> {
        // 根据配置创建动作空间（默认使用连续动作空间）
        let action_space = ActionSpace::continuous_position();
        let observation_space = state_encoder.observation_space().clone();

        let rng = if let Some(seed) = config.seed {
            ChaCha8Rng::seed_from_u64(seed)
        } else {
            ChaCha8Rng::from_entropy()
        };

        let advanced_reward_calculator = AdvancedRewardCalculator::new(reward_config);

        let initial_equity = account.get_equity_f64();

        Ok(Self {
            data_provider,
            account,
            advanced_reward_calculator,
            action_validator: TradingActionValidator::default(),
            state_encoder,
            action_executor: ActionExecutor::default(),
            action_space,
            observation_space,
            config,
            current_state: EnvState::default(),
            rng,
            total_reward: 0.0,
            prev_account: None,
            equity_before_action: initial_equity,
        })
    }

    /// 更新高级奖励计算器配置
    pub fn update_reward_config(&mut self, reward_config: RewardCalculationConfig) {
        self.advanced_reward_calculator.update_config(reward_config);
    }

    /// 获取风险指标
    pub fn get_risk_metrics(&self) -> drl_trading_account::performance::RiskMetrics {
        self.account.get_risk_metrics()
    }

    /// 获取性能指标
    pub fn get_performance_metrics(&self) -> drl_trading_account::performance::PerformanceMetrics {
        self.account.get_performance_metrics()
    }

    /// 获取当前回撤百分比
    pub fn get_current_drawdown_percent(&self) -> f64 {
        let initial_balance = self.config.initial_balance as f64;
        let current_equity = self.account.get_equity_f64();
        if initial_balance > 0.0 {
            ((initial_balance - current_equity) / initial_balance).max(0.0)
        } else {
            0.0
        }
    }

    /// 获取距离风控线的余量
    pub fn get_risk_buffer(&self) -> f64 {
        let current_drawdown = self.get_current_drawdown_percent();
        let max_allowed_drawdown = self.config.max_drawdown_percent;
        max_allowed_drawdown - current_drawdown
    }

    /// 检查是否接近风控线（预警机制）
    pub fn is_approaching_risk_limit(&self) -> bool {
        let risk_buffer = self.get_risk_buffer();
        risk_buffer <= 0.05 // 距离风控线5%以内时预警
    }

    /// 获取维度信息
    ///
    /// 不改变环境状态，安全地返回状态和动作的维度信息
    pub fn get_dim_info(&self) -> DimensionInfo {
        // 获取观测空间信息
        let observation_total_features = self.observation_space.total_features();
        let observation_shape = self.observation_space.shape.clone();
        let feature_names = self.observation_space.feature_names.clone();

        // 获取动作空间信息
        let action_shape = self.action_space.shape();
        let action_dim = match &self.action_space {
            drl_trading_core::types::space::ActionSpace::Discrete { n, .. } => *n,
            drl_trading_core::types::space::ActionSpace::Continuous { shape, .. } => shape.iter().product(),
            drl_trading_core::types::space::ActionSpace::Mixed { discrete, continuous } => {
                // 混合空间的维度计算，这里简化处理
                4 // 默认4个离散动作
            }
            drl_trading_core::types::space::ActionSpace::MultiAsset { assets } => {
                assets.len() * 4 // 每个资产4个动作
            }
        };
        DimensionInfo {
            state_dim: observation_total_features,
            action_dim,
            observation_shape,
            action_shape,
            feature_names,
        }
    }

    /// 获取状态编码器的引用（用于访问观测空间进行标准化）
    pub fn state_encoder(&self) -> &dyn StateEncoder {
        self.state_encoder.as_ref()
    }

    /// 检查终止条件的辅助方法
    fn check_termination_conditions(&mut self, base_reward: f64) -> EnvResult<(f64, bool, String)> {
        let initial_balance = self.config.initial_balance as f64;
        let current_equity = self.account.get_equity_f64();

        // 检查风控终止条件 - 修复：使用正确的回撤计算
        let drawdown_limit_percent = self.config.max_drawdown_percent;

        // 计算当前回撤百分比
        let current_drawdown_percent = if initial_balance > 0.0 {
            (initial_balance - current_equity) / initial_balance
        } else {
            0.0
        };

        // 优先检查：如果权益为负数，立即终止
        if current_equity <= 0.0 && !self.current_state.done {
            self.current_state.done = true;
            let termination_reason = format!(
                "严重风控终止：权益为负数 ({:.2}), 初始资金: {:.2}",
                current_equity,
                initial_balance
            );
            self.current_state.termination_reason = Some(termination_reason.clone());

            let risk_penalty = -1000.0; // 极大的惩罚
            log::error!("🚨 严重风控终止: {}", termination_reason);
            log::error!("💡 权益为负，这是不可接受的，强制终止交易");

            return Ok((base_reward + risk_penalty, true, termination_reason));
        }

        // 修复：当回撤超过限制时立即终止
        if current_drawdown_percent >= drawdown_limit_percent && !self.current_state.done {
            // 风控终止
            self.current_state.done = true;
            let termination_reason = format!(
                "风控终止：总权益触及最大回撤限制 ({:.2}% >= {:.2}%), 当前权益: {:.2}, 初始资金: {:.2}",
                current_drawdown_percent * 100.0,
                drawdown_limit_percent * 100.0,
                current_equity,
                initial_balance
            );
            self.current_state.termination_reason = Some(termination_reason.clone());

            let risk_penalty = -500.0; // 大幅增加风险惩罚
            log::warn!("🚨 风控终止: {}", termination_reason);
            log::info!("💡 风控机制生效，避免了进一步的资金损失，智能体将重新开始学习");

            return Ok((base_reward + risk_penalty, true, termination_reason));
        }
        
        // 检查最大步数终止条件  
        if let Some(max_steps) = self.config.max_steps {
            if self.current_state.current_step >= max_steps && !self.current_state.done {
                self.current_state.done = true;
                let termination_reason = format!("达到最大步数限制: {} 步", max_steps);
                self.current_state.termination_reason = Some(termination_reason.clone());
                log::info!("📈 正常结束：达到最大训练步数");
                
                return Ok((base_reward, true, termination_reason));
            }
        }

        // 未终止的情况
        let info = if self.is_approaching_risk_limit() {
            "风控预警：正在接近风控线".to_string()
        } else {
            format!("步骤 {}: 正常交易中", self.current_state.current_step)
        };
        
        Ok((base_reward, false, info))
    }
}

impl Env for TradingEnvironment {
    type Observation = Observation;
    type Action = ActionType;

    fn step(&mut self, action: ActionType) -> EnvResult<(Self::Observation, f64, bool, String)> {
        // --- 步骤1：递增步数计数器 ---
        self.current_state.current_step += 1;
        
        // --- 步骤2：获取当前时刻t的tick数据（执行价格） ---
        let current_tick = self.data_provider.get_tick().map_err(|e| EnvError::data_provider(e.to_string()))?;
        
        // 保存执行动作前的权益（用于奖励计算）
        self.equity_before_action = self.account.get_equity_f64();
        
        // --- 步骤3：在当前时刻t执行动作 ---
        // 使用当前时刻t的价格来执行交易
        let trade_result = self
            .action_executor
            .execute(&action, &mut self.account, &current_tick, self.current_state.current_step)
            .unwrap_or_else(|e| {
                debug!("动作执行失败: {}", e);
                None
            });
        
        // 执行后，账户的持仓结构已改变，但其市值和浮动盈亏仍是基于t时刻价格计算的
        
        // --- 步骤4：推进时间到t+1，获取下一个时间点的行情数据 ---
        let next_tick = self.data_provider.next_tick().map_err(|e| EnvError::data_provider(e.to_string()))?;
        
        // --- 步骤5：计算奖励 R_t（连接过去与未来） ---
        
        // 更新账户中的价格到t+1时刻（用于正确计算权益）
        // 使用on_tick方法更新价格，但不传入交易（因为交易已经在步骤3中处理）
        if let Err(e) = self.account.on_tick(&next_tick, None) {
            debug!("账户价格更新失败: {}", e);
        }
        let equity_after_action = self.account.get_equity_f64();
        
        let reward = if self.prev_account.is_some() {
            // 使用高级奖励计算器计算完整奖励
            let mut current_prices = std::collections::HashMap::new();
            current_prices.insert(next_tick.symbol.clone(), next_tick.close);
            
            let (full_reward, _components) = self.advanced_reward_calculator.calculate_step_reward(
                self.prev_account.as_ref().unwrap(),
                &self.account,
                &current_prices,
                &action
            );
            full_reward
        } else {
            0.0 // 首步无奖励
        };
        
        // --- 步骤6：生成下一状态的观测 S_{t+1} ---
        // 基于【未来t+1】的行情和【当前】的账户状态，生成新的观测向量
        let observation = self.state_encoder.encode(&next_tick, &self.account)?;
        
        // --- 步骤7：检查终止条件 ---
        let (final_reward, done, info) = self.check_termination_conditions(reward)?;
        
        // --- 步骤8：更新内部状态，为下一次循环做准备 ---
        // 保存当前账户状态供下次奖励计算使用
        self.prev_account = Some(self.account.clone());
        
        // 更新环境状态
        self.current_state.current_market_data = Some(next_tick);
        self.current_state.previous_account_value = equity_after_action;
        if self.current_state.previous_account_value > self.current_state.peak_value {
            self.current_state.peak_value = self.current_state.previous_account_value;
        }
        
        // 累计总奖励
        self.total_reward += final_reward;
        
        // --- 步骤9：返回结果 ---
        Ok((observation, final_reward, done, info))
    }

    fn get_action_mask(&self) -> Vec<bool> {
        // 创建验证上下文
        if let Some(ref market_data) = self.current_state.current_market_data {
            let mut current_prices = std::collections::HashMap::new();
            current_prices.insert(market_data.symbol.clone(), market_data.close);

            let context = ValidationContext {
                account: &self.account,
                current_prices: &current_prices,
                current_symbol: &market_data.symbol,
                current_price: market_data.close,
                timestamp: chrono::Utc::now(),
            };

            self.action_validator.get_action_mask(&context)
        } else {
            // 没有市场数据时，只允许Hold动作
            vec![true, false, false, false] // [Hold, Buy, Sell, Close]
        }
    }

    fn get_valid_actions(&self) -> Vec<Self::Action> {
        // 创建验证上下文
        if let Some(ref market_data) = self.current_state.current_market_data {
            let mut current_prices = std::collections::HashMap::new();
            current_prices.insert(market_data.symbol.clone(), market_data.close);

            let context = ValidationContext {
                account: &self.account,
                current_prices: &current_prices,
                current_symbol: &market_data.symbol,
                current_price: market_data.close,
                timestamp: chrono::Utc::now(),
            };

            self.action_validator.get_valid_actions(&context)
        } else {
            // 没有市场数据时，只允许Hold动作
            vec![ActionType::Hold]
        }
    }

    /// 通过summary 方法通知各个模块输出调试信息
    fn summary(&self) {
        println!("=== 交易环境概要信息 ===");

        // 1. 账户概要（紧凑格式）
        let portfolio_summary = self.account.portfolio.get_summary();
        let account_summary = self.account.get_account_summary_f64();
        
        if portfolio_summary.position_count > 0 {
            println!("\n【账户概要】持仓={}品种 | 权益={:.2} | 余额={:.2} | 已实现={:.2} | 未实现={:.2} | 持仓市值={:.2} USDT", 
                     portfolio_summary.position_count,
                     account_summary.total_equity,
                     account_summary.available_balance,
                     account_summary.realized_pnl,
                     account_summary.unrealized_pnl,
                     portfolio_summary.total_market_value.to_f64().unwrap_or(0.0));
            
            // 详细持仓信息
            for symbol in &portfolio_summary.symbols {
                if let Some(position) = self.account.portfolio.get_position(symbol) {
                    let current_price = self.account.portfolio.get_current_price(symbol);
                    let market_value = self.account.portfolio.get_position_market_value(symbol);
                    let unrealized_pnl = self.account.portfolio.get_position_unrealized_pnl(symbol);

                    let quantity = position.quantity.to_f64().unwrap_or(0.0);
                    let avg_price = position.average_entry_price.to_f64().unwrap_or(0.0);
                    let curr_price = current_price.map(|p| p.to_f64().unwrap_or(0.0)).unwrap_or(0.0);
                    let mkt_value = market_value.map(|v| v.to_f64().unwrap_or(0.0)).unwrap_or(0.0);
                    let unrealized = unrealized_pnl.map(|p| p.to_f64().unwrap_or(0.0)).unwrap_or(0.0);

                    // 修复：检查异常数据并标记
                    let avg_price_display = if avg_price < 0.0 {
                        format!("⚠️{:.2}", avg_price.abs()) // 负均价标记为异常
                    } else {
                        format!("{:.2}", avg_price)
                    };

                    let position_type = if quantity > 0.0 { "多头" } else if quantity < 0.0 { "空头" } else { "空仓" };

                    println!("  └─ {} {}数量={:.6} | 均价={} | 当前价={:.2} | 市值={:.2} | 盈亏={:+.2}",
                        symbol,
                        position_type,
                        quantity.abs(), // 显示绝对数量
                        avg_price_display,
                        curr_price,
                        mkt_value,
                        unrealized
                    );
                }
            }
        } else {
            println!("\n【账户概要】持仓=0 | 权益={:.2} | 余额={:.2} | 已实现={:.2} | 未实现={:.2} USDT", 
                     account_summary.total_equity,
                     account_summary.available_balance,
                     account_summary.realized_pnl,
                     account_summary.unrealized_pnl);
        }
        
        // 2. 交易统计（紧凑格式）
        let action_executor_stats = self.action_executor.get_stats();
        let trade_history = self.account.portfolio.get_trade_history();
        let total_trades = trade_history.len();
        let avg_fee_per_trade = if total_trades > 0 {
            account_summary.total_fees / total_trades as f64
        } else {
            0.0
        };
        
        println!("\n【交易统计】总交易={} | 累计手续费={:.4} | 平均每单={:.4} | 缓存命中率={:.2}% USDT", 
                 total_trades, account_summary.total_fees, avg_fee_per_trade, 
                 action_executor_stats.cache_hit_rate * 100.0);
        
        // 显示最近的成交记录
        if !trade_history.is_empty() {
            println!("  📋 最近成交记录 (最近5笔):");
            let recent_trades = if trade_history.len() > 5 {
                &trade_history[trade_history.len()-5..]
            } else {
                trade_history
            };
            
            for (i, trade) in recent_trades.iter().enumerate() {
                let side_str = match trade.side {
                    drl_trading_core::types::execution::OrderSide::Buy => "买入",
                    drl_trading_core::types::execution::OrderSide::Sell => "卖出",
                };
                println!("    {}. {} {} {:.6} @ {:.2} USDT", 
                        trade_history.len() - recent_trades.len() + i + 1,
                        side_str, 
                        trade.symbol,
                        trade.quantity.to_f64().unwrap_or(0.0),
                        trade.price.to_f64().unwrap_or(0.0)
                );
            }
        } else {
            println!("  📋 暂无成交记录");
        }

        // 4. 高级奖励系统
        println!("\n【高级奖励系统】");
        println!("  🎯 奖励模式: 多层次奖励系统 | 累计奖励: {:.4} | 当前步数: {} | 状态: {}", 
                 self.total_reward, 
                 self.current_state.current_step, 
                 if self.current_state.done { "已结束" } else { "运行中" });
        
        // 显示本回合平均奖励组成
        if let Some(avg_components) = self.advanced_reward_calculator.get_episode_average_reward_components() {
            let step_count = self.advanced_reward_calculator.get_episode_step_count();
            println!("  💰 本回合平均奖励组成({}步): 权益奖励={:.4} | 交易成本={:.4} | 持仓奖励={:.4} | 风险惩罚={:.4} | 总计={:.4}",
                     step_count,
                     avg_components.portfolio_return,
                     avg_components.transaction_cost,
                     avg_components.holding_reward,
                     avg_components.risk_penalty,
                     avg_components.total_reward());
            
            // 显示动作统计
            let (hold_count, buy_count, sell_count, close_count) = self.advanced_reward_calculator.get_episode_action_stats();
            println!("  📊 本回合动作统计: Hold={} | Buy={} | Sell={} | Close={} | 总计={}",
                     hold_count, buy_count, sell_count, close_count,
                     hold_count + buy_count + sell_count + close_count);

            // 显示连续动作统计（使用高级奖励计算器）
            let (mean_action, max_action, min_action, action_count) = self.advanced_reward_calculator.get_continuous_action_stats();
            let total_position_changes = self.advanced_reward_calculator.get_total_position_changes();

            if action_count > 0 {
                println!("  📊 连续动作统计: 平均={:.4} | 最大={:.4} | 最小={:.4} | 动作数={} | 总仓位变化={:.4}",
                    mean_action, max_action, min_action, action_count, total_position_changes);
            }
        } else {
            println!("  💰 奖励组成: 暂无数据");
        }

        // 直接从当前Account获取指标，而不是从缓存的last_account
        let total_trades = self.account.get_trade_amount();
        let realized_pnl = self.account.get_state().realized_pnl.to_f64().unwrap_or(0.0);
        let position_concentration = self.account.get_portfolio().calculate_position_concentration();
        let total_fees = self.account.get_state().total_fees.to_f64().unwrap_or(0.0);

        // 添加调试信息：检查Performance模块状态
        let performance = self.account.get_performance();
        let equity_points_count = performance.get_equity_history().len();
        let equity_history = performance.get_equity_history();
        let performance_initial = performance.get_initial_equity();
        let performance_current = performance.get_current_equity();
        
        // 获取实际账户权益数据（更准确）
        let actual_initial_balance = self.account.get_state().initial_balance;
        let actual_current_equity = self.account.get_equity();
        
        // 权益历史诊断（显示实际账户权益数据）
        // if equity_points_count > 1 {
        //     let first_point = &equity_history[0];
        //     let last_point = &equity_history[equity_points_count - 1];
        //     let actual_return = if actual_initial_balance > rust_decimal::Decimal::ZERO {
        //         ((actual_current_equity - actual_initial_balance) / actual_initial_balance).to_f64().unwrap_or(0.0)
        //     } else {
        //         0.0
        //     };
            // println!("  🔍 权益追踪: 数据点={} | 初始={:.2} | 当前={:.2} | 实际收益={:.4}%", 
            //          equity_points_count,
            //          actual_initial_balance.to_f64().unwrap_or(0.0),
            //          actual_current_equity.to_f64().unwrap_or(0.0),
            //          actual_return * 100.0);
            
            // 添加Performance模块对比信息用于调试
        //     println!("  🔧 Performance对比: P初始={:.2} | P当前={:.2} | P数据点: 首={:.2} 末={:.2}", 
        //              performance_initial.to_f64().unwrap_or(0.0),
        //              performance_current.to_f64().unwrap_or(0.0),
        //              first_point.equity.to_f64().unwrap_or(0.0),
        //              last_point.equity.to_f64().unwrap_or(0.0));
        // } else {
        //     println!("  🔍 权益追踪: 数据点={} | 初始={:.2} | 当前={:.2} | 实际收益=0.0000%", 
        //              equity_points_count,
        //              actual_initial_balance.to_f64().unwrap_or(0.0),
        //              actual_current_equity.to_f64().unwrap_or(0.0));
            
        //     println!("  🔧 Performance对比: P初始={:.2} | P当前={:.2} | 数据点不足", 
        //              performance_initial.to_f64().unwrap_or(0.0),
        //              performance_current.to_f64().unwrap_or(0.0));
        // }
        
        log::debug!(
            "Performance调试信息: 权益数据点数量={}, 交易次数={}, 已实现盈亏={:.2}",
            equity_points_count,
            total_trades,
            realized_pnl
        );

        let risk_metrics = self.account.get_risk_metrics();
        let performance_metrics = self.account.get_performance_metrics();

        // println!("  📊 风险指标: 最大回撤={:.4}% | 当前回撤={:.4}% | 波动率={:.4} | 夏普比率={:.4}", 
        //          risk_metrics.max_drawdown * 100.0, risk_metrics.current_drawdown * 100.0, 
        //          risk_metrics.volatility, risk_metrics.sharpe_ratio);
        println!("  📊 交易指标: 胜率={:.2}% | 盈利因子={:.4} | 持仓集中度={:.4}%", 
                 risk_metrics.win_rate * 100.0, risk_metrics.profit_factor, 
                 risk_metrics.position_concentration * 100.0);

        // 专业风控信息
        let current_drawdown = self.get_current_drawdown_percent();
        let risk_buffer = self.get_risk_buffer();
        let is_approaching_limit = self.is_approaching_risk_limit();
        
        let risk_status = if is_approaching_limit {
            "⚠️预警"
        } else if risk_buffer > 0.1 {
            "✅安全"
        } else {
            "🔶谨慎"
        };
        
        println!("  🛡️ 风控系统: 设置线={:.1}% | 当前回撤={:.2}% | 剩余余量={:.2}% | 状态={}", 
                 self.config.max_drawdown_percent * 100.0, current_drawdown * 100.0, 
                 risk_buffer * 100.0, risk_status);

        if self.current_state.done {
            if let Some(ref reason) = self.current_state.termination_reason {
                if reason.contains("风控终止") {
                    println!("  🚨 终止原因: 触发风控终止");
                }
            }
        }

        println!("  📈 收益分析: 总收益={:.4}% | 年化收益={:.4}% | 总交易={}", 
                 performance_metrics.total_return * 100.0, performance_metrics.annualized_return * 100.0, 
                 performance_metrics.total_trades);
        println!("  📈 交易分析: 盈利交易={} | 亏损交易={} | 平均盈利={:.4} | 平均亏损={:.4} | 最大盈利={:.4} | 最大亏损={:.4}", 
                 performance_metrics.winning_trades, performance_metrics.losing_trades,
                 performance_metrics.average_win, performance_metrics.average_loss,
                 performance_metrics.largest_win, performance_metrics.largest_loss);

        // 配置信息已在上面显示，此处删除重复

        if let Some(ref market_data) = self.current_state.current_market_data {
            println!("  当前市场数据: {} @ {:.2}", market_data.symbol, market_data.close);
        }
        println!("\n========================");
    }

    fn reset(&mut self) -> EnvResult<Self::Observation> {
        // 重置数据提供器到随机起始位置
        self.data_provider.reset();

        // 重置账户（每个episode独立）
        // 创建账户配置
        let account_config = drl_trading_account::AccountConfig {
            initial_balance: rust_decimal::Decimal::from(self.config.initial_balance),
            fee_rate: 0.001,
            slippage: 0.0001,
            leverage: 1.0,
            min_trade_value: rust_decimal::Decimal::from(10),
            max_position_ratio: 1.0,
            base_currency: "USDT".to_string(),
        };
        self.account = Account::new(account_config).map_err(|e| EnvError::account_state(e.to_string()))?;

        // 重置状态（使用高性能f64接口）
        self.current_state = EnvState::default();
        self.current_state.previous_account_value = self.account.get_equity_f64();
        self.current_state.peak_value = self.current_state.previous_account_value;

        // 重置累计奖励
        self.total_reward = 0.0;

        // 重置动作执行器
        self.action_executor.reset();

        // 重置高级奖励计算器
        self.advanced_reward_calculator.reset();

        // 高级奖励计算器已经在上面重置了

        // 重置前一个账户状态
        self.prev_account = None;

        // 重置执行前权益
        self.equity_before_action = self.account.get_equity_f64();

        // 获取初始市场数据
        match self.data_provider.next_tick() {
            Ok(tick) => {
                self.current_state.current_market_data = Some(tick.clone());
                let observation = self.state_encoder.encode(&tick, &self.account)?;
                debug!("环境重置完成，初始观测特征数: {}", observation.total_features());
                Ok(observation)
            }
            Err(e) => Err(EnvError::data_provider(format!("无法获取初始市场数据: {}", e))),
        }
    }

    fn get_observation(&mut self) -> EnvResult<Self::Observation> {
        if let Some(ref market_data) = self.current_state.current_market_data {
            self.state_encoder.encode(market_data, &self.account)
        } else {
            Err(EnvError::invalid_state("当前没有可用的市场数据"))
        }
    }

    fn action_space(&self) -> &ActionSpace {
        &self.action_space
    }

    fn observation_space(&self) -> &ObservationSpace {
        &self.observation_space
    }

    fn is_done(&self) -> bool {
        self.current_state.done
    }

    fn get_step_count(&self) -> usize {
        self.current_state.current_step
    }

    fn get_total_reward(&self) -> f64 {
        self.total_reward
    }

    fn set_seed(&mut self, seed: u64) {
        self.rng = ChaCha8Rng::seed_from_u64(seed);
        self.config.seed = Some(seed);
    }

    fn save_state(&self, path: &str) -> EnvResult<()> {
        let state_data = serde_json::to_string_pretty(&self.current_state).map_err(|e| EnvError::serialization(e.to_string()))?;

        fs::write(path, state_data).map_err(|e| EnvError::file_operation(e.to_string()))?;

        info!("环境状态已保存到: {}", path);
        Ok(())
    }

    fn load_state(&mut self, path: &str) -> EnvResult<()> {
        let state_data = fs::read_to_string(path).map_err(|e| EnvError::file_operation(e.to_string()))?;

        self.current_state = serde_json::from_str(&state_data).map_err(|e| EnvError::serialization(e.to_string()))?;

        info!("环境状态已从 {} 加载", path);
        Ok(())
    }

    fn render(&self, mode: RenderMode) -> EnvResult<()> {
        match mode {
            RenderMode::None => Ok(()),
            RenderMode::Console => {
                println!("=== 交易环境状态 ===");
                println!("步数: {}", self.current_state.current_step);
                println!("账户价值: {:.2}", self.current_state.previous_account_value);
                println!("累计奖励: {:.4}", self.total_reward);

                if let Some(ref reason) = self.current_state.termination_reason {
                    println!("终止原因: {}", reason);
                }
                println!("==================");
                Ok(())
            }
            RenderMode::Log => {
                info!(
                    "环境状态 - 步数: {}, 价值: {:.2}, 奖励: {:.4}",
                    self.current_state.current_step, self.current_state.previous_account_value, self.total_reward
                );
                Ok(())
            }
            RenderMode::Graphic => {
                warn!("图形渲染模式尚未实现");
                Ok(())
            }
            RenderMode::File(_append) => {
                warn!("文件渲染模式尚未实现");
                Ok(())
            }
        }
    }

    fn close(&mut self) -> EnvResult<()> {
        info!("关闭环境");
        // TODO: 实现资源清理逻辑
        // 例如，关闭数据提供器、执行处理器等
        // self.data_provider.close()?;
        // self.execution_handler.close()?;
        Ok(())
    }
}
