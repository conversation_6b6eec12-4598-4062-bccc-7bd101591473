//! # 技术指标计算器
//! 
//! 提供常见的技术分析指标计算功能，用于增强环境观测维度

use ta::indicators::{
    // 趋势指标
    SimpleMovingAverage, ExponentialMovingAverage,
    // 震荡指标
    RelativeStrengthIndex, FastStochastic, SlowStochastic,
    MovingAverageConvergenceDivergence, PercentagePriceOscillator,
    CommodityChannelIndex, MoneyFlowIndex,
    // 其他指标
    BollingerBands, StandardDeviation, AverageTrueRange,
    EfficiencyRatio, RateOfChange, OnBalanceVolume,
    Maximum, Minimum, MeanAbsoluteDeviation
};
use ta::{Next, Reset, DataItem};
use drl_trading_core::Tick;
use std::collections::VecDeque;

/// 技术指标计算器
/// 
/// 管理所有技术指标的计算和状态维护
#[derive(Debug, Clone)]
pub struct TechnicalIndicatorCalculator {
    /// 历史数据窗口大小
    window_size: usize,
    /// 历史价格数据缓存
    price_history: VecDeque<DataItem>,
    
    // 趋势指标
    sma_5: SimpleMovingAverage,
    sma_10: SimpleMovingAverage,
    sma_20: SimpleMovingAverage,
    ema_5: ExponentialMovingAverage,
    ema_10: ExponentialMovingAverage,
    ema_20: ExponentialMovingAverage,
    
    // 震荡指标
    rsi_14: RelativeStrengthIndex,
    stoch_fast_14: FastStochastic,
    stoch_slow_14: SlowStochastic,
    macd: MovingAverageConvergenceDivergence,
    ppo: PercentagePriceOscillator,
    cci_20: CommodityChannelIndex,
    mfi_14: MoneyFlowIndex,
    
    // 波动性指标
    bb_20: BollingerBands,
    atr_14: AverageTrueRange,
    std_dev_20: StandardDeviation,
    
    // 其他指标
    roc_10: RateOfChange,
    obv: OnBalanceVolume,
    er_14: EfficiencyRatio,
    max_14: Maximum,
    min_14: Minimum,
    mad_14: MeanAbsoluteDeviation,
}

impl TechnicalIndicatorCalculator {
    /// 创建新的技术指标计算器
    pub fn new(window_size: usize) -> Result<Self, Box<dyn std::error::Error>> {
        Ok(Self {
            window_size: window_size.max(50), // 确保窗口足够大
            price_history: VecDeque::new(),
            
            // 趋势指标
            sma_5: SimpleMovingAverage::new(5)?,
            sma_10: SimpleMovingAverage::new(10)?,
            sma_20: SimpleMovingAverage::new(20)?,
            ema_5: ExponentialMovingAverage::new(5)?,
            ema_10: ExponentialMovingAverage::new(10)?,
            ema_20: ExponentialMovingAverage::new(20)?,
            
            // 震荡指标
            rsi_14: RelativeStrengthIndex::new(14)?,
            stoch_fast_14: FastStochastic::new(14)?,
            stoch_slow_14: SlowStochastic::new(14, 3)?,
            macd: MovingAverageConvergenceDivergence::new(12, 26, 9)?,
            ppo: PercentagePriceOscillator::new(12, 26, 9)?,
            cci_20: CommodityChannelIndex::new(20)?,
            mfi_14: MoneyFlowIndex::new(14)?,
            
            // 波动性指标
            bb_20: BollingerBands::new(20, 2.0)?,
            atr_14: AverageTrueRange::new(14)?,
            std_dev_20: StandardDeviation::new(20)?,
            
            // 其他指标
            roc_10: RateOfChange::new(10)?,
            obv: OnBalanceVolume::new(),
            er_14: EfficiencyRatio::new(14)?,
            max_14: Maximum::new(14)?,
            min_14: Minimum::new(14)?,
            mad_14: MeanAbsoluteDeviation::new(14)?,
        })
    }
    
    /// 更新所有指标并返回计算结果
    pub fn update(&mut self, tick: &Tick) -> Vec<f64> {
        // 创建DataItem
        let data_item = DataItem::builder()
            .open(tick.open)
            .high(tick.high)
            .low(tick.low)
            .close(tick.close)
            .volume(tick.volume)
            .build()
            .unwrap_or_else(|_| {
                DataItem::builder()
                    .open(tick.close)
                    .high(tick.close)
                    .low(tick.close)
                    .close(tick.close)
                    .volume(0.0)
                    .build()
                    .unwrap()
            });
        
        // 添加到历史数据
        self.price_history.push_back(data_item.clone());
        if self.price_history.len() > self.window_size {
            self.price_history.pop_front();
        }
        
        let mut features = Vec::with_capacity(28); // 预分配空间
        
        // 更新趋势指标
        features.push(self.sma_5.next(tick.close));
        features.push(self.sma_10.next(tick.close));
        features.push(self.sma_20.next(tick.close));
        features.push(self.ema_5.next(tick.close));
        features.push(self.ema_10.next(tick.close));
        features.push(self.ema_20.next(tick.close));
        
        // 更新震荡指标
        features.push(self.rsi_14.next(&data_item));
        
        let stoch_fast = self.stoch_fast_14.next(&data_item);
        features.push(stoch_fast);
        features.push(stoch_fast); // 快速随机指标使用相同值代替K和D
        
        let stoch_slow = self.stoch_slow_14.next(&data_item);
        features.push(stoch_slow);
        features.push(stoch_slow); // 慢速随机指标使用相同值代替K和D
        
        let macd_result = self.macd.next(tick.close);
        features.push(macd_result.macd);
        features.push(macd_result.signal);
        features.push(macd_result.histogram);
        
        let ppo_result = self.ppo.next(tick.close);
        features.push(ppo_result.ppo);
        features.push(ppo_result.signal);
        features.push(ppo_result.histogram);
        
        features.push(self.cci_20.next(&data_item));
        features.push(self.mfi_14.next(&data_item));
        
        // 更新波动性指标
        let bb_result = self.bb_20.next(tick.close);
        features.push(bb_result.average);
        features.push(bb_result.upper);
        features.push(bb_result.lower);
        
        features.push(self.atr_14.next(&data_item));
        features.push(self.std_dev_20.next(tick.close));
        
        // 更新其他指标
        features.push(self.roc_10.next(tick.close));
        features.push(self.obv.next(&data_item));
        features.push(self.er_14.next(tick.close));
        features.push(self.max_14.next(tick.close));
        features.push(self.min_14.next(tick.close));
        features.push(self.mad_14.next(tick.close));
        
        features
    }
    
    /// 获取技术指标特征名称
    pub fn get_feature_names() -> Vec<String> {
        vec![
            // 趋势指标 (6个)
            "sma_5".to_string(), "sma_10".to_string(), "sma_20".to_string(),
            "ema_5".to_string(), "ema_10".to_string(), "ema_20".to_string(),
            
            // 震荡指标 (13个)
            "rsi_14".to_string(),
            "stoch_fast_k".to_string(), "stoch_fast_d".to_string(),
            "stoch_slow_k".to_string(), "stoch_slow_d".to_string(),
            "macd".to_string(), "macd_signal".to_string(), "macd_histogram".to_string(),
            "ppo".to_string(), "ppo_signal".to_string(), "ppo_histogram".to_string(),
            "cci_20".to_string(), "mfi_14".to_string(),
            
            // 波动性指标 (5个)
            "bb_middle".to_string(), "bb_upper".to_string(), "bb_lower".to_string(),
            "atr_14".to_string(), "std_dev_20".to_string(),
            
            // 其他指标 (6个)
            "roc_10".to_string(), "obv".to_string(), "er_14".to_string(),
            "max_14".to_string(), "min_14".to_string(), "mad_14".to_string(),
        ]
    }
    
    /// 获取技术指标特征数量
    pub fn get_feature_count() -> usize {
        30 // 总共30个技术指标特征
    }
    
    /// 重置所有指标
    pub fn reset(&mut self) {
        self.price_history.clear();
        
        // 重置趋势指标
        self.sma_5.reset();
        self.sma_10.reset();
        self.sma_20.reset();
        self.ema_5.reset();
        self.ema_10.reset();
        self.ema_20.reset();
        
        // 重置震荡指标
        self.rsi_14.reset();
        self.stoch_fast_14.reset();
        self.stoch_slow_14.reset();
        self.macd.reset();
        self.ppo.reset();
        self.cci_20.reset();
        self.mfi_14.reset();
        
        // 重置波动性指标
        self.bb_20.reset();
        self.atr_14.reset();
        self.std_dev_20.reset();
        
        // 重置其他指标
        self.roc_10.reset();
        self.obv.reset();
        self.er_14.reset();
        self.max_14.reset();
        self.min_14.reset();
        self.mad_14.reset();
    }
}

/// 技术指标边界定义
pub struct TechnicalIndicatorBounds;

impl TechnicalIndicatorBounds {
    /// 获取技术指标的合理边界范围
    pub fn get_bounds() -> (Vec<f64>, Vec<f64>) {
        let mut low_bounds = Vec::new();
        let mut high_bounds = Vec::new();
        
        // 趋势指标边界 (价格相关，0-100000)
        for _ in 0..6 {
            low_bounds.push(0.0);
            high_bounds.push(100000.0);
        }
        
        // 震荡指标边界
        low_bounds.push(0.0); high_bounds.push(100.0);    // RSI (0-100)
        low_bounds.push(0.0); high_bounds.push(100.0);    // Stoch Fast K (0-100)
        low_bounds.push(0.0); high_bounds.push(100.0);    // Stoch Fast D (0-100)
        low_bounds.push(0.0); high_bounds.push(100.0);    // Stoch Slow K (0-100)
        low_bounds.push(0.0); high_bounds.push(100.0);    // Stoch Slow D (0-100)
        low_bounds.push(-1000.0); high_bounds.push(1000.0); // MACD
        low_bounds.push(-1000.0); high_bounds.push(1000.0); // MACD Signal
        low_bounds.push(-1000.0); high_bounds.push(1000.0); // MACD Histogram
        low_bounds.push(-100.0); high_bounds.push(100.0);   // PPO
        low_bounds.push(-100.0); high_bounds.push(100.0);   // PPO Signal
        low_bounds.push(-100.0); high_bounds.push(100.0);   // PPO Histogram
        low_bounds.push(-300.0); high_bounds.push(300.0);   // CCI
        low_bounds.push(0.0); high_bounds.push(100.0);      // MFI (0-100)
        
        // 波动性指标边界
        for _ in 0..3 { // BB Middle, Upper, Lower (价格相关)
            low_bounds.push(0.0);
            high_bounds.push(100000.0);
        }
        low_bounds.push(0.0); high_bounds.push(10000.0);    // ATR
        low_bounds.push(0.0); high_bounds.push(10000.0);    // Std Dev
        
        // 其他指标边界
        low_bounds.push(-100.0); high_bounds.push(100.0);   // ROC
        low_bounds.push(-1000000.0); high_bounds.push(1000000.0); // OBV
        low_bounds.push(0.0); high_bounds.push(1.0);        // ER (0-1)
        low_bounds.push(0.0); high_bounds.push(100000.0);   // Max (价格)
        low_bounds.push(0.0); high_bounds.push(100000.0);   // Min (价格)
        low_bounds.push(0.0); high_bounds.push(10000.0);    // MAD
        
        (low_bounds, high_bounds)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    /// 创建测试用的Tick数据
    fn create_test_tick(symbol: &str, open: f64, high: f64, low: f64, close: f64, volume: f64) -> Tick {
        Tick {
            symbol: drl_trading_core::Symbol(symbol.to_string()),
            open,
            high,
            low,
            close,
            volume,
            timestamp: chrono::Utc::now().timestamp_millis(),
        }
    }
    
    #[test]
    fn test_indicator_calculator_creation() {
        let calculator = TechnicalIndicatorCalculator::new(50);
        assert!(calculator.is_ok());
        
        let calculator = calculator.unwrap();
        assert_eq!(calculator.window_size, 50);
    }
    
    #[test]
    fn test_feature_count() {
        assert_eq!(TechnicalIndicatorCalculator::get_feature_count(), 30);
        let feature_names = TechnicalIndicatorCalculator::get_feature_names();
        assert_eq!(feature_names.len(), 30);
    }
    
    #[test]
    fn test_bounds_consistency() {
        let (low_bounds, high_bounds) = TechnicalIndicatorBounds::get_bounds();
        
        // 边界数量应该与特征数量一致
        assert_eq!(low_bounds.len(), TechnicalIndicatorCalculator::get_feature_count());
        assert_eq!(high_bounds.len(), TechnicalIndicatorCalculator::get_feature_count());
        
        // 所有low应该小于等于high
        for (low, high) in low_bounds.iter().zip(high_bounds.iter()) {
            assert!(low <= high, "low: {}, high: {}", low, high);
        }
    }
    
    #[test]
    fn test_indicator_update_basic() {
        let mut calculator = TechnicalIndicatorCalculator::new(50).unwrap();
        
        // 创建一系列测试数据
        let test_data = vec![
            (100.0, 105.0, 98.0, 102.0, 1000.0),
            (102.0, 108.0, 100.0, 106.0, 1100.0),
            (106.0, 110.0, 104.0, 108.0, 1200.0),
            (108.0, 112.0, 106.0, 110.0, 1300.0),
            (110.0, 115.0, 108.0, 113.0, 1400.0),
        ];
        
        for (i, (open, high, low, close, volume)) in test_data.iter().enumerate() {
            let tick = create_test_tick("BTC-USDT", *open, *high, *low, *close, *volume);
            let features = calculator.update(&tick);
            
            // 验证特征数量
            assert_eq!(features.len(), 30);
            
            // 验证所有特征都是有限数值
            for (j, &feature) in features.iter().enumerate() {
                assert!(feature.is_finite(), 
                       "第{}次更新，特征{}不是有限数值: {}", i, j, feature);
            }
            
            println!("第{}次更新，前6个趋势指标: {:?}", i, &features[0..6]);
        }
    }
    
    #[test]
    fn test_indicator_reset() {
        let mut calculator = TechnicalIndicatorCalculator::new(50).unwrap();
        
        // 更新一些数据
        let tick = create_test_tick("BTC-USDT", 100.0, 105.0, 98.0, 102.0, 1000.0);
        let features_before = calculator.update(&tick);
        
        // 重置指标
        calculator.reset();
        
        // 再次更新相同数据
        let features_after = calculator.update(&tick);
        
        // 重置后的结果应该与第一次相同
        assert_eq!(features_before.len(), features_after.len());
        
        // 某些指标在重置后应该产生相同的初始值
        assert_eq!(features_before[0], features_after[0]); // SMA_5
    }
    
    #[test]
    fn test_specific_indicators() {
        let mut calculator = TechnicalIndicatorCalculator::new(50).unwrap();
        
        // 创建递增的价格序列测试趋势指标
        for i in 1..=25 {
            let price = 100.0 + i as f64;
            let tick = create_test_tick("BTC-USDT", price, price + 2.0, price - 1.0, price + 1.0, 1000.0);
            let features = calculator.update(&tick);
            
            // 验证一些基本的指标特性
            if i >= 5 {
                // SMA_5应该存在且合理
                let sma_5 = features[0];
                assert!(sma_5 > 0.0 && sma_5 < 200.0, "SMA_5异常: {}", sma_5);
                
                // EMA_5应该存在且合理
                let ema_5 = features[3];
                assert!(ema_5 > 0.0 && ema_5 < 200.0, "EMA_5异常: {}", ema_5);
            }
            
            if i >= 14 {
                // RSI应该在0-100范围内
                let rsi = features[6];
                assert!(rsi >= 0.0 && rsi <= 100.0, "RSI超出范围: {}", rsi);
            }
        }
    }
    
    #[test]
    fn test_feature_names_mapping() {
        let feature_names = TechnicalIndicatorCalculator::get_feature_names();
        
        // 验证特征名称的正确性
        assert_eq!(feature_names[0], "sma_5");
        assert_eq!(feature_names[1], "sma_10");
        assert_eq!(feature_names[2], "sma_20");
        assert_eq!(feature_names[6], "rsi_14");
        
        // 验证没有重复的特征名称
        let mut unique_names = std::collections::HashSet::new();
        for name in &feature_names {
            assert!(unique_names.insert(name.clone()), "重复的特征名称: {}", name);
        }
    }
} 