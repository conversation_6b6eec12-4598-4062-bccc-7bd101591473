//! # 动作执行器
//! 
//! 负责将环境动作转换为具体的交易订单
//! 
//! **设计原则：**
//! - 专注核心职责：动作转换为订单
//! - 不缓存任何状态信息，直接使用Account模块
//! - 保持简单高效

use drl_trading_core::{Order, Symbol, ActionType};
use drl_trading_account::Account;
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;

use crate::errors::{EnvError, EnvResult};

/// 动作执行器配置
#[derive(Debug, Clone)]
pub struct ActionExecutorConfig {
    /// 默认买入数量
    pub default_buy_quantity: f64,
    /// 默认卖出数量
    pub default_sell_quantity: f64,
    /// 最小交易数量
    pub min_trade_quantity: Decimal,
    /// 基础资金（用于仓位比例计算）
    pub base_capital: Decimal,
    /// 最小仓位变化阈值（避免频繁小额交易）
    pub min_position_change: f64,
}

impl Default for ActionExecutorConfig {
    fn default() -> Self {
        Self {
            default_buy_quantity: 2.0,   // 默认买入2个单位
            default_sell_quantity: 2.0,  // 默认卖出2个单位
            min_trade_quantity: Decimal::new(1, 8), // 0.********
            base_capital: Decimal::new(100000, 0), // 默认10万基础资金
            min_position_change: 0.01,   // 最小1%仓位变化
        }
    }
}

/// 动作执行器
/// 
/// 专注于将环境动作转换为具体订单
pub struct ActionExecutor {
    config: ActionExecutorConfig,
    order_counter: u64,
}

impl ActionExecutor {
    /// 创建新的动作执行器
    pub fn new(config: ActionExecutorConfig) -> Self {
        Self {
            config,
            order_counter: 0,
        }
    }

    /// 使用默认配置创建
    pub fn default() -> Self {
        Self::new(ActionExecutorConfig::default())
    }
    
    /// 执行动作的完整流程（推荐使用此方法）
    /// 
    /// 包含动作转换、Trade创建、账户更新的完整逻辑，简化调用方代码
    pub fn execute(
        &mut self, 
        action: &ActionType, 
        account: &mut Account, 
        tick: &drl_trading_core::Tick, 
        step: usize
    ) -> EnvResult<Option<drl_trading_core::trade::Trade>> {
        use drl_trading_core::trade::Trade;
        use rust_decimal::Decimal;
        
        match self.convert_action_to_order(action, account, &tick.symbol, tick.close, step) {
            Ok(Some(order)) => {
                // 创建Trade记录
                let price = order.price.unwrap_or_else(|| 
                    drl_trading_core::Price::try_from(tick.close).unwrap()
                );
                let trade = Trade {
                    id: order.id.clone(),
                    order_id: order.id,
                    symbol: order.symbol,
                    side: order.side,
                    quantity: order.quantity,
                    price,
                    fee: Some(order.quantity * price * Decimal::new(1, 3)), // 0.1% 手续费
                    timestamp: order.created_at,
                };

                // 通过Account的on_tick方法处理交易和价格更新
                if let Err(e) = account.on_tick(tick, Some(trade.clone())) {
                    log::debug!("账户更新失败: {}", e);
                }
                Ok(Some(trade))
            }
            Ok(None) => {
                // Hold动作，无需执行订单，但需要更新价格
                log::debug!("Hold动作，仅更新价格");
                if let Err(e) = account.on_tick(tick, None) {
                    log::debug!("账户价格更新失败: {}", e);
                }
                Ok(None)
            }
            Err(e) => {
                log::debug!("动作转换失败: {}", e);
                // 即使动作转换失败，也要更新价格
                if let Err(e) = account.on_tick(tick, None) {
                    log::debug!("账户价格更新失败: {}", e);
                }
                Err(e)
            }
        }
    }

    /// 将动作转换为订单
    pub fn convert_action_to_order(
        &mut self,
        action: &ActionType,
        account: &Account,
        symbol: &Symbol,
        current_price: f64,
        step: usize,
    ) -> EnvResult<Option<Order>> {
        // 生成订单ID和时间戳
        self.order_counter += 1;
        let order_id = format!("ACT_{:06}_{:06}", step, self.order_counter);
        let timestamp = chrono::Utc::now().timestamp_millis();
        let price_decimal = Decimal::try_from(current_price)
            .map_err(|e| EnvError::action(format!("价格转换失败: {}", e)))?;

        let order = match action {
            ActionType::Hold => return Ok(None), // Hold不生成订单

            ActionType::Buy => {
                let quantity = Decimal::try_from(self.config.default_buy_quantity)
                    .map_err(|e| EnvError::action(format!("数量转换失败: {}", e)))?;
                
                let required_balance = quantity * price_decimal;
                
                // 检查可用余额是否足够
                if account.state.available_balance < required_balance {
                    return Err(EnvError::action(
                        format!("可用余额不足：{:.2} < {:.2}",
                                account.state.available_balance.to_f64().unwrap_or(0.0),
                                required_balance.to_f64().unwrap_or(0.0))
                    ));
                }

                if quantity < self.config.min_trade_quantity {
                    return Err(EnvError::action(
                        format!("交易数量过小：{} < {}", quantity, self.config.min_trade_quantity)
                    ));
                }

                Order::limit_buy(order_id, symbol.clone(), quantity, price_decimal, timestamp)
            }

            ActionType::Sell => {
                // 直接从Account获取当前持仓
                let current_position = account.get_portfolio()
                    .get_position(symbol)
                    .map(|p| p.quantity)
                    .unwrap_or(Decimal::ZERO);
                
                // 检查持仓
                if current_position <= Decimal::ZERO {
                    return Err(EnvError::action("无持仓可卖出".to_string()));
                }

                // 计算卖出数量：默认数量与现有持仓的较小值
                let target_sell_quantity = Decimal::try_from(self.config.default_sell_quantity)
                    .map_err(|e| EnvError::action(format!("数量转换失败: {}", e)))?;
                let sell_quantity = current_position.min(target_sell_quantity);

                if sell_quantity < self.config.min_trade_quantity {
                    return Err(EnvError::action(
                        format!("卖出数量过小：{} < {}", sell_quantity, self.config.min_trade_quantity)
                    ));
                }

                Order::limit_sell(order_id, symbol.clone(), sell_quantity, price_decimal, timestamp)
            }

            ActionType::Close => {
                // 直接从Account获取当前持仓
                let current_position = account.get_portfolio()
                    .get_position(symbol)
                    .map(|p| p.quantity)
                    .unwrap_or(Decimal::ZERO);

                // 检查是否有持仓需要平仓
                if current_position.is_zero() {
                    return Err(EnvError::action("无持仓可平仓".to_string()));
                }

                // 根据持仓方向创建相反订单
                if current_position.is_sign_positive() {
                    // 多头持仓，创建卖出订单
                    Order::limit_sell(order_id, symbol.clone(), current_position, price_decimal, timestamp)
                } else {
                    // 空头持仓，创建买入订单
                    Order::limit_buy(order_id, symbol.clone(), current_position.abs(), price_decimal, timestamp)
                }
            }

            ActionType::ContinuousPosition { position_ratio } => {
                // 处理连续仓位动作
                self.handle_continuous_position_action(
                    *position_ratio,
                    account,
                    symbol,
                    price_decimal,
                    order_id,
                    timestamp,
                )?
            }

            ActionType::Complex { .. } => {
                return Err(EnvError::action("暂不支持复杂动作类型".to_string()));
            }
        };

        Ok(Some(order))
    }

    /// 处理连续仓位动作（重构版本，使用Account的新方法）
    ///
    /// 根据目标仓位比例计算需要的交易量并生成订单
    fn handle_continuous_position_action(
        &self,
        target_position_ratio: f64,
        account: &Account,
        symbol: &Symbol,
        current_price: Decimal,
        order_id: String,
        timestamp: i64,
    ) -> EnvResult<Order> {
        // 使用Account的方法计算需要的交易量
        let trade_quantity = account.calculate_trade_quantity_for_ratio(
            symbol,
            target_position_ratio,
            current_price,
        );

        // 检查是否应该执行交易
        if !account.should_execute_trade(trade_quantity, current_price) {
            return Err(EnvError::action(format!(
                "交易量过小，忽略。需要交易: {}, 最小交易价值: {}",
                trade_quantity, account.get_config().min_trade_value
            )));
        }

        // 获取当前仓位比例用于日志
        let current_ratio = account.get_position_ratio(symbol, current_price);

        // 生成订单
        let order = if trade_quantity.is_sign_positive() {
            // 需要买入
            Order::limit_buy(order_id, symbol.clone(), trade_quantity, current_price, timestamp)
        } else {
            // 需要卖出
            Order::limit_sell(order_id, symbol.clone(), trade_quantity.abs(), current_price, timestamp)
        };

        log::debug!(
            "连续仓位动作: 目标比例={:.2}%, 当前比例={:.2}%, 交易量={}",
            target_position_ratio * 100.0, current_ratio * 100.0, trade_quantity
        );

        Ok(order)
    }

    /// 获取执行器统计信息
    pub fn get_stats(&self) -> ActionExecutorStats {
        ActionExecutorStats {
            total_orders_generated: self.order_counter,
            config: self.config.clone(),
            cache_hit_rate: 0.0, // 不再使用缓存
        }
    }

    /// 重置执行器状态
    pub fn reset(&mut self) {
        self.order_counter = 0;
    }
}

/// 动作执行器统计信息
#[derive(Debug, Clone)]
pub struct ActionExecutorStats {
    /// 生成的订单总数
    pub total_orders_generated: u64,
    /// 当前配置
    pub config: ActionExecutorConfig,
    /// 缓存命中率（已废弃，保持兼容性）
    pub cache_hit_rate: f64,
}