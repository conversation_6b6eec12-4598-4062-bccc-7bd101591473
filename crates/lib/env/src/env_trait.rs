//! # 环境特征（Traits）定义
//!
//! 定义了交易环境及其所有组件的核心接口。

use crate::errors::EnvResult;
use drl_trading_account::Account;
use drl_trading_core::trade::Trade;
use drl_trading_core::types::space::{ActionSpace, ActionType, Observation, ObservationSpace};
use drl_trading_core::{Order, Tick};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 环境信息
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct EnvInfo {
    pub total_steps: usize,
    pub total_episodes: usize,
    pub current_step: usize,
    pub current_episode: usize,
    pub total_reward: f64,
    pub episode_reward: f64,
    pub step_count: usize,
    pub net_value: f64,
    pub max_drawdown: f64,
    pub win_rate: f64,
    pub current_positions: HashMap<drl_trading_core::Symbol, f64>,
    pub custom_metrics: HashMap<String, f64>,
}

/// 执行统计信息
#[derive(Debug, <PERSON>lone, Default)]
pub struct ExecutionStats {
    /// 总交易次数
    pub total_trades: usize,

    /// 盈利交易次数
    pub profitable_trades: usize,

    /// 亏损交易次数
    pub losing_trades: usize,

    /// 总盈利
    pub total_profit: f64,

    /// 总亏损
    pub total_loss: f64,

    /// 平均持仓时间（分钟）
    pub avg_holding_time: f64,

    /// 最大单笔盈利
    pub max_profit: f64,

    /// 最大单笔亏损
    pub max_loss: f64,
}

/// 交易环境核心接口
pub trait Env {
    type Observation;
    type Action;

    /// 执行一个动作，返回（观测，奖励）
    fn step(&mut self, action: Self::Action) -> EnvResult<(Self::Observation, f64, bool, String)>;

    /// 获取当前状态下的动作掩码
    fn get_action_mask(&self) -> Vec<bool>;

    /// 获取所有合法动作
    fn get_valid_actions(&self) -> Vec<Self::Action>;

    fn summary(&self);

    /// 重置环境
    fn reset(&mut self) -> EnvResult<Self::Observation>;

    /// 获取当前观测
    fn get_observation(&mut self) -> EnvResult<Self::Observation>;

    /// 获取动作空间
    fn action_space(&self) -> &ActionSpace;

    /// 获取观测空间
    fn observation_space(&self) -> &ObservationSpace;

    /// 检查环境是否结束
    fn is_done(&self) -> bool;

    /// 获取当前步数
    fn get_step_count(&self) -> usize;

    /// 获取累计奖励
    fn get_total_reward(&self) -> f64;

    /// 设置随机种子
    fn set_seed(&mut self, seed: u64);

    /// 保存环境状态
    fn save_state(&self, path: &str) -> EnvResult<()>;

    /// 加载环境状态
    fn load_state(&mut self, path: &str) -> EnvResult<()>;

    /// 渲染环境（可选）
    fn render(&self, mode: RenderMode) -> EnvResult<()>;

    /// 关闭环境
    fn close(&mut self) -> EnvResult<()>;
}

/// 数据提供器接口
pub trait DataProvider: Send + Sync {
    /// 获取下一个数据
    fn next_tick(&mut self) -> EnvResult<Tick>;
}

/// 交易执行处理器接口
pub trait ExecutionHandler: Send + Sync {
    /// 执行订单
    fn execute_order(&mut self, order: Order) -> EnvResult<Trade>;
    /// 接收市场tick
    fn on_tick(&mut self, tick: &Tick) -> EnvResult<()>;
}

/// 状态编码器接口
pub trait StateEncoder: Send + Sync {
    /// 将原始市场数据和账户状态编码为观测向量
    fn encode(&mut self, tick: &Tick, account: &Account) -> EnvResult<Observation>;

    /// 返回观测空间
    fn observation_space(&self) -> &ObservationSpace;
}

/// 渲染模式
#[derive(Debug, Clone, Copy)]
pub enum RenderMode {
    None,
    Console,
    File(bool), // true表示追加，false表示覆盖
    Log,
    Graphic,
}

/// 环境监听器接口
pub trait EnvListener: Send + Sync {
    /// 在环境重置时调用
    fn on_reset(&self, initial_observation: &Observation) -> EnvResult<()>;

    /// 在每个步骤结束时调用
    fn on_step(&self, observation: &Observation, action: &ActionType, reward: f64, done: bool, info: &EnvInfo) -> EnvResult<()>;

    /// 在环境结束时调用
    fn on_done(&self, final_info: &EnvInfo) -> EnvResult<()>;

    /// 在发生错误时调用
    fn on_error(&self, error: &crate::errors::EnvError) -> EnvResult<()>;
}
