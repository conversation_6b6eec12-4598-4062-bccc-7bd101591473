[package]
name = "drl-trading-env"
version = "0.1.0"
edition = "2021"
authors = ["DRL Trading Team"]
description = "深度强化学习交易环境模块"
license = "MIT"

[dependencies]
# 基础依赖
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"
log = "0.4"
thiserror = "1.0"

# 内部依赖
drl-trading-core = { path = "../core" }
drl-trading-data = { path = "../data" }
drl-trading-account = { path = "../account" }
drl-trading-learn = { path = "../learn" }

# 深度学习框架
burn = { version = "0.17", features = ["default"] }

# 配置文件处理
toml = "0.8"

# 随机数生成
rand = "0.8"
rand_chacha = "0.3"

# 数学运算
nalgebra = "0.32"
rust_decimal = { version = "1.32", features = ["serde-with-str"] }

# 技术分析指标
ta = "0.5.0"