//! # 历史数据提供模块
//!
//! 从CSV等历史文件中读取市场数据，并作为数据提供者。

use crate::errors::DataError;
use crate::provider::DataProvider;
use anyhow::{Context, Result};
use csv::ReaderBuilder;
use drl_trading_core::{Symbol, Tick};
use std::collections::VecDeque;
use std::fs::File;
use std::path::Path;

/// 历史数据提供者
///
/// 从CSV文件中加载所有OHLCV数据到内存中，并逐条提供。
/// 专为训练性能优化：数据预加载到内存，无I/O开销
pub struct HistoricalProvider {
    symbol: Symbol,
    data: VecDeque<Tick>,
    current_index: usize,
    description: String,
    steps_per_episode: usize,
}

impl HistoricalProvider {
    /// 从CSV文件创建新的HistoricalProvider
    ///
    /// # 性能优化
    /// - 一次性加载所有数据到内存
    /// - 避免训练过程中的文件I/O
    ///
    /// # CSV格式要求
    /// 标准格式：symbol,timestamp,open,high,low,close,volume
    /// 或简化格式：timestamp,open,high,low,close,volume（自动使用传入的symbol）
    pub fn from_csv<P: AsRef<Path>>(path: P, symbol: Symbol, steps_per_episode: usize) -> Result<Self> {
        let file_path = path.as_ref();
        let file = File::open(file_path).with_context(|| format!("无法打开CSV文件: {:?}", file_path))?;

        log::info!("开始解析CSV文件: {:?}", file_path);

        // 先读取前几行来检测格式
        let content = std::fs::read_to_string(file_path).with_context(|| format!("无法读取CSV文件内容: {:?}", file_path))?;

        let lines: Vec<&str> = content.lines().take(3).collect();
        if lines.is_empty() {
            return Err(anyhow::anyhow!("CSV文件为空: {:?}", file_path));
        }

        log::info!("CSV文件前3行内容:");
        for (i, line) in lines.iter().enumerate() {
            log::info!("  行{}: {}", i + 1, line);
        }

        // 重新打开文件进行解析
        let file = File::open(file_path)?;
        let mut rdr = ReaderBuilder::new()
            .has_headers(true)
            .flexible(true) // 允许不同行有不同数量的字段
            .from_reader(file);

        let mut data = VecDeque::new();
        let mut line_number = 1; // 跳过标题行

        for result in rdr.records() {
            line_number += 1;
            let record = result.with_context(|| format!("解析CSV第{}行失败", line_number))?;

            // 尝试解析为OHLCV
            let ohlcv = Self::parse_csv_format(&record, &symbol, line_number)?;
            data.push_back(ohlcv);
        }

        let description = format!("历史数据: {} ({} 条记录)", file_path.display(), data.len());

        log::info!("CSV解析完成，共加载{}条记录", data.len());

        Ok(Self {
            symbol,
            data,
            current_index: 0,
            description,
            steps_per_episode,
        })
    }

    /// 解析Binance格式CSV行 (timestamp,open,high,low,close,volume,close_time,quote_asset_volume,number_of_trades,taker_buy_base_asset_volume,taker_buy_quote_asset_volume)
    fn parse_csv_format(record: &csv::StringRecord, symbol: &Symbol, line_number: usize) -> Result<Tick> {
        use chrono::{DateTime, NaiveDateTime, Utc};
        use rust_decimal::Decimal;
        use std::str::FromStr;

        // 解析日期时间字符串为时间戳
        let timestamp_str = &record[0];
        let timestamp = if timestamp_str.contains('-') && timestamp_str.contains(':') {
            // 尝试解析多种日期时间格式
            let dt_result = if timestamp_str.contains('+') || timestamp_str.ends_with("Z") {
                // 带时区的格式：2024-06-09 05:31:00+00:00 或 2024-06-09T05:31:00Z
                DateTime::parse_from_str(timestamp_str, "%Y-%m-%d %H:%M:%S%:z")
                    .or_else(|_| DateTime::parse_from_str(timestamp_str, "%Y-%m-%dT%H:%M:%S%:z"))
                    .or_else(|_| DateTime::parse_from_str(timestamp_str, "%Y-%m-%dT%H:%M:%SZ"))
                    .map(|dt| dt.with_timezone(&Utc))
            } else {
                // 无时区格式：2025-05-06 00:00:00
                NaiveDateTime::parse_from_str(timestamp_str, "%Y-%m-%d %H:%M:%S")
                    .map(|naive_dt| DateTime::<Utc>::from_naive_utc_and_offset(naive_dt, Utc))
            };
            
            match dt_result {
                Ok(dt) => dt.timestamp_millis(),
                Err(e) => return Err(anyhow::anyhow!(
                    "第{}行：无法解析日期时间格式 '{}' - 支持格式: '2024-06-09 05:31:00+00:00', '2024-06-09T05:31:00Z', '2025-05-06 00:00:00' - 错误: {}", 
                    line_number, timestamp_str, e
                ))
            }
        } else {
            // 数字格式时间戳
            timestamp_str
                .parse::<i64>()
                .with_context(|| format!("第{}行：无法解析timestamp '{}'", line_number, timestamp_str))?
        };

        let open = record[1]
            .parse::<f64>()
            .with_context(|| format!("第{}行：无法解析open价格 '{}'", line_number, &record[1]))?;
        let high = record[2]
            .parse::<f64>()
            .with_context(|| format!("第{}行：无法解析high价格 '{}'", line_number, &record[2]))?;
        let low = record[3].parse::<f64>().with_context(|| format!("第{}行：无法解析low价格 '{}'", line_number, &record[3]))?;
        let close = record[4]
            .parse::<f64>()
            .with_context(|| format!("第{}行：无法解析close价格 '{}'", line_number, &record[4]))?;
        let volume = record[5].parse::<f64>().with_context(|| format!("第{}行：无法解析volume '{}'", line_number, &record[5]))?;
        Ok(Tick::new(symbol.clone(), timestamp, open, high, low, close, volume))
    }

    /// 获取总数据量
    pub fn total_count(&self) -> usize {
        self.data.len()
    }
}

impl DataProvider for HistoricalProvider {
    fn next_tick(&mut self) -> Result<Tick, DataError> {
        if let Some(ohlcv) = self.data.get(self.current_index) {
            self.current_index += 1;
            Ok(ohlcv.clone())
        } else {
            Err(DataError::StreamEnded)
        }
    }

    fn get_tick(&mut self) -> Result<Tick, DataError> {
        if let Some(ohlcv) = self.data.get(self.current_index) {
            Ok(ohlcv.clone())
        } else {
            Err(DataError::StreamEnded)
        }
    }

    fn reset(&mut self) {
        // 随机选择起始索引，确保有足够的数据进行训练
        // 留出最后1000条数据作为缓冲区，避免数据不足
        let max_start_index = self.data.len() - self.steps_per_episode;

        use rand::Rng;
        let mut rng = rand::rng();
        self.current_index = rng.random_range(0..max_start_index);
        log::debug!("重置数据提供器，随机起始索引: {}/{}", self.current_index, self.data.len());
    }
}
