//! # 合成数据生成器
//! 
//! 生成用于测试和验证的合成市场数据

use crate::errors::DataError;
use crate::provider::DataProvider;
use drl_trading_core::{Symbol, Tick};
use std::f64::consts::PI;

// 定义本地的Result类型
type DataResult<T> = Result<T, DataError>;

/// 正弦波数据生成器配置
#[derive(Debug, Clone)]
pub struct SineWaveConfig {
    /// 基础价格
    pub base_price: f64,
    /// 振幅（价格波动幅度）
    pub amplitude: f64,
    /// 周期（多少个时间步完成一个周期）
    pub period: f64,
    /// 相位偏移
    pub phase: f64,
    /// 趋势斜率（每步的价格趋势）
    pub trend_slope: f64,
    /// 噪声强度（0.0-1.0）
    pub noise_level: f64,
    /// 总数据点数
    pub total_points: usize,
    /// 时间间隔（毫秒）
    pub time_interval_ms: i64,
}

impl Default for SineWaveConfig {
    fn default() -> Self {
        Self {
            base_price: 100.0,
            amplitude: 10.0,
            period: 100.0,
            phase: 0.0,
            trend_slope: 0.01,
            noise_level: 0.1,
            total_points: 10000,
            time_interval_ms: 60000, // 1分钟
        }
    }
}

/// 正弦波数据生成器
/// 
/// 生成可预测的正弦波价格数据，用于验证模型学习能力
pub struct SineWaveDataProvider {
    config: SineWaveConfig,
    symbol: Symbol,
    current_index: usize,
    start_timestamp: i64,
    data_cache: Vec<Tick>,
}

impl SineWaveDataProvider {
    /// 创建新的正弦波数据生成器
    pub fn new(config: SineWaveConfig, symbol: Symbol) -> DataResult<Self> {
        let start_timestamp = chrono::Utc::now().timestamp_millis();
        let mut provider = Self {
            config,
            symbol,
            current_index: 0,
            start_timestamp,
            data_cache: Vec::new(),
        };
        
        // 预生成所有数据
        provider.generate_data()?;
        
        Ok(provider)
    }
    
    /// 使用默认配置创建
    pub fn default_btc() -> DataResult<Self> {
        let config = SineWaveConfig {
            base_price: 50000.0,
            amplitude: 5000.0,
            period: 200.0,
            ..Default::default()
        };
        Self::new(config, Symbol::new("BTCUSDT"))
    }
    
    /// 使用简单配置创建（用于快速测试）
    pub fn simple_test() -> DataResult<Self> {
        let config = SineWaveConfig {
            base_price: 100.0,
            amplitude: 20.0,
            period: 50.0,
            trend_slope: 0.0,
            noise_level: 0.05,
            total_points: 1000,
            ..Default::default()
        };
        Self::new(config, Symbol::new("TESTUSDT"))
    }
    
    /// 生成所有数据点
    fn generate_data(&mut self) -> DataResult<()> {
        self.data_cache.clear();
        self.data_cache.reserve(self.config.total_points);
        
        let mut rng = rand::rng();
        
        for i in 0..self.config.total_points {
            let t = i as f64;
            
            // 基础正弦波
            let sine_value = (2.0 * PI * t / self.config.period + self.config.phase).sin();
            
            // 趋势分量
            let trend = self.config.trend_slope * t;
            
            // 噪声分量
            let noise = if self.config.noise_level > 0.0 {
                use rand::Rng;
                let noise_factor: f64 = rng.random_range(-1.0..1.0);
                noise_factor * self.config.noise_level * self.config.amplitude
            } else {
                0.0
            };
            
            // 计算最终价格
            let price = self.config.base_price 
                + self.config.amplitude * sine_value 
                + trend 
                + noise;
            
            // 确保价格为正
            let price = price.max(1.0);
            
            // 生成时间戳
            let timestamp = self.start_timestamp + (i as i64) * self.config.time_interval_ms;
            
            // 创建Tick数据
            let tick = Tick {
                symbol: self.symbol.clone(),
                timestamp,
                open: price,
                high: price * 1.001,  // 简单的高低价
                low: price * 0.999,
                close: price,
                volume: 1000.0 + (sine_value * 500.0).abs(), // 基于价格波动的成交量
            };
            
            self.data_cache.push(tick);
        }
        
        log::info!(
            "生成正弦波数据完成: {} 个数据点, 基础价格={}, 振幅={}, 周期={}", 
            self.data_cache.len(), 
            self.config.base_price, 
            self.config.amplitude, 
            self.config.period
        );
        
        Ok(())
    }
    
    /// 获取数据统计信息
    pub fn get_stats(&self) -> SineWaveStats {
        if self.data_cache.is_empty() {
            return SineWaveStats::default();
        }
        
        let prices: Vec<f64> = self.data_cache.iter().map(|t| t.close).collect();
        let min_price = prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let max_price = prices.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let avg_price = prices.iter().sum::<f64>() / prices.len() as f64;
        
        SineWaveStats {
            total_points: self.data_cache.len(),
            min_price,
            max_price,
            avg_price,
            price_range: max_price - min_price,
            config: self.config.clone(),
        }
    }
    
    /// 重置到开始位置
    pub fn reset(&mut self) {
        self.current_index = 0;
    }
    
    /// 获取剩余数据点数
    pub fn remaining_points(&self) -> usize {
        self.data_cache.len().saturating_sub(self.current_index)
    }
    
    /// 检查是否还有数据
    pub fn has_more_data(&self) -> bool {
        self.current_index < self.data_cache.len()
    }
}

impl DataProvider for SineWaveDataProvider {
    fn next_tick(&mut self) -> Result<Tick, DataError> {
        if self.current_index >= self.data_cache.len() {
            return Err(DataError::StreamEnded);
        }

        let tick = self.data_cache[self.current_index].clone();
        self.current_index += 1;

        Ok(tick)
    }

    fn get_tick(&mut self) -> Result<Tick, DataError> {
        if self.current_index == 0 || self.current_index > self.data_cache.len() {
            return Err(DataError::StreamEnded);
        }

        // 返回当前tick（上一次next_tick返回的）
        let tick = self.data_cache[self.current_index - 1].clone();
        Ok(tick)
    }

    fn reset(&mut self) {
        self.current_index = 0;
    }
}

/// 正弦波数据统计信息
#[derive(Debug, Clone)]
pub struct SineWaveStats {
    pub total_points: usize,
    pub min_price: f64,
    pub max_price: f64,
    pub avg_price: f64,
    pub price_range: f64,
    pub config: SineWaveConfig,
}

impl Default for SineWaveStats {
    fn default() -> Self {
        Self {
            total_points: 0,
            min_price: 0.0,
            max_price: 0.0,
            avg_price: 0.0,
            price_range: 0.0,
            config: SineWaveConfig::default(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_sine_wave_generation() {
        let mut provider = SineWaveDataProvider::simple_test().unwrap();
        let stats = provider.get_stats();
        
        assert_eq!(stats.total_points, 1000);
        assert!(stats.min_price > 0.0);
        assert!(stats.max_price > stats.min_price);
        
        // 测试数据获取
        let mut count = 0;
        while let Ok(Some(_tick)) = provider.next_tick().await {
            count += 1;
            if count > 10 { break; } // 只测试前10个
        }
        assert_eq!(count, 10);
    }
    
    #[test]
    fn test_sine_wave_config() {
        let config = SineWaveConfig::default();
        assert_eq!(config.base_price, 100.0);
        assert_eq!(config.amplitude, 10.0);
        assert!(config.period > 0.0);
    }
}
