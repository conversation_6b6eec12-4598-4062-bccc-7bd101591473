//! # 同步数据提供者接口
//!
//! 为训练性能优化的同步数据提供者实现

use crate::errors::DataError;
use drl_trading_core::Tick;

/// 同步数据提供者接口
///
/// 专为训练性能优化，完全同步执行，无异步开销
pub trait DataProvider: Send + Sync {
    /// 获取下一个tick的数据
    fn next_tick(&mut self) -> Result<Tick, DataError>;

    /// 获取当前tick的数据
    fn get_tick(&mut self) -> Result<Tick, DataError>;

    /// 重置数据提供者到初始状态
    fn reset(&mut self);

    // /// 获取数据源描述
    // fn description(&self) -> String;
    // 
    // /// 检查数据源是否仍然可用
    // fn is_available(&self) -> bool {
    //     true
    // }
    // 
    // /// 获取剩余数据数量（如果已知）
    // fn remaining_count(&self) -> Option<usize> {
    //     None
    // }
}
