//! # DRL交易系统数据供应模块
//! 
//! 本模块提供统一的同步数据获取接口，专为训练性能优化。
//! 完全移除异步开销，专注于历史数据高效加载。

pub mod errors;
pub mod historical;
pub mod provider;
pub mod synthetic;

// 重新导出核心类型和特征
pub use errors::DataError;
pub use historical::HistoricalProvider;
pub use provider::DataProvider;
pub use synthetic::{SineWaveDataProvider, SineWaveConfig, SineWaveStats};

// 为方便使用，导出常用的结果类型
pub type DataResult<T> = Result<T, DataError>; 