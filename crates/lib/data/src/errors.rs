//! 数据模块错误类型定义
//! 
//! 定义与同步数据处理相关的错误类型，专注于CSV文件读取和数据解析。

use thiserror::Error;

/// 数据模块错误类型
#[derive(Debug, Error)]
pub enum DataError {
    #[error("文件读取失败: {0}")]
    FileReadError(String),

    #[error("数据解析失败: {0}")]
    ParseError(String),

    #[error("数据流已结束")]
    StreamEnded,

    #[error("配置错误: {0}")]
    ConfigError(String),

    #[error("数据格式错误: {0}")]
    FormatError(String),

    #[error("IO错误: {0}")]
    IoError(#[from] std::io::Error),

    #[error("CSV解析错误: {0}")]
    CsvError(#[from] csv::Error),
}

impl DataError {
    /// 创建文件读取错误
    pub fn file_read<T: std::fmt::Display>(msg: T) -> Self {
        Self::FileReadError(msg.to_string())
    }

    /// 创建数据解析错误
    pub fn parse<T: std::fmt::Display>(msg: T) -> Self {
        Self::ParseError(msg.to_string())
    }

    /// 创建配置错误
    pub fn config<T: std::fmt::Display>(msg: T) -> Self {
        Self::ConfigError(msg.to_string())
    }

    /// 创建数据格式错误
    pub fn format<T: std::fmt::Display>(msg: T) -> Self {
        Self::FormatError(msg.to_string())
    }
} 