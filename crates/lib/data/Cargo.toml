[package]
name = "drl-trading-data"
version.workspace = true
edition.workspace = true
description = "DRL量化交易系统的同步数据供应模块"
authors.workspace = true

[dependencies]
# 内部依赖
drl-trading-core.workspace = true

# 外部依赖 - 仅保留同步数据处理必需的
anyhow.workspace = true
csv.workspace = true
serde.workspace = true
thiserror.workspace = true
chrono.workspace = true
rust_decimal.workspace = true
log.workspace = true
rand.workspace = true

[dev-dependencies]
tempfile.workspace = true
rust_decimal_macros.workspace = true
