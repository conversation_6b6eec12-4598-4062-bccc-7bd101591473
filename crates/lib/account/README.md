# DRL Trading Account Module

深度强化学习量化交易系统的账户管理模块，作为整个交易系统的"状态核心"，负责精确的账户状态管理、投资组合跟踪和业绩分析。

## 🎯 核心功能

### 1. 账户状态管理 (`AccountState`)
- **资金管理**: 总余额、可用余额、冻结资金管理
- **盈亏追踪**: 实时已实现盈亏累计
- **费用计算**: 交易手续费统计和扣除
- **收益率计算**: 净值、总收益率等关键指标

### 2. 投资组合管理 (`Portfolio`)
- **持仓管理**: 多品种持仓的实时更新和维护
- **价格缓存**: 最新价格信息缓存，优化计算性能
- **盈亏计算**: 未实现盈亏的实时计算
- **市值统计**: 投资组合总市值和各品种市值

### 3. 业绩分析 (`Performance`)
- **权益历史**: 权益变化的时间序列记录
- **风险指标**: 最大回撤、回撤持续时间等风险控制指标
- **业绩统计**: 收益率、净值等业绩评估指标

### 4. 统一账户接口 (`Account`)
- **事件驱动**: 响应交易事件和价格更新事件
- **状态聚合**: 整合账户、投资组合、业绩的综合视图
- **数据验证**: 完整的账户状态一致性验证

## 🏗️ 设计架构

```
Account (账户聚合器)
├── AccountState (资金状态)
├── Portfolio (投资组合)
└── Performance (业绩分析)
```

## 📋 核心特性

### ✨ 设计理念
- **单一数据源**: 确保账户状态的唯一性和一致性
- **响应式更新**: 被动响应外部事件，避免主动轮询
- **精确计算**: 使用 `rust_decimal::Decimal` 确保金融计算精度
- **关注分离**: 清晰的模块边界和职责划分

### 🔄 事件驱动架构
- **交易事件处理**: `on_trade()` 方法处理成交通知
- **价格更新处理**: `on_tick()` 方法处理市场价格变化
- **状态自动更新**: 事件触发自动更新相关状态

### 💰 精确财务计算
- **Decimal 精度**: 避免浮点数误差
- **手续费管理**: 自动计算和扣除交易费用（默认0.1%）
- **盈亏分离**: 已实现和未实现盈亏的分别管理

## 🚀 快速开始

### 基本使用

```rust
use drl_trading_account::{Account, AccountResult};
use rust_decimal_macros::dec;

// 创建账户（初始资金10万）
let mut account = Account::new(dec!(100000))?;

// 处理交易事件
let trade = Trade::new(/* 交易参数 */);
account.on_trade(&trade)?;

// 处理价格更新
let tick = Tick::new(/* 价格数据 */);
account.on_tick(&tick)?;

// 获取账户摘要
let summary = account.get_account_summary();
println!("总权益: ${}", summary.total_equity);
println!("净值: {}", summary.net_value);
```

### 运行演示程序

```bash
cargo run --example account_demo -p drl-trading-account
```

演示程序展示了完整的交易流程，包括：
- 建立多品种持仓
- 价格变动影响
- 部分平仓操作  
- 资金存取操作
- 业绩分析报告

## 📊 主要数据结构

### AccountSummary (账户摘要)
```rust
pub struct AccountSummary {
    pub total_equity: Decimal,        // 总权益
    pub net_value: Decimal,           // 净值
    pub total_return: Decimal,        // 总收益率
    pub max_drawdown: Decimal,        // 最大回撤
    pub realized_pnl: Decimal,        // 已实现盈亏
    pub unrealized_pnl: Decimal,      // 未实现盈亏
    pub position_count: usize,        // 持仓数量
    // ...
}
```

### PerformanceMetrics (业绩指标)
```rust
pub struct PerformanceMetrics {
    pub initial_equity: Decimal,              // 初始权益
    pub current_equity: Decimal,              // 当前权益
    pub peak_equity: Decimal,                 // 峰值权益
    pub max_drawdown: Decimal,                // 最大回撤
    pub max_drawdown_duration_days: i64,     // 最大回撤持续天数
    pub total_return: Decimal,                // 总收益率
    pub current_net_value: Decimal,           // 当前净值
    // ...
}
```

## 🔧 API 参考

### Account 主要方法

| 方法 | 功能 | 说明 |
|------|------|------|
| `new(initial_balance)` | 创建账户 | 设置初始资金 |
| `on_trade(trade)` | 处理交易 | 更新持仓和资金状态 |
| `on_tick(tick)` | 处理行情 | 更新价格和未实现盈亏 |
| `deposit(amount)` | 存入资金 | 增加账户余额 |
| `withdraw(amount)` | 提取资金 | 减少账户余额 |
| `get_account_summary()` | 获取摘要 | 返回完整账户状态 |
| `validate()` | 状态验证 | 检查数据一致性 |

### Portfolio 主要方法

| 方法 | 功能 | 说明 |
|------|------|------|
| `get_position(symbol)` | 获取持仓 | 查询特定品种持仓 |
| `get_unrealized_pnl()` | 未实现盈亏 | 计算总未实现盈亏 |
| `get_total_market_value()` | 总市值 | 计算投资组合总市值 |
| `position_count()` | 持仓数量 | 返回持仓品种数 |

## 🧪 测试覆盖

模块包含33个全面的单元测试，覆盖：

- ✅ 账户创建和初始化
- ✅ 各类交易场景（买入、卖出、部分平仓）
- ✅ 价格更新和盈亏计算
- ✅ 资金存取操作
- ✅ 多品种持仓管理
- ✅ 业绩指标计算
- ✅ 错误场景处理
- ✅ 数据验证机制

运行测试：
```bash
cargo test -p drl-trading-account
```

## 🔍 技术细节

### 依赖管理
- 使用 workspace 模式管理依赖
- 核心依赖：`rust_decimal` (精确计算)、`chrono` (时间处理)、`serde` (序列化)

### 错误处理
- 统一的 `AccountError` 错误类型
- `AccountResult<T>` 类型别名简化错误处理
- 详细的错误信息和上下文

### 性能优化
- 价格数据缓存机制
- 未实现盈亏的延迟计算
- 高效的 HashMap 持仓管理

## 📈 使用场景

1. **实盘交易**: 作为实盘交易系统的账户管理核心
2. **策略回测**: 提供精确的历史业绩分析
3. **风险控制**: 实时监控账户风险指标
4. **业绩归因**: 详细的盈亏来源分析

## 🤝 贡献指南

遵循项目统一的 Rust 代码规范，确保：
- 使用 `rust_decimal::Decimal` 进行金融计算
- 完善的单元测试覆盖
- 清晰的文档和注释
- 遵循错误处理最佳实践

## 📄 许可证

本项目遵循 MIT 许可证。 