//! 账户模块错误类型定义
//! 
//! 定义与账户、投资组合和业绩计算相关的专用错误类型。

use thiserror::Error;
use rust_decimal::Decimal;
use drl_trading_core::types::common::Symbol;

/// 账户模块错误类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum AccountError {
    #[error("资金不足: 需要 {required}, 可用 {available}")]
    InsufficientFunds { required: Decimal, available: Decimal },

    #[error("无效的交易金额: {amount}")]
    InvalidTradeAmount { amount: Decimal },

    #[error("持仓不存在: {symbol}")]
    PositionNotFound { symbol: Symbol },

    #[error("无效的初始余额: {balance}")]
    InvalidInitialBalance { balance: Decimal },

    #[error("计算错误: {message}")]
    CalculationError { message: String },

    #[error("状态不一致: {message}")]
    StateInconsistency { message: String },

    #[error("业绩计算错误: {message}")]
    PerformanceError { message: String },

    #[error("数据点不足，无法计算业绩指标")]
    InsufficientDataPoints,

    #[error("时间戳无效或顺序错误")]
    InvalidTimestamp,
}

impl AccountError {
    /// 创建资金不足错误
    pub fn insufficient_funds(required: Decimal, available: Decimal) -> Self {
        Self::InsufficientFunds { required, available }
    }

    /// 创建无效交易金额错误
    pub fn invalid_trade_amount(amount: Decimal) -> Self {
        Self::InvalidTradeAmount { amount }
    }

    /// 创建持仓不存在错误
    pub fn position_not_found(symbol: Symbol) -> Self {
        Self::PositionNotFound { symbol }
    }

    /// 创建计算错误
    pub fn calculation_error<T: std::fmt::Display>(message: T) -> Self {
        Self::CalculationError {
            message: message.to_string(),
        }
    }

    /// 创建状态不一致错误
    pub fn state_inconsistency<T: std::fmt::Display>(message: T) -> Self {
        Self::StateInconsistency {
            message: message.to_string(),
        }
    }

    /// 创建业绩计算错误
    pub fn performance_error<T: std::fmt::Display>(message: T) -> Self {
        Self::PerformanceError {
            message: message.to_string(),
        }
    }
}

/// 账户模块结果类型
pub type AccountResult<T> = Result<T, AccountError>; 