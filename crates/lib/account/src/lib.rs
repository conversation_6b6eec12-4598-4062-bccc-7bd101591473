//! # DRL交易系统账户与投资组合模块
//!
//! 本模块是整个交易系统的**状态核心**，负责精确地跟踪、计算和报告所有与资金、资产和业绩相关的状态。
//!
//! ## 设计哲学
//!
//! - **事实的唯一来源**：任何时候需要了解账户状态时，本模块是唯一可信的答案来源
//! - **响应式状态更新**：被动接收外部事件并更新内部状态，不产生主动行为
//! - **计算精确性**：所有货币计算使用 `rust_decimal::Decimal` 确保精度
//! - **关注点分离**：分为状态、投资组合、业绩等独立组件
//!
//! ## 模块结构
//!
//! - [`AccountState`](account_state::AccountState) - 核心资金状态管理
//! - [`Portfolio`](portfolio::Portfolio) - 持仓管理和市值计算
//! - [`Performance`](performance::Performance) - 历史业绩分析
//! - [`Account`](account::Account) - 顶层聚合器和事件处理

pub mod account;
pub mod account_state;
pub mod errors;
pub mod performance;
pub mod portfolio;

// 重新导出核心类型
pub use account::{Account, AccountConfig, AccountSummaryF64, RewardMetrics};
pub use account_state::AccountState;
pub use errors::{AccountError, AccountResult};
pub use performance::{Performance, PerformanceMetrics};
pub use portfolio::Portfolio;

// 为方便使用，导出常用的结果类型
pub type Result<T> = std::result::Result<T, AccountError>;
