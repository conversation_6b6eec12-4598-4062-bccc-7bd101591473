//! 账户状态管理
//! 
//! `AccountState` 是原子化的资金状态机，负责跟踪和管理账户的核心资金状态。

use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use crate::errors::{AccountError, AccountResult};

/// 账户资金状态
/// 
/// 跟踪账户的基本资金信息，包括总余额、可用余额、已实现盈亏等。
/// 所有金额都使用 `Decimal` 类型确保精确计算。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AccountState {
    /// 总余额（包括已使用和可用的资金）
    pub total_balance: Decimal,
    
    /// 可用余额（可以用于新交易的资金）
    pub available_balance: Decimal,
    
    /// 已实现盈亏（通过平仓实现的盈亏）
    pub realized_pnl: Decimal,

    /// 未实现盈亏
    pub unrealized_pnl: Decimal,
    
    /// 累计手续费支出
    pub total_fees: Decimal,
    
    /// 初始资金（用于计算总回报率）
    pub initial_balance: Decimal,
}

impl AccountState {
    /// 创建新的账户状态
    /// 
    /// # 参数
    /// * `initial_balance` - 初始资金，必须大于零
    /// 
    /// # 返回
    /// * `Ok(AccountState)` - 成功创建的账户状态
    /// * `Err(AccountError)` - 初始余额无效时
    pub fn new(initial_balance: Decimal) -> AccountResult<Self> {
        if initial_balance <= Decimal::ZERO {
            return Err(AccountError::InvalidInitialBalance {
                balance: initial_balance,
            });
        }

        Ok(Self {
            total_balance: initial_balance,
            available_balance: initial_balance,
            realized_pnl: Decimal::ZERO,
            unrealized_pnl: Default::default(),
            total_fees: Decimal::ZERO,
            initial_balance,
        })
    }

    /// 存入资金
    /// 
    /// # 参数
    /// * `amount` - 存入金额，必须大于零
    pub fn deposit(&mut self, amount: Decimal) -> AccountResult<()> {
        if amount <= Decimal::ZERO {
            return Err(AccountError::invalid_trade_amount(amount));
        }

        self.total_balance += amount;
        self.available_balance += amount;
        
        log::info!("存入资金 {}, 总余额: {}", amount, self.total_balance);
        Ok(())
    }

    /// 提取资金
    /// 
    /// # 参数
    /// * `amount` - 提取金额，必须大于零且不超过可用余额
    pub fn withdraw(&mut self, amount: Decimal) -> AccountResult<()> {
        if amount <= Decimal::ZERO {
            return Err(AccountError::invalid_trade_amount(amount));
        }

        if amount > self.available_balance {
            return Err(AccountError::insufficient_funds(
                amount,
                self.available_balance,
            ));
        }

        self.total_balance -= amount;
        self.available_balance -= amount;
        
        log::info!("提取资金 {}, 总余额: {}", amount, self.total_balance);
        Ok(())
    }

    /// 支付手续费
    /// 
    /// # 参数
    /// * `fee` - 手续费金额，必须大于零
    pub fn pay_fee(&mut self, fee: Decimal) -> AccountResult<()> {
        if fee < Decimal::ZERO {
            return Err(AccountError::invalid_trade_amount(fee));
        }

        if fee > self.available_balance {
            return Err(AccountError::insufficient_funds(fee, self.available_balance));
        }

        self.total_balance -= fee;
        self.available_balance -= fee;
        self.total_fees += fee;
        
        log::debug!("支付手续费 {}, 累计手续费: {}", fee, self.total_fees);
        Ok(())
    }

    /// 冻结资金（用于开仓保证金）
    /// 
    /// # 参数
    /// * `amount` - 冻结金额
    pub fn freeze_funds(&mut self, amount: Decimal) -> AccountResult<()> {
        if amount <= Decimal::ZERO {
            return Err(AccountError::invalid_trade_amount(amount));
        }

        if amount > self.available_balance {
            return Err(AccountError::insufficient_funds(amount, self.available_balance));
        }

        self.available_balance -= amount;
        
        log::debug!("冻结资金 {}, 可用余额: {}", amount, self.available_balance);
        Ok(())
    }

    /// 解冻资金（平仓时释放保证金）
    /// 
    /// # 参数
    /// * `amount` - 解冻金额
    pub fn unfreeze_funds(&mut self, amount: Decimal) -> AccountResult<()> {
        if amount < Decimal::ZERO {
            return Err(AccountError::invalid_trade_amount(amount));
        }

        self.available_balance += amount;
        
        // 确保可用余额不超过总余额
        if self.available_balance > self.total_balance {
            return Err(AccountError::state_inconsistency(
                format!("可用余额 {} 超过总余额 {}", self.available_balance, self.total_balance)
            ));
        }
        
        log::debug!("解冻资金 {}, 可用余额: {}", amount, self.available_balance);
        Ok(())
    }

    /// 记录已实现盈亏
    /// 
    /// # 参数
    /// * `pnl` - 盈亏金额，正数为盈利，负数为亏损
    pub fn add_realized_pnl(&mut self, pnl: Decimal) -> AccountResult<()> {
        self.realized_pnl += pnl;
        self.total_balance += pnl;
        self.available_balance += pnl;
        
        // if pnl > Decimal::ZERO {
        //     log::info!("实现盈利 {}, 已实现盈亏: {}", pnl, self.realized_pnl);
        // } else {
        //     log::info!("实现亏损 {}, 已实现盈亏: {}", pnl, self.realized_pnl);
        // }
        
        Ok(())
    }

    /// 获取冻结资金金额
    pub fn get_frozen_funds(&self) -> Decimal {
        self.total_balance - self.available_balance
    }

    /// 获取总回报率
    pub fn get_total_return_rate(&self) -> Decimal {
        if self.initial_balance == Decimal::ZERO {
            return Decimal::ZERO;
        }
        
        (self.total_balance - self.initial_balance) / self.initial_balance
    }

    /// 获取净值（相对于初始资金的倍数）
    pub fn get_net_value(&self) -> Decimal {
        if self.initial_balance == Decimal::ZERO {
            return Decimal::ONE;
        }
        
        self.total_balance / self.initial_balance
    }

    /// 验证状态一致性
    pub fn validate(&self) -> AccountResult<()> {
        // 检查基本约束
        if self.total_balance < Decimal::ZERO {
            return Err(AccountError::state_inconsistency("总余额不能为负数"));
        }
        
        if self.available_balance < Decimal::ZERO {
            return Err(AccountError::state_inconsistency("可用余额不能为负数"));
        }
        
        if self.available_balance > self.total_balance {
            return Err(AccountError::state_inconsistency("可用余额不能超过总余额"));
        }
        
        if self.total_fees < Decimal::ZERO {
            return Err(AccountError::state_inconsistency("累计手续费不能为负数"));
        }
        
        if self.initial_balance <= Decimal::ZERO {
            return Err(AccountError::state_inconsistency("初始余额必须大于零"));
        }
        
        Ok(())
    }
}
