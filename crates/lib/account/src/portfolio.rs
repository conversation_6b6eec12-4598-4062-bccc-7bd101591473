//! 投资组合管理
//! 
//! `Portfolio` 结构体管理所有当前的持仓位置，计算未实现盈亏和总市值。

use std::collections::HashMap;
use rust_decimal::Decimal;
use rust_decimal::prelude::{ToPrimitive};
use serde::{Deserialize, Serialize};
use drl_trading_core::portfolio::PortfolioSummary;
use drl_trading_core::trade::Trade;
use drl_trading_core::types::{
    common::{Symbol, Price},
    execution::{Position},
    tick::Tick,
};
use crate::errors::{AccountError, AccountResult};

    /// 投资组合管理器
/// 
/// 管理所有持仓位置，计算市值和未实现盈亏。
/// 支持多品种同时持仓。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Portfolio {
    /// 持仓映射 (品种 -> 持仓)
    positions: HashMap<Symbol, Position>,
    
    /// 最新价格缓存 (品种 -> 价格)
    latest_prices: HashMap<Symbol, Price>,

    /// 交易笔数
    trade_amount: i64,

    /// 交易历史记录
    trade_history: Vec<Trade>,

    /// 总市值
    total_equity: Decimal,
    
    /// 总未实现盈亏缓存
    cached_unrealized_pnl: Decimal,
}

impl Portfolio {
    /// 创建新的投资组合
    pub fn new() -> Self {
        Self {
            positions: HashMap::new(),
            latest_prices: HashMap::new(),
            trade_amount: 0,
            trade_history: Vec::new(),
            total_equity: Default::default(),
            cached_unrealized_pnl: Decimal::ZERO,
        }
    }

    /// 处理交易事件，更新持仓
    /// 
    /// # 参数
    /// * `trade` - 交易记录
    pub fn on_trade(&mut self, trade: &Trade) -> AccountResult<()> {
        let symbol = &trade.symbol;
        
        // 获取或创建持仓
        let position = self.positions.entry(symbol.clone()).or_insert_with(|| {
            Position::empty(symbol.clone(), trade.timestamp)
        });

        self.trade_amount += 1;
        
        // 记录交易历史
        self.trade_history.push(trade.clone());
        
        // 更新持仓
        position.update_with_trade(trade);

        // 如果持仓为零，移除记录
        if position.quantity == Decimal::ZERO {
            self.positions.remove(symbol);
            log::debug!("移除空持仓: {}", symbol);
        } else {
            log::debug!(
                "更新持仓 {}: 数量={}, 均价={}", 
                symbol, 
                position.quantity, 
                position.average_entry_price
            );
        }
        Ok(())
    }

    /// 处理价格更新事件 总是返回未实现盈亏
    /// 
    /// # 参数
    /// * `tick` - 市场行情数据
    pub fn on_tick(&mut self, tick: &Tick) -> AccountResult<Decimal> {

        // 优化：提前检查是否有相关持仓，避免不必要的操作
        let has_position = self.positions.contains_key(&tick.symbol);
        let price =Decimal::try_from (tick.close).unwrap();
        // 优化：只有在有持仓时才进行价格更新和缓存失效检查
        if has_position {
            //更新价格并计算未实现盈亏
            self.latest_prices.insert(tick.symbol.clone(), price);
            self.recalculate_unrealized_pnl();
        } else {
            // 优化：无持仓时仍然更新价格缓存，但不标记缓存失效
            self.latest_prices.insert(tick.symbol.clone(), price);
        }
        let unrealized_pnl = self.get_unrealized_pnl();
        Ok(unrealized_pnl)
    }

    /// 获取指定品种的持仓
    /// 
    /// # 参数
    /// * `symbol` - 品种代码
    /// 
    /// # 返回
    /// * `Some(Position)` - 持仓信息
    /// * `None` - 无持仓
    pub fn get_position(&self, symbol: &Symbol) -> Option<&Position> {
        self.positions.get(symbol)
    }

    /// 获取所有持仓
    pub fn get_all_positions(&self) -> &HashMap<Symbol, Position> {
        &self.positions
    }

    /// 获取持仓数量
    pub fn position_count(&self) -> usize {
        self.positions.len()
    }

    /// 检查是否有持仓
    pub fn has_position(&self, symbol: &Symbol) -> bool {
        self.positions.contains_key(symbol)
    }

    /// 获取总未实现盈亏
    pub fn get_unrealized_pnl(&self) -> Decimal {
        self.cached_unrealized_pnl
    }


    /// 获取总市值
    pub fn get_total_market_value(&self) -> Decimal {
        let mut total_value = Decimal::ZERO;
        
        for (symbol, position) in &self.positions {
            if let Some(&current_price) = self.latest_prices.get(symbol) {
                let market_value = position.quantity * current_price;
                total_value += market_value;
            } else {
                // 如果没有最新价格，使用持仓均价
                let market_value = position.quantity * position.average_entry_price;
                total_value += market_value;
                log::warn!("品种 {} 缺少最新价格，使用持仓均价计算", symbol);
            }
        }
        
        total_value
    }

    /// 获取指定品种的未实现盈亏
    /// 
    /// # 参数
    /// * `symbol` - 品种代码
    /// 
    /// # 返回
    /// * `Some(Decimal)` - 未实现盈亏
    /// * `None` - 无持仓或无价格数据
    pub fn get_position_unrealized_pnl(&self, symbol: &Symbol) -> Option<Decimal> {
        let position = self.positions.get(symbol)?;
        let current_price = self.latest_prices.get(symbol)?;
        
        let unrealized_pnl = (current_price - position.average_entry_price) * position.quantity;
        Some(unrealized_pnl)
    }

    /// 获取指定品种的持仓市值
    /// 
    /// # 参数
    /// * `symbol` - 品种代码
    /// 
    /// # 返回
    /// * `Some(Decimal)` - 持仓市值
    /// * `None` - 无持仓
    pub fn get_position_market_value(&self, symbol: &Symbol) -> Option<Decimal> {
        let position = self.positions.get(symbol)?;
        
        if let Some(&current_price) = self.latest_prices.get(symbol) {
            Some(position.quantity * current_price)
        } else {
            // 使用均价作为备选
            Some(position.quantity * position.average_entry_price)
        }
    }

    /// 获取指定品种的当前价格
    /// 
    /// # 参数
    /// * `symbol` - 品种代码
    /// 
    /// # 返回
    /// * `Some(Price)` - 当前价格
    /// * `None` - 无价格数据
    pub fn get_current_price(&self, symbol: &Symbol) -> Option<Price> {
        self.latest_prices.get(symbol).copied()
    }


    /// 重新计算未实现盈亏
    fn recalculate_unrealized_pnl(&mut self) {
        let mut total_unrealized_pnl = Decimal::ZERO;
        
        for (symbol, position) in &self.positions {
            if let Some(&current_price) = self.latest_prices.get(symbol) {
                let unrealized_pnl = (current_price - position.average_entry_price) * position.quantity;
                total_unrealized_pnl += unrealized_pnl;
            } else {
                log::warn!("品种 {} 缺少最新价格，无法计算未实现盈亏", symbol);
            }
        }
        self.cached_unrealized_pnl = total_unrealized_pnl;
        log::debug!("重新计算未实现盈亏: {}", total_unrealized_pnl);
    }

    /// 获取持仓摘要信息
    pub fn get_summary(&self) -> PortfolioSummary {
        PortfolioSummary {
            position_count: self.position_count(),
            total_market_value: f64::try_from(self.get_total_market_value()).unwrap(),
            unrealized_pnl: f64::try_from(self.get_unrealized_pnl()).unwrap(),
            total_amount: self.trade_amount,
            symbols: self.positions.keys().cloned().collect(),
        }
    }

    /// 验证投资组合状态
    pub fn validate(&self) -> AccountResult<()> {
        // 检查持仓数据的一致性
        for (symbol, position) in &self.positions {
            // 持仓数量不能为零（零持仓应该被移除）
            if position.quantity == Decimal::ZERO {
                return Err(AccountError::state_inconsistency(
                    format!("持仓 {} 数量为零但未被移除", symbol)
                ));
            }
            
            // 平均价格必须为正数
            if position.average_entry_price <= Decimal::ZERO {
                return Err(AccountError::state_inconsistency(
                    format!("持仓 {} 平均价格无效: {}", symbol, position.average_entry_price)
                ));
            }
        }
        
        Ok(())
    }

    /// 清空所有持仓（用于重置）
    pub fn clear(&mut self) {
        self.positions.clear();
        self.latest_prices.clear();
        self.trade_history.clear();
        self.trade_amount = 0;
        self.cached_unrealized_pnl = Decimal::ZERO;
        log::info!("清空投资组合所有持仓");
    }
    
    /// 获取交易次数
    pub fn get_trade_amount(&self) -> i64 {
        self.trade_amount
    }
    
    /// 获取交易历史记录
    pub fn get_trade_history(&self) -> &Vec<Trade> {
        &self.trade_history
    }
    
    /// 计算持仓集中度
    /// 
    /// 返回最大单一持仓占总持仓价值的比例
    pub fn calculate_position_concentration(&self) -> f64 {
        // if self.positions.is_empty() {
        //     return 0.0;
        // }
        // 
        // let total_market_value = self.get_total_market_value();
        // if total_market_value == Decimal::ZERO {
        //     return 0.0;
        // }
        // 
        // let mut max_position_value = Decimal::ZERO;
        // 
        // for (symbol, _position) in &self.positions {
        //     if let Some(position_value) = self.get_position_market_value(symbol) {
        //         let abs_value = position_value.abs();
        //         if abs_value > max_position_value {
        //             max_position_value = abs_value;
        //         }
        //     }
        // }
        // 
        // let concentration = max_position_value / total_market_value.abs();
        // concentration.to_f64().unwrap_or(0.0)
        return 0.0;
    }
}

