//! 业绩分析模块
//! 
//! `Performance` 结构体维护历史权益时间序列，计算各种业绩指标。

use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;
use serde::{Deserialize, Serialize};
use chrono;
use crate::errors::{AccountError, AccountResult};

/// 权益数据点
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct EquityPoint {
    /// 时间戳（毫秒）
    pub timestamp: i64,
    
    /// 权益值
    pub equity: Decimal,
    
    /// 相对于初始权益的净值
    pub net_value: Decimal,
}

impl EquityPoint {
    /// 创建新的权益数据点
    pub fn new(timestamp: i64, equity: Decimal, initial_equity: Decimal) -> Self {
        let net_value = if initial_equity > Decimal::ZERO {
            equity / initial_equity
        } else {
            Decimal::ONE
        };
        
        Self {
            timestamp,
            equity,
            net_value,
        }
    }
}

/// 风险指标
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RiskMetrics {
    /// 最大回撤
    pub max_drawdown: f64,
    /// 当前回撤
    pub current_drawdown: f64,
    /// 波动率
    pub volatility: f64,
    /// 夏普比率
    pub sharpe_ratio: f64,
    /// 胜率
    pub win_rate: f64,
    /// 盈利因子
    pub profit_factor: f64,
    /// 最大连续亏损次数
    pub max_consecutive_losses: i32,
    /// 持仓集中度
    pub position_concentration: f64,
}

impl Default for RiskMetrics {
    fn default() -> Self {
        Self {
            max_drawdown: 0.0,
            current_drawdown: 0.0,
            volatility: 0.0,
            sharpe_ratio: 0.0,
            win_rate: 0.0,
            profit_factor: 0.0,
            max_consecutive_losses: 0,
            position_concentration: 0.0,
        }
    }
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// 总收益率
    pub total_return: f64,
    /// 年化收益率
    pub annualized_return: f64,
    /// 总交易次数
    pub total_trades: i32,
    /// 盈利交易次数
    pub winning_trades: i32,
    /// 亏损交易次数
    pub losing_trades: i32,
    /// 平均盈利
    pub average_win: f64,
    /// 平均亏损
    pub average_loss: f64,
    /// 最大盈利
    pub largest_win: f64,
    /// 最大亏损
    pub largest_loss: f64,
    /// 总手续费
    pub total_commission: f64,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            total_return: 0.0,
            annualized_return: 0.0,
            total_trades: 0,
            winning_trades: 0,
            losing_trades: 0,
            average_win: 0.0,
            average_loss: 0.0,
            largest_win: 0.0,
            largest_loss: 0.0,
            total_commission: 0.0,
        }
    }
}

/// 业绩分析器
/// 
/// 维护权益历史记录，计算各种业绩指标如最大回撤、年化收益率、夏普比率等。
/// 整合了原 performance_tracker 的功能，提供完整的业绩分析能力。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Performance {
    /// 历史权益序列
    equity_history: Vec<EquityPoint>,
    
    /// 初始权益
    initial_equity: Decimal,
    
    /// 当前权益
    current_equity: Decimal,
    
    /// 最高权益（用于计算回撤）
    peak_equity: Decimal,
    
    /// 最大回撤
    max_drawdown: Decimal,
    
    /// 最大回撤持续时间（毫秒）
    max_drawdown_duration: i64,
    
    /// 当前回撤开始时间
    current_drawdown_start: Option<i64>,
    
    /// 历史记录容量限制（用于内存管理）
    max_history_size: usize,
    
    /// 数据点之间的时间间隔（秒，用于年化计算）
    time_interval_seconds: i64,
}

impl Performance {
    /// 创建新的业绩分析器
    /// 
    /// # 参数
    /// * `initial_equity` - 初始权益，必须大于零
    pub fn new(initial_equity: Decimal) -> AccountResult<Self> {
        if initial_equity <= Decimal::ZERO {
            return Err(AccountError::PerformanceError {
                message: format!("初始权益必须大于零: {}", initial_equity),
            });
        }

        let timestamp = chrono::Utc::now().timestamp_millis();
        let initial_point = EquityPoint::new(timestamp, initial_equity, initial_equity);

        Ok(Self {
            equity_history: vec![initial_point],
            initial_equity,
            current_equity: initial_equity,
            peak_equity: initial_equity,
            max_drawdown: Decimal::ZERO,
            max_drawdown_duration: 0,
            current_drawdown_start: None,
            max_history_size: 10000, // 默认保留1万个数据点
            time_interval_seconds: 300, // 默认5分钟间隔
        })
    }
    
    /// 创建带容量限制的业绩分析器
    pub fn with_capacity(initial_equity: Decimal, max_history_size: usize) -> AccountResult<Self> {
        let mut performance = Self::new(initial_equity)?;
        performance.max_history_size = max_history_size;
        Ok(performance)
    }
    
    /// 设置时间间隔（用于年化计算）
    pub fn set_time_interval(&mut self, seconds: i64) {
        self.time_interval_seconds = seconds;
    }

    /// 添加权益数据点
    /// 
    /// # 参数
    /// * `timestamp` - 时间戳（毫秒）
    /// * `equity` - 当前权益
    pub fn add_equity_point(&mut self, timestamp: i64, equity: Decimal) -> AccountResult<()> {
        // 验证时间戳顺序
        if let Some(last_point) = self.equity_history.last() {
            if timestamp < last_point.timestamp {
                return Err(AccountError::InvalidTimestamp);
            }
        }

        // 允许负权益（杠杆交易中可能出现）
        // 但记录警告信息以便调试
        if equity < Decimal::ZERO {
            log::warn!("检测到负权益: {}, 可能由于杠杆交易或计算错误", equity);
        }

        // 创建新的数据点
        let point = EquityPoint::new(timestamp, equity, self.initial_equity);
        self.equity_history.push(point);
        
        // 维护容量限制
        if self.equity_history.len() > self.max_history_size {
            self.equity_history.remove(0);
        }

        // 更新当前权益
        self.current_equity = equity;

        // 更新峰值和回撤计算
        self.update_drawdown_metrics(timestamp, equity)?;

        log::debug!("添加权益点: {} -> {}", timestamp, equity);
        Ok(())
    }

    /// 获取当前权益
    pub fn get_current_equity(&self) -> Decimal {
        self.current_equity
    }

    /// 获取初始权益
    pub fn get_initial_equity(&self) -> Decimal {
        self.initial_equity
    }

    /// 获取当前净值
    pub fn get_current_net_value(&self) -> Decimal {
        if self.initial_equity > Decimal::ZERO {
            self.current_equity / self.initial_equity
        } else {
            Decimal::ONE
        }
    }

    /// 获取总收益率
    pub fn get_total_return(&self) -> Decimal {
        if self.initial_equity > Decimal::ZERO {
            (self.current_equity - self.initial_equity) / self.initial_equity
        } else {
            Decimal::ZERO
        }
    }
    
    /// 获取总收益率（f64版本）
    pub fn get_total_return_f64(&self) -> f64 {
        self.get_total_return().to_f64().unwrap_or(0.0)
    }

    /// 获取最大回撤
    pub fn get_max_drawdown(&self) -> Decimal {
        self.max_drawdown
    }
    
    /// 获取最大回撤（f64版本）
    pub fn get_max_drawdown_f64(&self) -> f64 {
        self.max_drawdown.to_f64().unwrap_or(0.0)
    }
    
    /// 获取当前回撤率
    pub fn get_current_drawdown(&self) -> f64 {
        // 简化：直接计算当前权益相对于峰值的回撤
        if let Some(last_point) = self.equity_history.last() {
            let current_equity = last_point.equity.to_f64().unwrap_or(0.0);
            let peak_equity = self.peak_equity.to_f64().unwrap_or(current_equity);
            
            if peak_equity > 0.0 {
                ((peak_equity - current_equity) / peak_equity).max(0.0)
            } else {
                0.0
            }
        } else {
            0.0
        }
    }

    /// 计算年化收益率（简化版本）
    pub fn calculate_annualized_return(&self) -> f64 {
        // 简化：直接返回总收益率，不做复杂的年化计算
        // 在强化学习环境中，我们更关心相对表现而不是精确的年化值
        let total_return = self.get_total_return_f64();
        total_return
    }
    
    /// 计算波动率（简化版本）
    pub fn calculate_volatility(&self, _lookback_days: Option<usize>) -> f64 {
        // 简化：基于最近几个权益点的标准差计算
        if self.equity_history.len() < 2 {
            return 0.0;
        }
        
        // 取最后10个数据点（或全部如果不足10个）
        let recent_count = 10.min(self.equity_history.len());
        let recent_points = &self.equity_history[self.equity_history.len() - recent_count..];
        
        // 计算收益率序列
        let mut returns = Vec::new();
        for i in 1..recent_points.len() {
            let prev_equity = recent_points[i-1].equity.to_f64().unwrap_or(0.0);
            let curr_equity = recent_points[i].equity.to_f64().unwrap_or(0.0);
            
            if prev_equity > 0.0 {
                let return_rate = (curr_equity - prev_equity) / prev_equity;
                returns.push(return_rate);
            }
        }
        
        // 计算标准差
        if returns.len() < 2 {
            return 0.0;
        }
        
        let mean: f64 = returns.iter().sum::<f64>() / returns.len() as f64;
        let variance: f64 = returns.iter()
            .map(|r| (r - mean).powi(2))
            .sum::<f64>() / returns.len() as f64;
        
        variance.sqrt()
    }
    
    /// 计算夏普比率
    pub fn calculate_sharpe_ratio(&self, risk_free_rate: f64) -> f64 {
        // 修改：降低数据要求，即使只有少量数据也尝试计算
        if self.equity_history.len() < 2 {
            return 0.0;
        }
        
        let annualized_return = self.calculate_annualized_return();
        
        // 防止年化收益率过大造成溢出
        if !annualized_return.is_finite() || annualized_return.abs() > 1000.0 {
            return 0.0;
        }
        
        // 计算年化波动率
        let volatility = self.calculate_volatility(None);
        
        // 修改：即使波动率很小也尝试计算夏普比率，避免过早返回0
        if volatility > 1e-10 && volatility.is_finite() {
            let periods_per_year = if self.time_interval_seconds > 0 {
                365.25 * 24.0 * 3600.0 / self.time_interval_seconds as f64
            } else {
                365.0 // 默认每天一个数据点
            };
            
            let annualized_volatility = volatility * periods_per_year.sqrt();
            
            if annualized_volatility > 1e-10 && annualized_volatility.is_finite() {
                let sharpe = (annualized_return - risk_free_rate) / annualized_volatility;
                // 放宽夏普比率范围限制
                if sharpe.is_finite() && sharpe.abs() <= 1000.0 {
                    sharpe
                } else {
                    0.0
                }
            } else {
                // 当波动率极小时，根据收益率正负性返回简化的夏普比率
                if annualized_return > risk_free_rate {
                    10.0 // 表示良好表现
                } else if annualized_return < risk_free_rate {
                    -10.0 // 表示糟糕表现
                } else {
                    0.0
                }
            }
        } else {
            0.0
        }
    }
    
    /// 生成风险指标（基于精确的交易统计数据）
    /// 
    /// # 参数
    /// * `win_rate` - 胜率
    /// * `profit_factor` - 盈利因子
    /// * `position_concentration` - 持仓集中度
    pub(crate) fn generate_risk_metrics(&self, win_rate: f64, profit_factor: f64, position_concentration: f64) -> RiskMetrics {
        RiskMetrics {
            max_drawdown: self.get_max_drawdown_f64(),
            current_drawdown: self.get_current_drawdown(),
            volatility: self.calculate_volatility(None),
            sharpe_ratio: self.calculate_sharpe_ratio(0.02), // 假设2%无风险利率
            win_rate,
            profit_factor,
            max_consecutive_losses: 0, // 可以在Account模块中计算并传入
            position_concentration,
        }
    }
    
    /// 生成性能指标（基于精确的交易统计数据）
    /// 
    /// # 参数
    /// * `total_trades` - 总交易次数
    /// * `winning_trades` - 盈利交易次数
    /// * `losing_trades` - 亏损交易次数
    /// * `average_win` - 平均盈利
    /// * `average_loss` - 平均亏损
    /// * `largest_win` - 最大单笔盈利
    /// * `largest_loss` - 最大单笔亏损
    /// * `total_fees` - 总手续费
    pub(crate) fn generate_performance_metrics(
        &self, 
        total_trades: i32, 
        winning_trades: i32, 
        losing_trades: i32, 
        average_win: f64, 
        average_loss: f64, 
        largest_win: f64, 
        largest_loss: f64, 
        total_fees: f64
    ) -> PerformanceMetrics {
        PerformanceMetrics {
            total_return: self.get_total_return_f64(),
            annualized_return: self.calculate_annualized_return(),
            total_trades,
            winning_trades,
            losing_trades,
            average_win,
            average_loss,
            largest_win,
            largest_loss,
            total_commission: total_fees,
        }
    }

    /// 获取最大回撤持续时间（毫秒）
    pub fn get_max_drawdown_duration(&self) -> i64 {
        self.max_drawdown_duration
    }

    /// 获取权益历史记录
    pub fn get_equity_history(&self) -> &[EquityPoint] {
        &self.equity_history
    }

    /// 获取最近N个权益点
    pub fn get_recent_equity_points(&self, count: usize) -> &[EquityPoint] {
        let start = if self.equity_history.len() > count {
            self.equity_history.len() - count
        } else {
            0
        };
        &self.equity_history[start..]
    }

    /// 获取业绩统计摘要（保持向后兼容）
    pub fn get_performance_metrics(&self) -> LegacyPerformanceMetrics {
        LegacyPerformanceMetrics {
            total_return: self.get_total_return(),
            max_drawdown: self.get_max_drawdown(),
            max_drawdown_duration_days: self.max_drawdown_duration / (24 * 60 * 60 * 1000),
            current_net_value: self.get_current_net_value(),
            equity_points_count: self.equity_history.len(),
            peak_equity: self.peak_equity,
            current_equity: self.current_equity,
            initial_equity: self.initial_equity,
        }
    }

    /// 清空历史记录并重置
    pub fn reset(&mut self, new_initial_equity: Decimal) -> AccountResult<()> {
        if new_initial_equity <= Decimal::ZERO {
            return Err(AccountError::PerformanceError {
                message: format!("新的初始权益必须大于零: {}", new_initial_equity),
            });
        }

        let timestamp = chrono::Utc::now().timestamp_millis();
        let initial_point = EquityPoint::new(timestamp, new_initial_equity, new_initial_equity);

        self.equity_history = vec![initial_point];
        self.initial_equity = new_initial_equity;
        self.current_equity = new_initial_equity;
        self.peak_equity = new_initial_equity;
        self.max_drawdown = Decimal::ZERO;
        self.max_drawdown_duration = 0;
        self.current_drawdown_start = None;

        log::info!("重置业绩分析器，新初始权益: {}", new_initial_equity);
        Ok(())
    }
    
    /// 重置并设置开始时间戳
    pub fn reset_with_timestamp(&mut self, new_initial_equity: Decimal, start_timestamp: i64) -> AccountResult<()> {
        if new_initial_equity <= Decimal::ZERO {
            return Err(AccountError::PerformanceError {
                message: format!("新的初始权益必须大于零: {}", new_initial_equity),
            });
        }

        let initial_point = EquityPoint::new(start_timestamp, new_initial_equity, new_initial_equity);

        self.equity_history = vec![initial_point];
        self.initial_equity = new_initial_equity;
        self.current_equity = new_initial_equity;
        self.peak_equity = new_initial_equity;
        self.max_drawdown = Decimal::ZERO;
        self.max_drawdown_duration = 0;
        self.current_drawdown_start = None;

        log::info!("重置业绩分析器，新初始权益: {}，开始时间: {}", new_initial_equity, start_timestamp);
        Ok(())
    }

    /// 更新回撤相关指标
    fn update_drawdown_metrics(&mut self, timestamp: i64, equity: Decimal) -> AccountResult<()> {
        // 更新峰值
        if equity > self.peak_equity {
            self.peak_equity = equity;
            // 创新高，结束回撤期
            self.current_drawdown_start = None;
        } else if equity < self.peak_equity {
            // 计算当前回撤
            let current_drawdown = (self.peak_equity - equity) / self.peak_equity;
            
            // 更新最大回撤
            if current_drawdown > self.max_drawdown {
                self.max_drawdown = current_drawdown;
            }
            
            // 跟踪回撤持续时间
            if self.current_drawdown_start.is_none() {
                self.current_drawdown_start = Some(timestamp);
            } else if let Some(start_time) = self.current_drawdown_start {
                let duration = timestamp - start_time;
                if duration > self.max_drawdown_duration {
                    self.max_drawdown_duration = duration;
                }
            }
        }
        
        Ok(())
    }
}

/// 业绩指标摘要（保持向后兼容）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LegacyPerformanceMetrics {
    /// 总收益率
    pub total_return: Decimal,
    
    /// 最大回撤
    pub max_drawdown: Decimal,
    
    /// 最大回撤持续天数
    pub max_drawdown_duration_days: i64,
    
    /// 当前净值
    pub current_net_value: Decimal,
    
    /// 权益点数量
    pub equity_points_count: usize,
    
    /// 峰值权益
    pub peak_equity: Decimal,
    
    /// 当前权益
    pub current_equity: Decimal,
    
    /// 初始权益
    pub initial_equity: Decimal,
}

impl Default for Performance {
    fn default() -> Self {
        Self::new(Decimal::ONE).unwrap_or_else(|_| {
            // 这种情况不应该发生，但为了安全提供一个备选
            let timestamp = chrono::Utc::now().timestamp_millis();
            let initial_point = EquityPoint::new(timestamp, Decimal::ONE, Decimal::ONE);
            
            Self {
                equity_history: vec![initial_point],
                initial_equity: Decimal::ONE,
                current_equity: Decimal::ONE,
                peak_equity: Decimal::ONE,
                max_drawdown: Decimal::ZERO,
                max_drawdown_duration: 0,
                current_drawdown_start: None,
                max_history_size: 10000,
                time_interval_seconds: 300,
            }
        })
    }
}
