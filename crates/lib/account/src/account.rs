//! 账户模块的顶层聚合器
//!
//! `Account` 是整个账户模块的对外入口，聚合了状态、投资组合和业绩分析功能。

use crate::{account_state::AccountState, errors::AccountResult, performance::Performance, portfolio::Portfolio};
use drl_trading_core::types::tick::Tick;
use rust_decimal::Decimal;
use rust_decimal::prelude::ToPrimitive;
use serde::{Deserialize, Serialize};
use drl_trading_core::trade::Trade;

/// 账户模块的顶层聚合器
///
/// 整合账户状态、投资组合和业绩分析，提供统一的事件处理接口。
/// 采用响应式设计，被动接收外部事件并更新内部状态。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Account {
    /// 账户资金状态
    pub state: AccountState,

    /// 投资组合管理
    pub portfolio: Portfolio,

    /// 业绩分析
    pub performance: Performance,

    /// 账户相关配置
    pub config: i64,
}

impl Account {
    /// 创建一个具有初始资金的新账户
    ///
    /// # 参数
    /// * `initial_balance` - 初始资金，必须大于零
    ///
    /// # 返回
    /// * `Ok(Account)` - 成功创建的账户
    /// * `Err(AccountError)` - 初始余额无效时
    pub fn new(initial_balance: i64) -> AccountResult<Self> {
        let balance = Decimal::from(initial_balance);
        let state = AccountState::new(balance)?;
        let portfolio = Portfolio::new();
        let performance = Performance::new(balance)?;

        log::debug!("创建新账户，初始资金: {}", initial_balance);

        Ok(Self {
            state,
            portfolio,
            performance,
            config: 0,
        })
    }

    /// 每个env.step()都会调用，确保指标实时更新
    pub fn on_tick(&mut self, tick: &Tick, trade: Option<Trade>) -> AccountResult<()> {
        match trade {
            Some(data) => match self.handler_trade(&data) {
                Ok(_) => log::debug!("on_tick success"),
                Err(err) => {
                    log::debug!("on_tick error: {:?}", err);
                }
            },
            None => {
                log::debug!("No trade")
            }
        }
        self.state.unrealized_pnl = self.portfolio.on_tick(tick).expect("TODO: panic message");
        // 4. 计算当前总权益
        let current_equity = self.state.total_balance + self.state.unrealized_pnl;
        // 5. 更新性能指标（这是唯一更新performance的地方）
        self.performance.add_equity_point(tick.timestamp, current_equity)?;
        log::debug!(
            "价格更新 {}: {}, 未实现盈亏: {}, 当前权益: {}",
            tick.symbol,
            tick.close,
            self.state.unrealized_pnl,
            current_equity
        );
        Ok(())
    }

    /// 当交易发生时调用 - 仅处理交易逻辑，不计算性能指标
    ///
    /// **职责**：
    /// - 更新持仓（开仓/加仓/减仓/平仓）
    /// - 更新账户余额（扣除/增加资金）
    /// - 扣除手续费
    /// - 计算并记录已实现盈亏
    ///
    /// # 参数
    /// * `trade` - 交易记录
    ///
    /// # 使用场景
    /// 仅在智能体动作不为Hold，且实际产生交易时调用
    pub fn handler_trade(&mut self, trade: &Trade) -> AccountResult<()> {
        // 1. 计算并扣除手续费
        let fee = self.calculate_fee(trade);
        if fee > Decimal::ZERO {
            self.state.pay_fee(fee)?;
            log::debug!("扣除交易手续费: {}", fee);
        }

        // 2. 计算已实现盈亏（在更新持仓之前获取原有持仓信息）
        let realized_pnl = match trade.side {
            drl_trading_core::types::execution::OrderSide::Sell => {
                // 卖出时计算已实现盈亏
                if let Some(position) = self.portfolio.get_position(&trade.symbol) {
                    let avg_entry_price = position.average_entry_price;
                    let current_position_qty = position.quantity;
                    
                    // 计算本次卖出的盈亏
                    if current_position_qty >= trade.quantity {
                        let price_diff = trade.price - avg_entry_price;
                        price_diff * trade.quantity
                    } else {
                        // 卖出数量超过持仓，只计算实际持仓部分的盈亏
                        let actual_sell_qty = current_position_qty.min(trade.quantity);
                        let price_diff = trade.price - avg_entry_price;
                        price_diff * actual_sell_qty
                    }
                } else {
                    Decimal::ZERO // 无持仓时卖出，无盈亏
                }
            }
            drl_trading_core::types::execution::OrderSide::Buy => {
                Decimal::ZERO // 买入时无已实现盈亏
            }
        };

        // 3. 记录已实现盈亏到账户状态
        if realized_pnl != Decimal::ZERO {
            self.state.add_realized_pnl(realized_pnl)?;
            log::debug!("本次交易已实现盈亏: {}, 累计已实现盈亏: {}", realized_pnl, self.state.realized_pnl);
        }

        // 4. 处理资金流（买入消耗现金，卖出获得现金）
        let trade_value = trade.quantity * trade.price;
        match trade.side {
            drl_trading_core::types::execution::OrderSide::Buy => {
                // 买入：消耗现金购买资产
                if trade_value > self.state.available_balance {
                    return Err(crate::errors::AccountError::insufficient_funds(trade_value, self.state.available_balance));
                }
                self.state.available_balance -= trade_value;
                log::debug!("买入消耗现金: {}, 剩余可用余额: {}", trade_value, self.state.available_balance);
            }
            drl_trading_core::types::execution::OrderSide::Sell => {
                // 卖出：获得现金
                self.state.available_balance += trade_value;
                log::debug!("卖出获得现金: {}, 当前可用余额: {}", trade_value, self.state.available_balance);
            }
        }

        // 5. 更新持仓状态
        self.portfolio.on_trade(trade)?;

        log::debug!("交易处理完成，持仓数量: {}", self.portfolio.position_count());
        Ok(())
    }

    /// 获取当前总权益（资产负债净值）
    ///
    /// 总权益 = 资金余额 + 未实现盈亏
    pub fn get_equity(&self) -> Decimal {
        // 使用缓存的未实现盈亏，避免portfolio状态计算
        self.state.total_balance + self.state.unrealized_pnl
    }

    /// 获取当前净值
    pub fn get_net_value(&self) -> Decimal {
        let current_equity = self.get_equity();
        if self.state.initial_balance > Decimal::ZERO {
            current_equity / self.state.initial_balance
        } else {
            Decimal::ONE
        }
    }

    /// 获取总收益率
    pub fn get_total_return(&self) -> Decimal {
        let current_equity = self.get_equity();
        if self.state.initial_balance > Decimal::ZERO {
            (current_equity - self.state.initial_balance) / self.state.initial_balance
        } else {
            Decimal::ZERO
        }
    }

    // ============ 高性能f64接口 (避免decimal类型传递) ============

    /// 获取当前总权益 (f64版本，高性能)
    pub fn get_equity_f64(&self) -> f64 {
        use rust_decimal::prelude::ToPrimitive;
        // 使用缓存的未实现盈亏
        (self.state.total_balance + self.state.unrealized_pnl).to_f64().unwrap_or(0.0)
    }

    /// 获取当前净值 (f64版本，高性能)
    pub fn get_net_value_f64(&mut self) -> f64 {
        use rust_decimal::prelude::ToPrimitive;
        let current_equity = self.get_equity();
        if self.state.initial_balance > Decimal::ZERO {
            (current_equity / self.state.initial_balance).to_f64().unwrap_or(1.0)
        } else {
            1.0
        }
    }

    /// 获取总收益率 (f64版本，高性能)
    pub fn get_total_return_f64(&mut self) -> f64 {
        use rust_decimal::prelude::ToPrimitive;
        let current_equity = self.get_equity();
        if self.state.initial_balance > Decimal::ZERO {
            ((current_equity - self.state.initial_balance) / self.state.initial_balance).to_f64().unwrap_or(0.0)
        } else {
            0.0
        }
    }

    /// 获取可用余额 (f64版本，高性能)
    pub fn get_available_balance_f64(&self) -> f64 {
        use rust_decimal::prelude::ToPrimitive;
        self.state.available_balance.to_f64().unwrap_or(0.0)
    }
    
    /// 计算精确的交易统计数据
    /// 
    /// 基于真实的交易历史记录计算，而非基于推测
    /// 
    /// # 返回
    /// (盈利次数, 亏损次数, 总盈利额, 总亏损额, 最大单笔盈利, 最大单笔亏损)
    pub fn calculate_trade_stats(&self) -> (i32, i32, f64, f64, f64, f64) {
        let mut winning_trades = 0i32;
        let mut losing_trades = 0i32;
        let mut total_profit = 0f64;
        let mut total_loss = 0f64;
        let mut largest_win = 0f64;
        let mut largest_loss = 0f64;
        
        // 遍历所有交易历史，计算每笔交易的盈亏
        let trade_history = self.portfolio.get_trade_history();
        
        // 使用HashMap记录每个品种的开仓价格，用于计算平仓盈亏
        let mut position_tracker: std::collections::HashMap<drl_trading_core::types::common::Symbol, Vec<(rust_decimal::Decimal, rust_decimal::Decimal)>> = std::collections::HashMap::new();
        
        for trade in trade_history {
            let symbol = &trade.symbol;
            let quantity = trade.quantity;
            let price = trade.price;
            
            let positions = position_tracker.entry(symbol.clone()).or_insert_with(Vec::new);
            
            match trade.side {
                drl_trading_core::types::execution::OrderSide::Buy => {
                    // 买入：记录开仓位置
                    positions.push((quantity, price));
                }
                drl_trading_core::types::execution::OrderSide::Sell => {
                    // 卖出：计算与之前买入的盈亏
                    let mut remaining_quantity = quantity;
                    
                    while remaining_quantity > rust_decimal::Decimal::ZERO && !positions.is_empty() {
                        let (buy_qty, buy_price) = positions[0];
                        
                        if buy_qty <= remaining_quantity {
                            // 完全平仓这个买入位置
                            let pnl = ((price - buy_price) * buy_qty).to_f64().unwrap_or(0.0);
                            
                            if pnl > 0.0 {
                                winning_trades += 1;
                                total_profit += pnl;
                                largest_win = largest_win.max(pnl);
                            } else if pnl < 0.0 {
                                losing_trades += 1;
                                total_loss += pnl.abs();
                                largest_loss = largest_loss.max(pnl.abs());
                            }
                            
                            remaining_quantity -= buy_qty;
                            positions.remove(0);
                        } else {
                            // 部分平仓
                            let pnl = ((price - buy_price) * remaining_quantity).to_f64().unwrap_or(0.0);
                            
                            if pnl > 0.0 {
                                winning_trades += 1;
                                total_profit += pnl;
                                largest_win = largest_win.max(pnl);
                            } else if pnl < 0.0 {
                                losing_trades += 1;
                                total_loss += pnl.abs();
                                largest_loss = largest_loss.max(pnl.abs());
                            }
                            
                            positions[0] = (buy_qty - remaining_quantity, buy_price);
                            remaining_quantity = rust_decimal::Decimal::ZERO;
                        }
                    }
                }
            }
        }
        
        (winning_trades, losing_trades, total_profit, total_loss, largest_win, largest_loss)
    }

    /// 获取奖励计算所需的指标 (f64版本，高性能)
    pub fn get_reward_metrics(&self) -> RewardMetrics {
        use rust_decimal::prelude::ToPrimitive;

        let current_equity = self.get_equity();
        let portfolio_summary = self.portfolio.get_summary();
        let performance_metrics = self.performance.get_performance_metrics();

        RewardMetrics {
            current_equity: current_equity.to_f64().unwrap_or(0.0),
            total_return: self.get_total_return().to_f64().unwrap_or(0.0),
            // 使用缓存的未实现盈亏
            unrealized_pnl: self.state.unrealized_pnl.to_f64().unwrap_or(0.0),

            // 风险指标
            max_drawdown: performance_metrics.max_drawdown.to_f64().unwrap_or(0.0),
            volatility: 0.0, // 简化：暂不计算波动率，可在后续版本中实现

            // 交易成本
            total_fees: self.state.total_fees.to_f64().unwrap_or(0.0),

            // 其他有用指标
            sharpe_ratio: 0.0, // 简化：暂不计算夏普比率，可在后续版本中实现
            win_rate: 0.0,     // 简化：暂不计算胜率，可在后续版本中实现
        }
    }

    /// 为高级奖励计算器提供持仓权重信息
    ///
    /// 计算各持仓相对于总权益的权重分布，用于集中度风险控制
    pub fn get_position_weights(
        &self,
        current_prices: &std::collections::HashMap<drl_trading_core::types::common::Symbol, f64>,
    ) -> std::collections::HashMap<drl_trading_core::types::common::Symbol, f64> {
        use rust_decimal::prelude::ToPrimitive;

        let total_equity = self.get_equity_f64();
        if total_equity <= 0.0 {
            return std::collections::HashMap::new();
        }

        let portfolio_summary = self.portfolio.get_summary();
        let mut weights = std::collections::HashMap::new();

        for symbol in &portfolio_summary.symbols {
            if let Some(position) = self.portfolio.get_position(symbol) {
                if position.quantity == rust_decimal::Decimal::ZERO {
                    continue;
                }

                let current_price = current_prices.get(symbol).copied().unwrap_or(0.0);
                if current_price <= 0.0 {
                    continue;
                }

                let quantity = position.quantity.to_f64().unwrap_or(0.0);
                let position_value = quantity.abs() * current_price;
                let weight = position_value / total_equity;

                weights.insert(symbol.clone(), weight);
            }
        }

        weights
    }

    /// 为高级奖励计算器提供持仓盈亏信息
    ///
    /// 返回所有持仓的未实现盈亏率，用于持仓状态奖励计算
    pub fn get_position_pnl_rates(
        &self,
        current_prices: &std::collections::HashMap<drl_trading_core::types::common::Symbol, f64>,
    ) -> std::collections::HashMap<drl_trading_core::types::common::Symbol, f64> {
        use rust_decimal::prelude::ToPrimitive;

        let portfolio_summary = self.portfolio.get_summary();
        let mut pnl_rates = std::collections::HashMap::new();

        for symbol in &portfolio_summary.symbols {
            if let Some(position) = self.portfolio.get_position(symbol) {
                if position.quantity == rust_decimal::Decimal::ZERO {
                    continue;
                }

                let current_price = current_prices.get(symbol).copied().unwrap_or(0.0);
                if current_price <= 0.0 {
                    continue;
                }

                let avg_price = position.average_entry_price.to_f64().unwrap_or(0.0);
                let quantity = position.quantity.to_f64().unwrap_or(0.0);

                // 计算该持仓的未实现盈亏率
                let position_value = quantity.abs() * current_price;
                let cost_basis = quantity.abs() * avg_price;

                if cost_basis > 0.0 {
                    let unrealized_pnl_rate = (position_value - cost_basis) / cost_basis;
                    pnl_rates.insert(symbol.clone(), unrealized_pnl_rate);
                }
            }
        }

        pnl_rates
    }

    /// 获取账户摘要信息 (f64版本，高性能，避免decimal传递)
    pub fn get_account_summary_f64(&self) -> AccountSummaryF64 {
        use rust_decimal::prelude::ToPrimitive;

        let current_equity = self.get_equity();
        let portfolio_summary = self.portfolio.get_summary();
        let performance_metrics = self.performance.get_performance_metrics();

        AccountSummaryF64 {
            // 资金信息
            total_balance: self.state.total_balance.to_f64().unwrap_or(0.0),
            available_balance: self.state.available_balance.to_f64().unwrap_or(0.0),
            frozen_funds: self.state.get_frozen_funds().to_f64().unwrap_or(0.0),
            realized_pnl: self.state.realized_pnl.to_f64().unwrap_or(0.0),
            // 使用缓存的未实现盈亏
            unrealized_pnl: self.state.unrealized_pnl.to_f64().unwrap_or(0.0),
            total_equity: current_equity.to_f64().unwrap_or(0.0),

            // 持仓信息
            position_count: portfolio_summary.position_count,
            total_market_value: portfolio_summary.total_market_value.to_f64().unwrap_or(0.0),

            // 业绩信息
            total_amount: 0.0,
            net_value: self.get_net_value().to_f64().unwrap_or(1.0),
            total_return: self.get_total_return().to_f64().unwrap_or(0.0),
            max_drawdown: performance_metrics.max_drawdown.to_f64().unwrap_or(0.0),

            // 其他信息
            total_fees: self.state.total_fees.to_f64().unwrap_or(0.0),
            initial_balance: self.state.initial_balance.to_f64().unwrap_or(0.0),
        }
    }

    /// 获取状态编码器专用的账户和持仓特征（只读版本，优化性能）
    ///
    /// 为状态编码器提供标准化的账户数据，避免编码器直接进行业务逻辑计算
    ///
    /// # 返回
    /// 账户特征和持仓特征的元组: (account_features, position_features)
    /// - account_features: [total_balance, equity, initial_balance, pnl_ratio]
    /// - position_features: [quantity, market_value, unrealized_pnl, pnl_ratio]
    pub fn get_encoding_features_readonly(&self, symbol: &drl_trading_core::types::common::Symbol) -> (Vec<f64>, Vec<f64>) {
        // 1. 账户特征计算（优化：使用缓存的未实现盈亏）
        let total_balance = self.state.total_balance.to_f64().unwrap_or(0.0);
        let initial_balance = self.state.initial_balance.to_f64().unwrap_or(100000.0);

        // 优化：使用Account缓存的未实现盈亏，避免portfolio状态计算
        let current_equity = self.state.total_balance + self.state.unrealized_pnl;
        let equity_f64 = current_equity.to_f64().unwrap_or(0.0);

        // 账户盈亏比 = (当前权益 - 初始余额) / 初始余额
        let account_pnl_ratio = if initial_balance > 0.0 { (equity_f64 - initial_balance) / initial_balance } else { 0.0 };

        let account_features = vec![total_balance, equity_f64, initial_balance, account_pnl_ratio];

        // 2. 持仓特征计算（优化：直接获取，减少重复计算）
        let position_features = if let Some(position) = self.portfolio.get_position(symbol) {
            let quantity = position.quantity.to_f64().unwrap_or(0.0);
            let avg_price = position.average_entry_price.to_f64().unwrap_or(0.0);

            // 优化：直接从portfolio获取当前价格，减少查找次数
            let current_price = self.portfolio.get_current_price(symbol).map(|p| p.to_f64().unwrap_or(0.0)).unwrap_or(avg_price);

            // 批量计算减少重复的乘法运算
            let market_value = quantity * current_price;
            let price_diff = current_price - avg_price;
            let unrealized_pnl = quantity * price_diff;

            // 持仓盈亏比计算优化
            let cost_basis = quantity.abs() * avg_price;
            let position_pnl_ratio = if cost_basis > 0.0 { unrealized_pnl / cost_basis } else { 0.0 };

            vec![quantity, market_value, unrealized_pnl, position_pnl_ratio]
        } else {
            // 无持仓时直接返回预定义的零向量，避免重复分配
            vec![0.0, 0.0, 0.0, 0.0]
        };

        (account_features, position_features)
    }

    /// 存入资金
    ///
    /// # 参数
    /// * `amount` - 存入金额
    pub fn deposit(&mut self, amount: Decimal) -> AccountResult<()> {
        self.state.deposit(amount)?;

        // 记录权益变化
        let current_equity = self.get_equity();
        let timestamp = chrono::Utc::now().timestamp_millis();
        self.performance.add_equity_point(timestamp, current_equity)?;

        log::info!("存入资金 {}, 当前权益: {}", amount, current_equity);
        Ok(())
    }

    /// 提取资金
    ///
    /// # 参数
    /// * `amount` - 提取金额
    pub fn withdraw(&mut self, amount: Decimal) -> AccountResult<()> {
        self.state.withdraw(amount)?;

        // 记录权益变化
        let current_equity = self.get_equity();
        let timestamp = chrono::Utc::now().timestamp_millis();
        self.performance.add_equity_point(timestamp, current_equity)?;

        log::info!("提取资金 {}, 当前权益: {}", amount, current_equity);
        Ok(())
    }

    /// 重置账户到初始状态
    ///
    /// # 参数
    /// * `new_initial_balance` - 新的初始余额
    pub fn reset(&mut self, new_initial_balance: Decimal) -> AccountResult<()> {
        self.state = AccountState::new(new_initial_balance)?;
        self.portfolio.clear();
        self.performance.reset(new_initial_balance)?;
        self.state.unrealized_pnl = Decimal::ZERO;

        log::info!("重置账户，新初始余额: {}", new_initial_balance);
        Ok(())
    }

    /// 验证账户状态一致性
    pub fn validate(&self) -> AccountResult<()> {
        // 验证各个组件的状态
        self.state.validate()?;
        self.portfolio.validate()?;

        // 验证组件间的一致性
        // 例如：确保总权益计算的一致性等
        log::debug!("账户状态验证通过");
        Ok(())
    }

    /// 计算交易手续费
    ///
    /// 这里使用简化的手续费计算，实际应用中可能需要更复杂的逻辑
    fn calculate_fee(&self, trade: &Trade) -> Decimal {
        let trade_value = trade.quantity * trade.price;
        // 简化：使用固定的手续费率 0.1%
        trade_value * Decimal::new(1, 3) // 0.001 = 0.1%
    }

    // ============ Getter方法 ============

    /// 获取性能分析器的引用
    pub fn get_performance(&self) -> &Performance {
        &self.performance
    }

    /// 获取性能分析器的可变引用
    pub fn get_performance_mut(&mut self) -> &mut Performance {
        &mut self.performance
    }

    /// 获取投资组合的引用
    pub fn get_portfolio(&self) -> &Portfolio {
        &self.portfolio
    }

    /// 获取投资组合的可变引用
    pub fn get_portfolio_mut(&mut self) -> &mut Portfolio {
        &mut self.portfolio
    }

    /// 获取账户状态的引用
    pub fn get_state(&self) -> &AccountState {
        &self.state
    }

    /// 获取总交易次数
    pub fn get_trade_amount(&self) -> i32 {
        self.portfolio.get_trade_amount() as i32
    }
    
    /// 获取性能指标（整合Portfolio和Performance数据）
    pub fn get_performance_metrics(&self) -> crate::performance::PerformanceMetrics {
        let (winning_trades, losing_trades, total_profit, total_loss, largest_win, largest_loss) = self.calculate_trade_stats();
        let total_trades = winning_trades + losing_trades;
        let average_win = if winning_trades > 0 { total_profit / winning_trades as f64 } else { 0.0 };
        let average_loss = if losing_trades > 0 { total_loss / losing_trades as f64 } else { 0.0 };
        let total_fees = self.state.total_fees.to_f64().unwrap_or(0.0);
        
        self.performance.generate_performance_metrics(
            total_trades, 
            winning_trades, 
            losing_trades, 
            average_win, 
            average_loss, 
            largest_win, 
            largest_loss, 
            total_fees
        )
    }
    
    /// 获取风险指标（整合Portfolio和Performance数据）
    pub fn get_risk_metrics(&self) -> crate::performance::RiskMetrics {
        let (winning_trades, losing_trades, total_profit, total_loss, _largest_win, _largest_loss) = self.calculate_trade_stats();
        let total_trades = winning_trades + losing_trades;
        
        // 计算胜率
        let win_rate = if total_trades > 0 { 
            winning_trades as f64 / total_trades as f64 
        } else { 
            0.0 
        };
        
        // 计算盈利因子 (总盈利 / 总亏损)
        let profit_factor = if total_loss > 0.0 { 
            total_profit / total_loss 
        } else if total_profit > 0.0 { 
            total_profit // 没有亏损时，盈利因子设为总盈利
        } else { 
            0.0 
        };
        
        // 获取持仓集中度
        let position_concentration = self.portfolio.calculate_position_concentration();
        
        self.performance.generate_risk_metrics(win_rate, profit_factor, position_concentration)
    }
}

/// 奖励计算专用指标结构
///
/// 为execution模块提供标准化的奖励计算接口，避免重复计算
#[derive(Debug, Clone)]
pub struct RewardMetrics {
    /// 当前总权益
    pub current_equity: f64,
    /// 总收益率
    pub total_return: f64,
    /// 未实现盈亏
    pub unrealized_pnl: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 波动率
    pub volatility: f64,
    /// 总手续费
    pub total_fees: f64,
    /// 夏普比率
    pub sharpe_ratio: f64,
    /// 胜率
    pub win_rate: f64,
}

/// 账户快照，用于奖励计算的不可变状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountSnapshot {
    /// 总权益
    pub total_equity: Decimal,
    /// 可用余额
    pub available_balance: Decimal,
    /// 已实现盈亏
    pub realized_pnl: Decimal,
    /// 未实现盈亏
    pub unrealized_pnl: Decimal,
    /// 初始余额
    pub initial_balance: Decimal,
    /// 持仓数量
    pub position_count: usize,
    /// 快照时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl AccountSnapshot {
    /// 从账户状态创建快照
    pub fn from_account(account: &Account) -> Self {
        // 使用Account缓存的未实现盈亏，避免portfolio状态计算
        let unrealized_pnl = account.state.unrealized_pnl;
        let total_equity = account.state.total_balance + unrealized_pnl;

        Self {
            total_equity,
            available_balance: account.state.available_balance,
            realized_pnl: account.state.realized_pnl,
            unrealized_pnl,
            initial_balance: account.state.initial_balance,
            position_count: account.portfolio.position_count(),
            timestamp: chrono::Utc::now(),
        }
    }

    /// 计算总收益率
    pub fn total_return(&self) -> Decimal {
        if self.initial_balance > Decimal::ZERO {
            (self.total_equity - self.initial_balance) / self.initial_balance
        } else {
            Decimal::ZERO
        }
    }

    /// 计算权益变化（相对于另一个快照）
    pub fn equity_change(&self, other: &AccountSnapshot) -> Decimal {
        self.total_equity - other.total_equity
    }
}

/// 账户摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountSummary {
    /// 资金信息
    pub total_balance: Decimal,
    pub available_balance: Decimal,
    pub frozen_funds: Decimal,
    pub realized_pnl: Decimal,
    pub unrealized_pnl: Decimal,
    pub total_equity: Decimal,

    /// 持仓信息
    pub position_count: usize,
    pub total_market_value: Decimal,

    /// 业绩信息
    pub net_value: Decimal,
    pub total_return: Decimal,
    pub max_drawdown: Decimal,

    /// 其他信息
    pub total_fees: Decimal,
    pub initial_balance: Decimal,
}

/// 高性能账户摘要（f64版本，避免decimal传递）
#[derive(Debug, Clone)]
pub struct AccountSummaryF64 {
    /// 资金信息
    pub total_balance: f64,
    pub available_balance: f64,
    pub frozen_funds: f64,
    pub realized_pnl: f64,
    pub unrealized_pnl: f64,
    pub total_equity: f64,

    /// 持仓信息
    pub position_count: usize,
    pub total_market_value: f64,
    pub total_amount: f64,

    /// 业绩信息
    pub net_value: f64,
    pub total_return: f64,
    pub max_drawdown: f64,

    /// 其他信息
    pub total_fees: f64,
    pub initial_balance: f64,
}
