[package]
name = "drl-trading-account"
version.workspace = true
edition.workspace = true
description = "DRL量化交易系统的账户与投资组合模块"
authors.workspace = true

[dependencies]
# 内部依赖
drl-trading-core.workspace = true

# 外部依赖
rust_decimal.workspace = true
serde.workspace = true
thiserror.workspace = true
log.workspace = true
chrono.workspace = true

[dev-dependencies]
rust_decimal_macros.workspace = true
tokio = { workspace = true, features = ["rt-multi-thread", "macros"] } 