//! # 学习模块配置
//! 
//! 定义深度强化学习训练过程中的所有超参数配置。

use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 深度强化学习配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LearnConfig {
    /// 通用配置
    pub common: CommonConfig,

    /// SAC算法配置
    pub sac: SACConfig,

    /// 神经网络配置
    pub network: NetworkConfig,

    /// 训练配置
    pub training: TrainingConfig,

    /// 经验回放配置
    pub replay: ReplayConfig,

    /// 设备配置
    pub device: DeviceConfig,

    /// 保存配置
    pub checkpoint: CheckpointConfig,
}

/// 通用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommonConfig {
    /// 状态维度
    pub state_dim: usize,

    /// 动作维度
    pub action_dim: usize,

    /// 最大动作值
    pub max_action: f64,

    /// 最小动作值
    pub min_action: f64,

    /// 随机种子
    pub seed: Option<u64>,
}

/// SAC算法配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SACConfig {
    /// 折扣因子
    pub gamma: f64,

    /// 软更新系数
    pub tau: f64,

    /// 熵温度系数初始值
    pub alpha_init: f64,

    /// 是否自动调整熵系数
    pub auto_entropy_tuning: bool,

    /// 目标熵值（如果为None则自动计算）
    pub target_entropy: Option<f64>,

    /// Q网络数量
    pub num_q_networks: usize,

    /// 延迟策略更新频率
    pub policy_delay: usize,
}

/// 神经网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// Actor网络配置
    pub actor: ActorNetworkConfig,

    /// Critic网络配置
    pub critic: CriticNetworkConfig,

    /// 状态编码器配置
    pub encoder: Option<EncoderConfig>,
}

/// Actor网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActorNetworkConfig {
    /// 隐藏层维度
    pub hidden_dims: Vec<usize>,

    /// 激活函数类型
    pub activation: ActivationType,

    /// 最后一层激活函数
    pub output_activation: ActivationType,

    /// 初始化类型
    pub init_type: InitializationType,

    /// 噪声标准差
    pub log_std_min: f64,

    /// 噪声标准差上界
    pub log_std_max: f64,
}

/// Critic网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CriticNetworkConfig {
    /// 隐藏层维度
    pub hidden_dims: Vec<usize>,

    /// 激活函数类型
    pub activation: ActivationType,

    /// 初始化类型
    pub init_type: InitializationType,
}

/// 编码器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncoderConfig {
    /// 编码器类型
    pub encoder_type: EncoderType,

    /// 输出维度
    pub output_dim: usize,

    /// 额外参数
    pub params: serde_json::Value,
}

/// 训练配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrainingConfig {
    /// 学习率配置
    pub learning_rates: LearningRateConfig,

    /// 批次大小
    pub batch_size: usize,

    /// 总回合数
    pub episodes: usize,

    /// 每回合步数
    pub steps_per_episode: usize,

    /// 预热步数（开始训练前的随机探索步数）
    pub warmup_steps: usize,

    /// 训练频率（每N步执行一次模型训练）
    pub train_frequency: usize,

    /// 评估频率（每N个回合输出一次详细统计）
    pub eval_frequency: usize,

    /// 梯度裁剪阈值
    pub grad_clip: Option<f64>,

    /// 权重衰减
    pub weight_decay: f64,
}

/// 学习率配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LearningRateConfig {
    /// Actor学习率
    pub actor: f64,

    /// Critic学习率
    pub critic: f64,

    /// 熵系数学习率
    pub alpha: f64,

    /// 学习率调度器类型
    pub scheduler: Option<SchedulerType>,

    /// 调度器参数
    pub scheduler_params: serde_json::Value,
}

/// 经验回放配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReplayConfig {
    /// 缓冲区大小
    pub capacity: usize,

    /// 优先级回放
    pub prioritized: bool,

    /// 优先级回放参数
    pub priority_params: Option<PriorityParams>,
}

/// 优先级回放参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriorityParams {
    /// alpha参数
    pub alpha: f64,

    /// beta初始值
    pub beta_start: f64,

    /// beta最终值
    pub beta_end: f64,

    /// beta调度步数
    pub beta_frames: usize,
}

/// 设备配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceConfig {
    /// 是否使用GPU
    pub use_gpu: bool,

    /// GPU设备ID
    pub gpu_id: Option<usize>,

    /// 是否启用混合精度训练
    pub mixed_precision: bool,
}

/// 检查点配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckpointConfig {
    /// 保存目录
    pub save_dir: PathBuf,

    /// 保存频率
    pub save_frequency: usize,

    /// 保留的检查点数量
    pub keep_checkpoints: usize,

    /// 是否保存最佳模型
    pub save_best: bool,

    /// 最佳模型评估指标
    pub best_metric: String,
}

/// 激活函数类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ActivationType {
    ReLU,
    Tanh,
    Sigmoid,
    LeakyReLU,
    ELU,
    GELU,
    Swish,
    Mish,
}

/// 初始化类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum InitializationType {
    Xavier,
    He,
    Normal,
    Uniform,
}

/// 编码器类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum EncoderType {
    MLP,
    CNN,
    RNN,
    LSTM,
    GRU,
    Transformer,
}

/// 学习率调度器类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum SchedulerType {
    StepLR,
    ExponentialLR,
    CosineAnnealingLR,
    ReduceLROnPlateau,
}

impl Default for LearnConfig {
    fn default() -> Self {
        Self {
            common: CommonConfig::default(),
            sac: SACConfig::default(),
            network: NetworkConfig::default(),
            training: TrainingConfig::default(),
            replay: ReplayConfig::default(),
            device: DeviceConfig::default(),
            checkpoint: CheckpointConfig::default(),
        }
    }
}

impl Default for CommonConfig {
    fn default() -> Self {
        Self {
            state_dim: 64,
            action_dim: 1,
            max_action: 1.0,
            min_action: -1.0,
            seed: Some(42),
        }
    }
}

impl Default for SACConfig {
    fn default() -> Self {
        Self {
            gamma: 0.99,
            tau: 0.005,
            alpha_init: 0.2,
            auto_entropy_tuning: true,
            target_entropy: None, // 将自动计算为 -动作维度
            num_q_networks: 2,
            policy_delay: 1,
        }
    }
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            actor: ActorNetworkConfig::default(),
            critic: CriticNetworkConfig::default(),
            encoder: None,
        }
    }
}

impl Default for ActorNetworkConfig {
    fn default() -> Self {
        Self {
            hidden_dims: vec![256, 256],
            activation: ActivationType::ReLU,
            output_activation: ActivationType::Tanh,
            init_type: InitializationType::Xavier,
            log_std_min: -20.0,
            log_std_max: 2.0,
        }
    }
}

impl Default for CriticNetworkConfig {
    fn default() -> Self {
        Self {
            hidden_dims: vec![256, 256],
            activation: ActivationType::ReLU,
            init_type: InitializationType::Xavier,
        }
    }
}

impl Default for TrainingConfig {
    fn default() -> Self {
        Self {
            learning_rates: LearningRateConfig::default(),
            batch_size: 256,
            episodes: 100,
            steps_per_episode: 1000,
            warmup_steps: 10_000,
            train_frequency: 4,
            eval_frequency: 10,
            grad_clip: Some(1.0),
            weight_decay: 1e-4,
        }
    }
}

impl Default for LearningRateConfig {
    fn default() -> Self {
        Self {
            actor: 3e-4,
            critic: 3e-4,
            alpha: 3e-4,
            scheduler: None,
            scheduler_params: serde_json::Value::Null,
        }
    }
}

impl Default for ReplayConfig {
    fn default() -> Self {
        Self {
            capacity: 1_000_000,
            prioritized: false,
            priority_params: None,
        }
    }
}

impl Default for DeviceConfig {
    fn default() -> Self {
        Self {
            use_gpu: true,
            gpu_id: None,
            mixed_precision: false,
        }
    }
}

impl Default for CheckpointConfig {
    fn default() -> Self {
        Self {
            save_dir: PathBuf::from("checkpoints"),
            save_frequency: 10_000,
            keep_checkpoints: 5,
            save_best: true,
            best_metric: "episode_reward".to_string(),
        }
    }
}

impl LearnConfig {
    /// 从文件加载配置
    pub fn from_file<P: AsRef<std::path::Path>>(path: P) -> Result<Self, Box<dyn std::error::Error>> {
        let content = std::fs::read_to_string(path)?;
        let config: Self = toml::from_str(&content)?;
        Ok(config)
    }

    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<std::path::Path>>(&self, path: P) -> Result<(), Box<dyn std::error::Error>> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }

    /// 验证配置有效性
    pub fn validate(&self) -> Result<(), crate::errors::LearnError> {
        // 验证维度
        if self.common.state_dim == 0 {
            return Err(crate::errors::LearnError::config("状态维度必须大于0"));
        }
        
        if self.common.action_dim == 0 {
            return Err(crate::errors::LearnError::config("动作维度必须大于0"));
        }

        // 验证SAC参数
        if !(0.0..=1.0).contains(&self.sac.gamma) {
            return Err(crate::errors::LearnError::config("折扣因子必须在[0,1]范围内"));
        }

        if !(0.0..=1.0).contains(&self.sac.tau) {
            return Err(crate::errors::LearnError::config("软更新系数必须在[0,1]范围内"));
        }

        // 验证学习率
        if self.training.learning_rates.actor <= 0.0 {
            return Err(crate::errors::LearnError::config("Actor学习率必须大于0"));
        }

        if self.training.learning_rates.critic <= 0.0 {
            return Err(crate::errors::LearnError::config("Critic学习率必须大于0"));
        }

        // 验证批次大小
        if self.training.batch_size == 0 {
            return Err(crate::errors::LearnError::config("批次大小必须大于0"));
        }

        // 验证回放缓冲区
        if self.replay.capacity == 0 {
            return Err(crate::errors::LearnError::config("回放缓冲区容量必须大于0"));
        }

        Ok(())
    }

    /// 计算目标熵值（如果未指定）
    pub fn get_target_entropy(&self) -> f64 {
        self.sac.target_entropy.unwrap_or_else(|| {
            -(self.common.action_dim as f64)
        })
    }
} 