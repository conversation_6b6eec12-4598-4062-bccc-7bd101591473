//! # SAC 智能体
//!
//! 实现 Soft Actor-Critic (SAC) 算法的智能体。

use burn::{
    module::{Module, Param},
    tensor::{
        backend::AutodiffBackend, 
        cast::ToElement,
        Tensor
    },
    prelude::Backend,
};
use std::time::{Instant, Duration};
use crate::{
    errors::{LearnError, LearnResult},
    model::{Actor, ActorConfig, Critic, CriticConfig, Model},
};
use serde::{Serialize, Deserialize};

/// SAC 智能体配置
#[derive(Debug, Clone)]
pub struct SacAgentConfig {
    /// Actor 网络配置
    pub actor_config: ActorConfig,
    /// Critic 网络配置
    pub critic_config: CriticConfig,
    /// 学习率
    pub learning_rate: f64,
    /// 折扣因子
    pub gamma: f64,
    /// 软更新系数
    pub tau: f64,
    /// 自动调节温度参数
    pub auto_alpha: bool,
    /// 初始温度参数
    pub alpha: f64,
    /// 目标熵（用于自动调节温度）
    pub target_entropy: Option<f64>,
}

impl Default for SacAgentConfig {
    fn default() -> Self {
        Self {
            actor_config: ActorConfig::new(8, 4), // 默认状态和动作维度
            critic_config: CriticConfig::new(8, 4),
            learning_rate: 3e-4,
            gamma: 0.99,
            tau: 0.005,
            auto_alpha: true,
            alpha: 0.2,
            target_entropy: None,
        }
    }
}

impl SacAgentConfig {
    /// 从 LearnConfig 创建 SacAgentConfig
    pub fn from_learn_config(learn_config: &crate::config::LearnConfig) -> Self {
        // 创建 Actor 配置
        let actor_config = ActorConfig {
            state_dim: learn_config.common.state_dim,
            action_dim: learn_config.common.action_dim,
            hidden_dims: learn_config.network.actor.hidden_dims.clone(),
            activation: Self::convert_activation_type(learn_config.network.actor.activation.clone()),
            init_type: Self::convert_init_type(learn_config.network.actor.init_type.clone()),
            log_std_min: learn_config.network.actor.log_std_min,
            log_std_max: learn_config.network.actor.log_std_max,
            action_scale: learn_config.common.max_action,
        };

        // 创建 Critic 配置
        let critic_config = CriticConfig {
            state_dim: learn_config.common.state_dim,
            action_dim: learn_config.common.action_dim,
            hidden_dims: learn_config.network.critic.hidden_dims.clone(),
            activation: Self::convert_activation_type(learn_config.network.critic.activation.clone()),
            init_type: Self::convert_init_type(learn_config.network.critic.init_type.clone()),
        };

        Self {
            actor_config,
            critic_config,
            learning_rate: learn_config.training.learning_rates.actor,
            gamma: learn_config.sac.gamma,
            tau: learn_config.sac.tau,
            auto_alpha: learn_config.sac.auto_entropy_tuning,
            alpha: learn_config.sac.alpha_init,
            target_entropy: learn_config.sac.target_entropy,
        }
    }

    /// 转换激活函数类型
    fn convert_activation_type(config_activation: crate::config::ActivationType) -> crate::model::ActivationType {
        match config_activation {
            crate::config::ActivationType::ReLU => crate::model::ActivationType::ReLU,
            crate::config::ActivationType::Tanh => crate::model::ActivationType::Tanh,
            crate::config::ActivationType::Sigmoid => crate::model::ActivationType::Sigmoid,
            crate::config::ActivationType::LeakyReLU => crate::model::ActivationType::LeakyReLU,
            crate::config::ActivationType::ELU => crate::model::ActivationType::ELU,
            crate::config::ActivationType::GELU => crate::model::ActivationType::GELU,
            crate::config::ActivationType::Swish => crate::model::ActivationType::Swish,
            crate::config::ActivationType::Mish => crate::model::ActivationType::Mish,
        }
    }

    /// 转换初始化类型
    fn convert_init_type(config_init: crate::config::InitializationType) -> crate::model::InitializationType {
        match config_init {
            crate::config::InitializationType::Xavier => crate::model::InitializationType::Xavier,
            crate::config::InitializationType::He => crate::model::InitializationType::Xavier, // He 映射到 Xavier
            crate::config::InitializationType::Normal => crate::model::InitializationType::Normal,
            crate::config::InitializationType::Uniform => crate::model::InitializationType::Uniform,
        }
    }
}

/// SAC 智能体
#[derive(Module, Debug)]
pub struct SacAgent<B: Backend> {
    pub actor: Actor<B>,
    pub critic: Critic<B>,
    pub log_alpha: Option<Param<Tensor<B, 1>>>,
}

impl<B: AutodiffBackend> SacAgent<B> {
    /// 创建新的 SAC 智能体
    pub fn new(config: &SacAgentConfig, device: &B::Device) -> LearnResult<Self> {
        let actor = Actor::new(&config.actor_config, device);
        let critic = Critic::new(&config.critic_config, device);
        
        let log_alpha = if config.auto_alpha {
            let _target_entropy = config.target_entropy.unwrap_or_else(|| {
                -(config.actor_config.action_dim as f64)
            });
            Some(Param::from_tensor(Tensor::zeros([1], device)))
        } else {
            None
        };

        Ok(Self {
            actor,
            critic,
            log_alpha,
        })
    }

    /// 便利构造函数：使用默认设备  
    pub fn with_default_device(config: &SacAgentConfig) -> LearnResult<Self> 
    where 
        B: Backend,
        B::Device: Default,
    {
        let device = B::Device::default();
        Self::new(config, &device)
    }

    /// 初始化智能体（占位符实现）
    pub fn initialize(&mut self) -> LearnResult<()> {
        // TODO: 实现智能体初始化逻辑
        log::info!("智能体初始化完成");
        Ok(())
    }

    /// 选择动作（离散版本）- 优化版：直接返回动作索引
    pub fn select_action(&self, state: &[f64], training: bool) -> LearnResult<usize> {
        // 将状态转换为张量
        let state_vec: Vec<f32> = state.iter().map(|&x| x as f32).collect();
        let state_tensor_1d: Tensor<B, 1> = Tensor::from_floats(
            state_vec.as_slice(), 
            &self.actor.devices()[0]
        );
        let state_tensor: Tensor<B, 2> = state_tensor_1d.reshape([1, state.len()]); // 添加批次维度

        // 获取动作概率分布
        let action_probs = self.actor.get_action_probabilities(
            state_tensor, 
            crate::model::ActivationType::ReLU
        );

        // 转换为Vec<f32>格式 - 先移除批次维度，然后获取数据
        let probs_1d: Tensor<B, 1> = action_probs.squeeze_dims(&[0]);
        let probs_data: Vec<f32> = probs_1d.into_data().to_vec().unwrap();
        
        if training {
            // 训练时：从概率分布中采样（加权随机采样）
            Ok(self.sample_from_probabilities(&probs_data))
        } else {
            // 评估时：选择概率最高的动作（argmax）
            let action_index = probs_data.iter()
                .enumerate()
                .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap())
                .map(|(i, _)| i)
                .unwrap_or(0); // 默认选择Hold动作（索引0）
            
            Ok(action_index)
        }
    }

    /// 选择动作（支持动作掩码版本）- 核心动作掩码技术
    /// 
    /// 通过动作掩码确保智能体只从合法动作中选择，实现100%样本效率
    pub fn select_action_with_mask(&self, state: &[f64], action_mask: &[bool], training: bool) -> LearnResult<usize> {
        // 1. 获取原始动作概率分布
        let state_vec: Vec<f32> = state.iter().map(|&x| x as f32).collect();
        let state_tensor_1d: Tensor<B, 1> = Tensor::from_floats(
            state_vec.as_slice(), 
            &self.actor.devices()[0]
        );
        let state_tensor: Tensor<B, 2> = state_tensor_1d.reshape([1, state.len()]);

        let action_probs = self.actor.get_action_probabilities(
            state_tensor, 
            crate::model::ActivationType::ReLU
        );

        let probs_1d: Tensor<B, 1> = action_probs.squeeze_dims(&[0]);
        let mut probs_data: Vec<f32> = probs_1d.into_data().to_vec().unwrap();
        
        // 2. 应用动作掩码 - 关键步骤！
        for (i, &is_valid) in action_mask.iter().enumerate() {
            if !is_valid && i < probs_data.len() {
                probs_data[i] = 0.0; // 将无效动作的概率设为0
            }
        }
        
        // 3. 重新归一化概率分布
        let sum: f32 = probs_data.iter().sum();
        if sum > 0.0 {
            for prob in probs_data.iter_mut() {
                *prob /= sum;
            }
        } else {
            // 如果所有动作都被屏蔽，强制选择Hold动作（索引0）
            log::warn!("所有动作都被屏蔽，强制选择Hold动作");
            return Ok(0);
        }
        
        // 4. 从掩码后的分布中选择动作
        if training {
            // 训练时：从掩码后的概率分布中采样
            Ok(self.sample_from_probabilities(&probs_data))
        } else {
            // 评估时：选择掩码后概率最高的动作
            let action_index = probs_data.iter()
                .enumerate()
                .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap())
                .map(|(i, _)| i)
                .unwrap_or(0);
            
            Ok(action_index)
        }
    }

    /// 支持动作掩码的高性能版本（带性能监控）
    pub fn select_action_with_mask_profiling(
        &self, 
        state: &[f64], 
        action_mask: &[bool],
        training: bool,
        stats: &mut SelectActionPerformanceStats
    ) -> LearnResult<usize> {
        let total_start = Instant::now();

        // 步骤1-6: 与原版本相同
        let conv_start = Instant::now();
        let state_vec: Vec<f32> = state.iter().map(|&x| x as f32).collect();
        stats.type_conversion_time += conv_start.elapsed();

        let tensor_create_start = Instant::now();
        let state_tensor_1d: Tensor<B, 1> = Tensor::from_floats(
            state_vec.as_slice(), 
            &self.actor.devices()[0]
        );
        stats.tensor_creation_time += tensor_create_start.elapsed();

        let reshape_start = Instant::now();
        let state_tensor: Tensor<B, 2> = state_tensor_1d.reshape([1, state.len()]);
        stats.tensor_reshape_time += reshape_start.elapsed();

        let forward_start = Instant::now();
        let action_probs = self.actor.get_action_probabilities(
            state_tensor, 
            crate::model::ActivationType::ReLU
        );
        stats.forward_pass_time += forward_start.elapsed();

        let squeeze_start = Instant::now();
        let probs_1d: Tensor<B, 1> = action_probs.squeeze_dims(&[0]);
        stats.tensor_squeeze_time += squeeze_start.elapsed();

        let extract_start = Instant::now();
        let mut probs_data: Vec<f32> = probs_1d.into_data().to_vec().unwrap();
        stats.data_extraction_time += extract_start.elapsed();

        // 步骤7: 应用动作掩码（新增）
        let mask_start = Instant::now();
        for (i, &is_valid) in action_mask.iter().enumerate() {
            if !is_valid && i < probs_data.len() {
                probs_data[i] = 0.0;
            }
        }
        
        // 重新归一化
        let sum: f32 = probs_data.iter().sum();
        if sum > 0.0 {
            for prob in probs_data.iter_mut() {
                *prob /= sum;
            }
        } else {
            log::warn!("所有动作都被屏蔽，强制选择Hold动作");
            stats.total_time += total_start.elapsed();
            stats.call_count += 1;
            return Ok(0);
        }
        
        // 这里应该添加到新的掩码时间统计中，但为了保持兼容性，
        // 暂时计入action_selection_time
        
        // 步骤8: 动作选择
        let action_start = Instant::now();
        let result = if training {
            self.sample_from_probabilities(&probs_data)
        } else {
            probs_data.iter()
                .enumerate()
                .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap())
                .map(|(i, _)| i)
                .unwrap_or(0)
        };
        
        let mask_and_action_time = mask_start.elapsed();
        stats.action_selection_time += mask_and_action_time;

        stats.total_time += total_start.elapsed();
        stats.call_count += 1;

        Ok(result)
    }

    /// 选择动作（带性能监控版本）- 用于性能分析
    pub fn select_action_with_profiling(
        &self, 
        state: &[f64], 
        training: bool,
        stats: &mut SelectActionPerformanceStats
    ) -> LearnResult<usize> {
        let total_start = Instant::now();

        // 步骤1: 数据类型转换 (f64 → f32)
        let conv_start = Instant::now();
        let state_vec: Vec<f32> = state.iter().map(|&x| x as f32).collect();
        stats.type_conversion_time += conv_start.elapsed();

        // 步骤2: 1D张量创建 (Array → Tensor)
        let tensor_create_start = Instant::now();
        let state_tensor_1d: Tensor<B, 1> = Tensor::from_floats(
            state_vec.as_slice(), 
            &self.actor.devices()[0]
        );
        stats.tensor_creation_time += tensor_create_start.elapsed();

        // 步骤3: 张量变形 (1D → 2D)
        let reshape_start = Instant::now();
        let state_tensor: Tensor<B, 2> = state_tensor_1d.reshape([1, state.len()]);
        stats.tensor_reshape_time += reshape_start.elapsed();

        // 步骤4: 神经网络前向传播
        let forward_start = Instant::now();
        let action_probs = self.actor.get_action_probabilities(
            state_tensor, 
            crate::model::ActivationType::ReLU
        );
        stats.forward_pass_time += forward_start.elapsed();

        // 步骤5: 张量降维 (2D → 1D)
        let squeeze_start = Instant::now();
        let probs_1d: Tensor<B, 1> = action_probs.squeeze_dims(&[0]);
        stats.tensor_squeeze_time += squeeze_start.elapsed();

        // 步骤6: 数据提取 (Tensor → Vec)
        let extract_start = Instant::now();
        let probs_data: Vec<f32> = probs_1d.into_data().to_vec().unwrap();
        stats.data_extraction_time += extract_start.elapsed();

        // 步骤7: 动作选择 (采样/argmax)
        let action_start = Instant::now();
        let result = if training {
            // 训练时：从概率分布中采样（加权随机采样）
            self.sample_from_probabilities(&probs_data)
        } else {
            // 评估时：选择概率最高的动作（argmax）
            probs_data.iter()
                .enumerate()
                .max_by(|(_, a), (_, b)| a.partial_cmp(b).unwrap())
                .map(|(i, _)| i)
                .unwrap_or(0) // 默认选择Hold动作（索引0）
        };
        stats.action_selection_time += action_start.elapsed();

        // 更新总统计
        stats.total_time += total_start.elapsed();
        stats.call_count += 1;

        Ok(result)
    }

    /// 从概率分布中采样动作索引
    fn sample_from_probabilities(&self, probs: &[f32]) -> usize {
        use rand::Rng;
        let mut rng = rand::rng();
        let rand_val: f32 = rng.random_range(0.0..1.0);
        
        let mut cumulative = 0.0;
        for (i, &prob) in probs.iter().enumerate() {
            cumulative += prob;
            if rand_val <= cumulative {
                return i;
            }
        }
        
        // 如果没有找到，返回最后一个索引
        probs.len() - 1
    }

    /// 评估智能体性能
    pub fn evaluate(&self, _num_episodes: usize) -> LearnResult<EvaluationMetrics> {
        // TODO: 实现评估逻辑
        Ok(EvaluationMetrics {
            average_reward: 0.0,
            total_episodes: 0,
            success_rate: 0.0,
        })
    }

    /// 从文件加载模型
    pub fn load_from_file(&mut self, path: &str) -> LearnResult<()> {
        use std::path::Path;
        use burn::record::{BinFileRecorder, FullPrecisionSettings, Recorder};
        
        let path = Path::new(path);
        
        log::info!("开始加载模型: {}", path.display());
        
        // 使用 burn 的记录器加载模型
        let recorder = BinFileRecorder::<FullPrecisionSettings>::new();
        
        // 加载 Actor 网络
        let actor_path = path.with_extension("actor.bin");
        if actor_path.exists() {
            match recorder.load(actor_path.clone(), &self.actor.devices()[0]) {
                Ok(record) => {
                    self.actor = self.actor.clone().load_record(record);
                    log::info!("Actor 网络加载成功");
                }
                Err(e) => {
                    return Err(LearnError::ModelLoad(format!("加载 Actor 失败: {}", e)));
                }
            }
        }
        
        // 加载 Critic 网络
        let critic_path = path.with_extension("critic.bin");
        if critic_path.exists() {
            match recorder.load(critic_path.clone(), &self.critic.devices()[0]) {
                Ok(record) => {
                    self.critic = self.critic.clone().load_record(record);
                    log::info!("Critic 网络加载成功");
                }
                Err(e) => {
                    return Err(LearnError::ModelLoad(format!("加载 Critic 失败: {}", e)));
                }
            }
        }
        
        // 加载 alpha 参数（如果存在）
        let alpha_path = path.with_extension("alpha.bin");
        if alpha_path.exists() && self.log_alpha.is_some() {
            match recorder.load(alpha_path.clone(), &self.actor.devices()[0]) {
                Ok(record) => {
                    if let Some(ref mut log_alpha) = self.log_alpha {
                        *log_alpha = log_alpha.clone().load_record(record);
                        log::info!("Alpha 参数加载成功");
                    }
                }
                Err(e) => {
                    log::warn!("加载 Alpha 参数失败: {}", e);
                }
            }
        }
        
        log::info!("模型加载完成: {}", path.display());
        Ok(())
    }

    /// 保存模型到文件
    pub fn save_to_file(&self, path: &str) -> LearnResult<()> {
        use std::path::Path;
        use std::fs;
        use burn::record::{BinFileRecorder, FullPrecisionSettings, Recorder};
        
        let path = Path::new(path);
        
        // 确保目录存在
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| LearnError::ModelSave(format!("创建目录失败: {}", e)))?;
        }
        
        log::info!("开始保存模型: {}", path.display());
        
        // 使用 burn 的记录器保存模型
        let recorder = BinFileRecorder::<FullPrecisionSettings>::new();
        
        // 保存 Actor 网络
        let actor_path = path.with_extension("actor.bin");
        let actor_record = self.actor.state();  // 优化：使用state()而不是clone().into_record()
        recorder.record(actor_record, actor_path.clone())
            .map_err(|e| LearnError::ModelSave(format!("保存 Actor 失败: {}", e)))?;
        log::info!("Actor 网络保存成功: {}", actor_path.display());
        
        // 保存 Critic 网络
        let critic_path = path.with_extension("critic.bin");
        let critic_record = self.critic.state();  // 优化：使用state()而不是clone().into_record()
        recorder.record(critic_record, critic_path.clone())
            .map_err(|e| LearnError::ModelSave(format!("保存 Critic 失败: {}", e)))?;
        log::info!("Critic 网络保存成功: {}", critic_path.display());
        
        // 保存 alpha 参数（如果存在）
        if let Some(ref log_alpha) = self.log_alpha {
            let alpha_path = path.with_extension("alpha.bin");
            let alpha_record = log_alpha.clone().into_record();
            recorder.record(alpha_record, alpha_path.clone())
                .map_err(|e| LearnError::ModelSave(format!("保存 Alpha 失败: {}", e)))?;
            log::info!("Alpha 参数保存成功: {}", alpha_path.display());
        }
        
        // 保存元数据信息
        let metadata = ModelMetadata {
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs()
                .to_string(),
            actor_config: format!("{:?}", std::any::type_name::<crate::model::Actor<B>>()),
            critic_config: format!("{:?}", std::any::type_name::<crate::model::Critic<B>>()),
            version: "1.0.0".to_string(),
        };
        
        let metadata_path = path.with_extension("metadata.json");
        let metadata_json = serde_json::to_string_pretty(&metadata)
            .map_err(|e| LearnError::ModelSave(format!("序列化元数据失败: {}", e)))?;
        
        fs::write(&metadata_path, metadata_json)
            .map_err(|e| LearnError::ModelSave(format!("写入元数据失败: {}", e)))?;
        
        log::info!("模型保存完成: {}", path.display());
        Ok(())
    }

    /// 获取 actor 配置的激活类型（辅助方法）
    fn actor_config_activation(&self) -> crate::model::ActivationType {
        // 使用默认激活类型，实际应该从配置中获取
        crate::model::ActivationType::ReLU
    }

    /// 获取动作（用于训练）
    pub fn get_action(&self, state: Tensor<B, 2>, config: &SacAgentConfig) -> (Tensor<B, 2>, Tensor<B, 2>) {
        self.actor.sample_action(
            state,
            config.actor_config.activation,
            config.actor_config.log_std_min,
            config.actor_config.log_std_max,
            config.actor_config.action_scale,
        )
    }

    /// 获取确定性动作（用于评估）
    pub fn get_deterministic_action(&self, state: Tensor<B, 2>, config: &SacAgentConfig) -> Tensor<B, 2> {
        self.actor.deterministic_action(
            state,
            config.actor_config.activation,
            config.actor_config.action_scale,
        )
    }

    /// 计算Q值（主网络）
    pub fn compute_q_values(&self, state: Tensor<B, 2>, action: Tensor<B, 2>, config: &SacAgentConfig) -> (Tensor<B, 2>, Tensor<B, 2>) {
        self.critic.forward(state, action, config.critic_config.activation)
    }

    /// 计算目标Q值（目标网络）
    pub fn compute_target_q_values(&self, state: Tensor<B, 2>, action: Tensor<B, 2>, config: &SacAgentConfig) -> (Tensor<B, 2>, Tensor<B, 2>) {
        self.critic.forward_target(state, action, config.critic_config.activation)
    }

    /// 获取当前Alpha值
    pub fn get_alpha(&self, config: &SacAgentConfig) -> f64 {
        if config.auto_alpha {
            if let Some(ref log_alpha) = self.log_alpha {
                log_alpha.val().mean().into_scalar().to_f32() as f64
            } else {
                config.alpha
            }
        } else {
            config.alpha
        }
    }

    /// 软更新目标网络
    pub fn soft_update_targets(&mut self, config: &SacAgentConfig) -> LearnResult<()> {
        self.critic.soft_update(config.tau)?;
        Ok(())
    }

    /// 获取Actor网络引用
    pub fn actor(&self) -> &Actor<B> {
        &self.actor
    }

    /// 获取Critic网络引用
    pub fn critic(&self) -> &Critic<B> {
        &self.critic
    }

    /// 获取log_alpha参数的可变引用
    pub fn log_alpha_mut(&mut self) -> Option<&mut Param<Tensor<B, 1>>> {
        self.log_alpha.as_mut()
    }
}

impl<B: AutodiffBackend> Model<B> for SacAgent<B> {
    type Record = <Self as Module<B>>::Record;

    fn state(&self) -> Self::Record {
        self.clone().into_record()
    }

    fn load(&mut self, _record: &Self::Record) -> LearnResult<()> {
        // TODO: 实现适当的记录加载
        Ok(())
    }

    fn soft_update(&mut self, tau: f64) -> LearnResult<()> {
        self.critic.soft_update(tau)
    }
}

/// 评估指标
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct EvaluationMetrics {
    pub average_reward: f64,
    pub total_episodes: usize,
    pub success_rate: f64,
}

/// 模型元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelMetadata {
    pub timestamp: String,
    pub actor_config: String,
    pub critic_config: String,
    pub version: String,
}

/// select_action 函数性能统计
#[derive(Debug, Clone)]
pub struct SelectActionPerformanceStats {
    /// 数据类型转换耗时 (f64 → f32)
    pub type_conversion_time: Duration,
    /// 1D张量创建耗时 (Array → Tensor)
    pub tensor_creation_time: Duration,
    /// 张量变形耗时 (1D → 2D)
    pub tensor_reshape_time: Duration,
    /// 神经网络前向传播耗时
    pub forward_pass_time: Duration,
    /// 张量降维耗时 (2D → 1D)
    pub tensor_squeeze_time: Duration,
    /// 数据提取耗时 (Tensor → Vec)
    pub data_extraction_time: Duration,
    /// 动作选择耗时 (采样/argmax)
    pub action_selection_time: Duration,
    /// 总耗时
    pub total_time: Duration,
    /// 调用次数
    pub call_count: u64,
}

impl Default for SelectActionPerformanceStats {
    fn default() -> Self {
        Self {
            type_conversion_time: Duration::ZERO,
            tensor_creation_time: Duration::ZERO,
            tensor_reshape_time: Duration::ZERO,
            forward_pass_time: Duration::ZERO,
            tensor_squeeze_time: Duration::ZERO,
            data_extraction_time: Duration::ZERO,
            action_selection_time: Duration::ZERO,
            total_time: Duration::ZERO,
            call_count: 0,
        }
    }
}

impl SelectActionPerformanceStats {
    /// 打印性能统计报告
    pub fn print_report(&self) {
        if self.call_count == 0 {
            println!("🔍 select_action 性能分析：暂无数据");
            return;
        }

        let avg_total = self.total_time.as_nanos() as f64 / self.call_count as f64;
        
        println!("\n🔬 select_action 函数性能分析报告");
        println!("{}", "=".repeat(50));
        println!("📊 统计信息：");
        println!("  调用次数: {}", self.call_count);
        println!("  总耗时: {:.2}ms", self.total_time.as_secs_f64() * 1000.0);
        println!("  平均耗时: {:.2}μs", avg_total / 1000.0);
        
        println!("\n⏱️  详细步骤耗时分析：");
        self.print_step_analysis("1. 数据类型转换 (f64→f32)", &self.type_conversion_time);
        self.print_step_analysis("2. 1D张量创建", &self.tensor_creation_time);
        self.print_step_analysis("3. 张量变形 (1D→2D)", &self.tensor_reshape_time);
        self.print_step_analysis("4. 神经网络前向传播", &self.forward_pass_time);
        self.print_step_analysis("5. 张量降维 (2D→1D)", &self.tensor_squeeze_time);
        self.print_step_analysis("6. 数据提取 (Tensor→Vec)", &self.data_extraction_time);
        self.print_step_analysis("7. 动作选择 (采样/argmax)", &self.action_selection_time);
        
        println!("\n🚩 性能瓶颈识别：");
        self.identify_bottlenecks();
    }
    
    fn print_step_analysis(&self, step_name: &str, duration: &Duration) {
        let avg_time = duration.as_nanos() as f64 / self.call_count as f64;
        let percentage = (duration.as_nanos() as f64 / self.total_time.as_nanos() as f64) * 100.0;
        println!("  {:<25} 平均: {:.2}μs  占比: {:.1}%", 
                 step_name, avg_time / 1000.0, percentage);
    }
    
    fn identify_bottlenecks(&self) {
        let total_nanos = self.total_time.as_nanos() as f64;
        let steps = vec![
            ("数据类型转换", self.type_conversion_time.as_nanos() as f64),
            ("1D张量创建", self.tensor_creation_time.as_nanos() as f64),
            ("张量变形", self.tensor_reshape_time.as_nanos() as f64),
            ("神经网络前向传播", self.forward_pass_time.as_nanos() as f64),
            ("张量降维", self.tensor_squeeze_time.as_nanos() as f64),
            ("数据提取", self.data_extraction_time.as_nanos() as f64),
            ("动作选择", self.action_selection_time.as_nanos() as f64),
        ];
        
        let mut sorted_steps: Vec<_> = steps.iter()
            .map(|(name, time)| (name, time / total_nanos * 100.0))
            .collect();
        sorted_steps.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());
        
        println!("  最耗时的3个步骤：");
        for (i, (name, percentage)) in sorted_steps.iter().take(3).enumerate() {
            let icon = match i {
                0 => "🥇",
                1 => "🥈", 
                2 => "🥉",
                _ => "  ",
            };
            println!("  {} {}: {:.1}%", icon, name, percentage);
        }
        
        // 性能建议
        println!("\n💡 优化建议：");
        if sorted_steps[0].1 > 30.0 {
            match sorted_steps[0].0 {
                &"神经网络前向传播" => println!("  - 考虑使用批量推理减少GPU调用开销"),
                &"1D张量创建" => println!("  - 考虑重用张量，避免频繁GPU内存分配"),
                &"数据类型转换" => println!("  - 考虑直接使用f32数据源，避免转换开销"),
                &"数据提取" => println!("  - 考虑优化张量到CPU的数据传输"),
                _ => println!("  - 针对最耗时步骤进行专项优化"),
            }
        }
    }
}
