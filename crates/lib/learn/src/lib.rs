//! # DRL Trading Learn - 深度强化学习交易智能体
//!
//! 这个库提供了深度强化学习交易系统的学习模块。
#![recursion_limit = "256"] 
use burn::backend::{Autodiff, Wgpu, NdArray, wgpu};
use burn::backend::wgpu::WgpuDevice;

pub mod agent;
pub mod config;
pub mod errors;
pub mod model;
pub mod trainer;

// 重新导出主要类型
pub use agent::{SacAgent, SacAgentConfig, EvaluationMetrics, SelectActionPerformanceStats};
pub use config::LearnConfig;
pub use errors::{LearnError, LearnResult};
pub use trainer::{Sac<PERSON><PERSON><PERSON>, ExperienceBatcher, TrainingMetrics, Experience, ReplayBuffer};



// 定义您的后端类型
type AppBackend = Wgpu;

// 导出默认后端类型，方便应用层使用
// 根据feature flag选择backend：默认GPU，可选CPU-only
#[cfg(feature = "cpu-only")]
pub type DefaultBackend = Autodiff<NdArray>;
#[cfg(not(feature = "cpu-only"))]
pub type DefaultBackend = Autodiff<Wgpu>;

pub type DefaultSacAgent = SacAgent<DefaultBackend>;
pub type DefaultSacTrainer = SacTrainer<DefaultBackend>;

/// 创建默认的SAC智能体的便利函数
pub fn create_default_sac_agent(config: &SacAgentConfig) -> LearnResult<DefaultSacAgent> {
    let device = create_best_device();
    SacAgent::new(config, &device)
}

/// 智能设备选择：根据编译特性选择合适的设备
#[cfg(feature = "cpu-only")]
pub fn create_best_device() -> burn::backend::ndarray::NdArrayDevice {
    log::info!("🖥️  使用CPU训练 (NdArray backend)");
    log::info!("   性能: 适中，兼容性好");
    burn::backend::ndarray::NdArrayDevice::default()
}


#[cfg(not(feature = "cpu-only"))]
pub fn create_best_device() -> WgpuDevice {
    let device = WgpuDevice::default();
    //log::warn!("✅ Burn-WGPU已选择设备: {:?}", device);
    device
}



pub mod test_utils {
    use super::*;
    use burn::tensor::Tensor;
    
    /// 创建测试用的简单配置
    pub fn create_test_config() -> SacAgentConfig {
        use crate::model::{ActorConfig, CriticConfig, ActivationType, InitializationType};
        
        let actor_config = ActorConfig {
            state_dim: 10,
            action_dim: 2,
            hidden_dims: vec![64, 32],
            activation: ActivationType::ReLU,
            init_type: InitializationType::Xavier,
            log_std_min: -20.0,
            log_std_max: 2.0,
            action_scale: 1.0,
        };
        
        let critic_config = CriticConfig {
            state_dim: 10,
            action_dim: 2,
            hidden_dims: vec![64, 32],
            activation: ActivationType::ReLU,
            init_type: InitializationType::Xavier,
        };
        
        SacAgentConfig {
            actor_config,
            critic_config,
            learning_rate: 3e-4,
            gamma: 0.99,
            tau: 0.005,
            auto_alpha: true,
            alpha: 0.2,
            target_entropy: None,
        }
    }
    
    /// 检测 WGPU 设备是否可用
    pub fn check_wgpu_available() -> bool {
        match std::panic::catch_unwind(|| {
            let device = burn::backend::wgpu::WgpuDevice::default();
            // 尝试创建一个简单的张量
            let test_tensor: Tensor<Wgpu, 2> = Tensor::zeros([2, 2], &device);
            test_tensor.sum().into_scalar() >= 0.0
        }) {
            Ok(result) => result,
            Err(_) => false,
        }
    }
    
    /// 性能测试工具
    pub struct PerformanceMonitor {
        start_time: std::time::Instant,
        memory_start: Option<usize>,
    }
    
    impl PerformanceMonitor {
        pub fn new() -> Self {
            Self {
                start_time: std::time::Instant::now(),
                memory_start: Self::get_memory_usage(),
            }
        }
        
        pub fn elapsed(&self) -> std::time::Duration {
            self.start_time.elapsed()
        }
        
        pub fn memory_delta(&self) -> Option<isize> {
            if let (Some(start), Some(current)) = (self.memory_start, Self::get_memory_usage()) {
                Some(current as isize - start as isize)
            } else {
                None
            }
        }
        
        fn get_memory_usage() -> Option<usize> {
            // 简化的内存使用检测
            // 在实际环境中可以使用更精确的方法
            None
        }
    }
}

#[cfg(test)]
mod performance_tests {
    use super::*;
    use std::time::Instant;
    
    #[test]
    fn test_select_action_performance() {
        println!("🔬 select_action 函数性能测试");
        
        // 创建配置
        let config = test_utils::create_test_config();

        // 为了避免GPU适配器问题，强制使用CPU后端进行测试
        let device = burn::backend::ndarray::NdArrayDevice::default();
        let agent: SacAgent<Autodiff<NdArray>> = SacAgent::new(&config, &device)
            .expect("创建智能体失败");

        // 创建测试状态
        let test_state: Vec<f64> = (0..10).map(|i| (i as f64) * 0.1).collect();

        // 性能统计
        let mut stats = SelectActionPerformanceStats::default();

        // 测试参数 (降低iterations避免CI超时)
        let test_iterations = 50;
        let warm_up_iterations = 5;

        println!("🔥 预热阶段: {} 次调用", warm_up_iterations);
        for _ in 0..warm_up_iterations {
            let _ = agent.select_action(&test_state, true);
        }

        println!("📊 正式测试: {} 次调用", test_iterations);
        let benchmark_start = Instant::now();

        for i in 0..test_iterations {
            let training = i % 2 == 0;
            let result = agent.select_action_with_profiling(&test_state, training, &mut stats);
            assert!(result.is_ok(), "选择动作失败: {:?}", result.err());
        }

        let total_benchmark_time = benchmark_start.elapsed();

        println!("✅ 测试完成！");
        println!("总基准时间: {:.2}ms", total_benchmark_time.as_secs_f64() * 1000.0);

        // 打印性能报告
        stats.print_report();

        // 基本性能断言
        let avg_time_us = total_benchmark_time.as_nanos() as f64 / test_iterations as f64 / 1000.0;
        println!("平均每次调用: {:.2}μs", avg_time_us);
        
        // 确保性能在合理范围内 (每次调用 < 50ms，比较宽松的限制)
        assert!(avg_time_us < 50000.0, "select_action 性能过慢: {:.2}μs > 50000μs", avg_time_us);
        
        // 验证统计数据的合理性
        assert!(stats.call_count == test_iterations as u64, "调用次数统计错误");
        assert!(stats.total_time > std::time::Duration::ZERO, "总时间应该大于0");
    }
    
    #[test]
    fn benchmark_basic_operations() {
        println!("🧪 基础操作基准测试");
        
        let state: Vec<f64> = (0..100).map(|i| i as f64 * 0.01).collect();
        
        // 测试数据转换步骤
        let convert_start = Instant::now();
        for _ in 0..1000 {  // 减少迭代次数
            let _state_vec: Vec<f32> = state.iter().map(|&x| x as f32).collect();
        }
        let convert_time = convert_start.elapsed();
        
        println!("数据转换基准 (1000次): {:.2}ms", convert_time.as_secs_f64() * 1000.0);
        println!("单次转换平均: {:.2}μs", convert_time.as_nanos() as f64 / 1000.0 / 1000.0);
        
        // 基本断言
        assert!(convert_time.as_millis() < 100, "数据转换过慢");
    }
} 