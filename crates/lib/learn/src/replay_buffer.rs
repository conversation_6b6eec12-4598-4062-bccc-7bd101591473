//! # 经验回放缓冲区
//! 
//! 实现经验回放缓冲区，用于存储和采样训练数据。
//! 支持普通回放和优先级回放。

use crate::errors::{LearnError, LearnResult};
use rand::{prelude::*, Rng};
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;

/// 经验数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Experience {
    /// 当前状态
    pub state: Vec<f64>,
    /// 执行的动作
    pub action: Vec<f64>,
    /// 获得的奖励
    pub reward: f64,
    /// 下一状态
    pub next_state: Vec<f64>,
    /// 是否为终止状态
    pub done: bool,
}

impl Experience {
    /// 创建新的经验
    pub fn new(
        state: Vec<f64>,
        action: Vec<f64>,
        reward: f64,
        next_state: Vec<f64>,
        done: bool,
    ) -> Self {
        Self {
            state,
            action,
            reward,
            next_state,
            done,
        }
    }
}

/// 经验批次
#[derive(Debug, <PERSON><PERSON>)]
pub struct ExperienceBatch {
    /// 状态批次
    pub states: Vec<Vec<f64>>,
    /// 动作批次
    pub actions: Vec<Vec<f64>>,
    /// 奖励批次
    pub rewards: Vec<f64>,
    /// 下一状态批次
    pub next_states: Vec<Vec<f64>>,
    /// 终止标志批次
    pub dones: Vec<bool>,
    /// 采样权重（用于优先级回放）
    pub weights: Option<Vec<f64>>,
    /// 采样索引（用于优先级回放更新）
    pub indices: Option<Vec<usize>>,
}

impl ExperienceBatch {
    /// 获取批次大小
    pub fn batch_size(&self) -> usize {
        self.states.len()
    }

    /// 验证批次数据一致性
    pub fn validate(&self) -> LearnResult<()> {
        let batch_size = self.states.len();
        
        if self.actions.len() != batch_size {
            return Err(LearnError::dimension_mismatch(
                format!("actions length: {}", batch_size),
                format!("actual: {}", self.actions.len()),
            ));
        }
        
        if self.rewards.len() != batch_size {
            return Err(LearnError::dimension_mismatch(
                format!("rewards length: {}", batch_size),
                format!("actual: {}", self.rewards.len()),
            ));
        }
        
        if self.next_states.len() != batch_size {
            return Err(LearnError::dimension_mismatch(
                format!("next_states length: {}", batch_size),
                format!("actual: {}", self.next_states.len()),
            ));
        }
        
        if self.dones.len() != batch_size {
            return Err(LearnError::dimension_mismatch(
                format!("dones length: {}", batch_size),
                format!("actual: {}", self.dones.len()),
            ));
        }

        Ok(())
    }
}

/// 回放缓冲区特征
pub trait ReplayBufferTrait {
    /// 添加经验
    fn push(&mut self, experience: Experience);
    
    /// 采样批次数据
    fn sample(&mut self, batch_size: usize) -> LearnResult<ExperienceBatch>;
    
    /// 更新优先级（仅用于优先级回放）
    fn update_priorities(&mut self, indices: &[usize], priorities: &[f64]) -> LearnResult<()>;
    
    /// 获取缓冲区大小
    fn len(&self) -> usize;
    
    /// 检查缓冲区是否为空
    fn is_empty(&self) -> bool {
        self.len() == 0
    }
    
    /// 检查是否有足够的数据进行采样
    fn can_sample(&self, batch_size: usize) -> bool {
        self.len() >= batch_size
    }
}

/// 简单回放缓冲区
#[derive(Debug)]
pub struct SimpleReplayBuffer {
    /// 缓冲区数据
    buffer: VecDeque<Experience>,
    /// 最大容量
    capacity: usize,
    /// 随机数生成器
    rng: rand::rngs::ThreadRng,
}

impl SimpleReplayBuffer {
    /// 创建新的简单回放缓冲区
    pub fn new(capacity: usize) -> Self {
        Self {
            buffer: VecDeque::with_capacity(capacity),
            capacity,
            rng: rand::rng(),
        }
    }
}

impl ReplayBufferTrait for SimpleReplayBuffer {
    fn push(&mut self, experience: Experience) {
        if self.buffer.len() >= self.capacity {
            self.buffer.pop_front();
        }
        self.buffer.push_back(experience);
    }
    
    fn sample(&mut self, batch_size: usize) -> LearnResult<ExperienceBatch> {
        if !self.can_sample(batch_size) {
            return Err(LearnError::ReplayBuffer(format!(
                "缓冲区数据不足，需要 {} 条，实际 {} 条",
                batch_size,
                self.len()
            )));
        }
        
        let indices: Vec<usize> = (0..self.buffer.len())
            .collect::<Vec<_>>()
            .choose_multiple(&mut self.rng, batch_size)
            .cloned()
            .collect();
        
        let mut states = Vec::with_capacity(batch_size);
        let mut actions = Vec::with_capacity(batch_size);
        let mut rewards = Vec::with_capacity(batch_size);
        let mut next_states = Vec::with_capacity(batch_size);
        let mut dones = Vec::with_capacity(batch_size);
        
        for &idx in &indices {
            let exp = &self.buffer[idx];
            states.push(exp.state.clone());
            actions.push(exp.action.clone());
            rewards.push(exp.reward);
            next_states.push(exp.next_state.clone());
            dones.push(exp.done);
        }
        
        let batch = ExperienceBatch {
            states,
            actions,
            rewards,
            next_states,
            dones,
            weights: None,
            indices: None,
        };
        
        batch.validate()?;
        Ok(batch)
    }
    
    fn update_priorities(&mut self, _indices: &[usize], _priorities: &[f64]) -> LearnResult<()> {
        // 简单回放缓冲区不支持优先级更新
        Ok(())
    }
    
    fn len(&self) -> usize {
        self.buffer.len()
    }
}

/// 优先级回放缓冲区的节点
#[derive(Debug, Clone)]
struct PriorityNode {
    /// 经验数据
    experience: Experience,
    /// 优先级
    priority: f64,
}

/// 优先级回放缓冲区
#[derive(Debug)]
pub struct PrioritizedReplayBuffer {
    /// 缓冲区数据
    buffer: VecDeque<PriorityNode>,
    /// 最大容量
    capacity: usize,
    /// 优先级参数
    alpha: f64,
    /// 重要性采样参数
    beta: f64,
    /// 最大优先级
    max_priority: f64,
    /// 当前索引
    current_index: usize,
    /// 随机数生成器
    rng: rand::rngs::ThreadRng,
}

impl PrioritizedReplayBuffer {
    /// 创建新的优先级回放缓冲区
    pub fn new(capacity: usize, alpha: f64, beta: f64) -> Self {
        Self {
            buffer: VecDeque::with_capacity(capacity),
            capacity,
            alpha,
            beta,
            max_priority: 1.0,
            current_index: 0,
            rng: rand::rng(),
        }
    }
    
    /// 更新beta参数
    pub fn update_beta(&mut self, beta: f64) {
        self.beta = beta;
    }
    
    /// 计算采样概率
    fn compute_probabilities(&self) -> Vec<f64> {
        let priorities: Vec<f64> = self.buffer
            .iter()
            .map(|node| node.priority.powf(self.alpha))
            .collect();
        
        let sum: f64 = priorities.iter().sum();
        
        if sum == 0.0 {
            vec![1.0 / self.buffer.len() as f64; self.buffer.len()]
        } else {
            priorities.iter().map(|p| p / sum).collect()
        }
    }
    
    /// 计算重要性采样权重
    fn compute_weights(&self, indices: &[usize]) -> Vec<f64> {
        let probabilities = self.compute_probabilities();
        let min_prob = probabilities.iter().cloned().fold(f64::INFINITY, f64::min);
        let max_weight = (self.buffer.len() as f64 * min_prob).powf(-self.beta);
        
        indices
            .iter()
            .map(|&idx| {
                let prob = probabilities[idx];
                let weight = (self.buffer.len() as f64 * prob).powf(-self.beta);
                weight / max_weight
            })
            .collect()
    }
}

impl ReplayBufferTrait for PrioritizedReplayBuffer {
    fn push(&mut self, experience: Experience) {
        let priority = self.max_priority;
        let node = PriorityNode { experience, priority };
        
        if self.buffer.len() >= self.capacity {
            self.buffer.pop_front();
        }
        self.buffer.push_back(node);
        self.current_index = (self.current_index + 1) % self.capacity;
    }
    
    fn sample(&mut self, batch_size: usize) -> LearnResult<ExperienceBatch> {
        if !self.can_sample(batch_size) {
            return Err(LearnError::ReplayBuffer(format!(
                "缓冲区数据不足，需要 {} 条，实际 {} 条",
                batch_size,
                self.len()
            )));
        }
        
        let probabilities = self.compute_probabilities();
        let mut indices = Vec::with_capacity(batch_size);
        
        // 使用轮盘赌采样
        for _ in 0..batch_size {
            let rand_val: f64 = self.rng.random();
            let mut cumulative = 0.0;
            
            for (idx, &prob) in probabilities.iter().enumerate() {
                cumulative += prob;
                if rand_val <= cumulative {
                    indices.push(idx);
                    break;
                }
            }
        }
        
        // 如果由于数值精度问题没有采样到足够的数据，随机补充
        while indices.len() < batch_size {
            indices.push(self.rng.random_range(0..self.buffer.len()));
        }
        
        let weights = self.compute_weights(&indices);
        
        let mut states = Vec::with_capacity(batch_size);
        let mut actions = Vec::with_capacity(batch_size);
        let mut rewards = Vec::with_capacity(batch_size);
        let mut next_states = Vec::with_capacity(batch_size);
        let mut dones = Vec::with_capacity(batch_size);
        
        for &idx in &indices {
            let exp = &self.buffer[idx].experience;
            states.push(exp.state.clone());
            actions.push(exp.action.clone());
            rewards.push(exp.reward);
            next_states.push(exp.next_state.clone());
            dones.push(exp.done);
        }
        
        let batch = ExperienceBatch {
            states,
            actions,
            rewards,
            next_states,
            dones,
            weights: Some(weights),
            indices: Some(indices),
        };
        
        batch.validate()?;
        Ok(batch)
    }
    
    fn update_priorities(&mut self, indices: &[usize], priorities: &[f64]) -> LearnResult<()> {
        if indices.len() != priorities.len() {
            return Err(LearnError::dimension_mismatch(
                format!("indices length: {}", indices.len()),
                format!("priorities length: {}", priorities.len()),
            ));
        }
        
        for (&idx, &priority) in indices.iter().zip(priorities.iter()) {
            if idx < self.buffer.len() {
                let clamped_priority = priority.max(1e-8); // 避免零优先级
                self.buffer[idx].priority = clamped_priority;
                self.max_priority = self.max_priority.max(clamped_priority);
            }
        }
        
        Ok(())
    }
    
    fn len(&self) -> usize {
        self.buffer.len()
    }
}

/// 回放缓冲区工厂
pub struct ReplayBuffer;

impl ReplayBuffer {
    /// 创建简单回放缓冲区
    pub fn simple(capacity: usize) -> Box<dyn ReplayBufferTrait> {
        Box::new(SimpleReplayBuffer::new(capacity))
    }
    
    /// 创建优先级回放缓冲区
    pub fn prioritized(
        capacity: usize,
        alpha: f64,
        beta: f64,
    ) -> Box<dyn ReplayBufferTrait> {
        Box::new(PrioritizedReplayBuffer::new(capacity, alpha, beta))
    }
    
    /// 根据配置创建回放缓冲区
    pub fn from_config(config: &crate::config::ReplayConfig) -> Box<dyn ReplayBufferTrait> {
        if config.prioritized {
            if let Some(ref params) = config.priority_params {
                Self::prioritized(config.capacity, params.alpha, params.beta_start)
            } else {
                // 使用默认优先级参数
                Self::prioritized(config.capacity, 0.6, 0.4)
            }
        } else {
            Self::simple(config.capacity)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_experience(id: usize) -> Experience {
        Experience::new(
            vec![id as f64; 4],  // state
            vec![id as f64; 2],  // action
            id as f64,           // reward
            vec![(id + 1) as f64; 4], // next_state
            id % 5 == 4,         // done
        )
    }

    #[test]
    fn test_simple_replay_buffer() {
        let mut buffer = SimpleReplayBuffer::new(5);
        
        // 测试空缓冲区
        assert_eq!(buffer.len(), 0);
        assert!(buffer.is_empty());
        assert!(!buffer.can_sample(1));
        
        // 添加经验
        for i in 0..3 {
            buffer.push(create_test_experience(i));
        }
        
        assert_eq!(buffer.len(), 3);
        assert!(!buffer.is_empty());
        assert!(buffer.can_sample(2));
        assert!(!buffer.can_sample(5));
        
        // 测试采样
        let batch = buffer.sample(2).unwrap();
        assert_eq!(batch.batch_size(), 2);
        batch.validate().unwrap();
        
        // 测试容量限制
        for i in 3..8 {
            buffer.push(create_test_experience(i));
        }
        assert_eq!(buffer.len(), 5); // 不超过容量
    }

    #[test]
    fn test_prioritized_replay_buffer() {
        let mut buffer = PrioritizedReplayBuffer::new(5, 0.6, 0.4);
        
        // 添加经验
        for i in 0..3 {
            buffer.push(create_test_experience(i));
        }
        
        assert_eq!(buffer.len(), 3);
        
        // 测试采样
        let batch = buffer.sample(2).unwrap();
        assert_eq!(batch.batch_size(), 2);
        assert!(batch.weights.is_some());
        assert!(batch.indices.is_some());
        batch.validate().unwrap();
        
        // 测试优先级更新
        let indices = batch.indices.unwrap();
        let priorities = vec![1.5, 2.0];
        buffer.update_priorities(&indices, &priorities).unwrap();
    }

    #[test]
    fn test_experience_batch_validation() {
        let batch = ExperienceBatch {
            states: vec![vec![1.0, 2.0], vec![3.0, 4.0]],
            actions: vec![vec![0.5]], // 长度不匹配
            rewards: vec![1.0, 2.0],
            next_states: vec![vec![2.0, 3.0], vec![4.0, 5.0]],
            dones: vec![false, true],
            weights: None,
            indices: None,
        };
        
        assert!(batch.validate().is_err());
    }
} 