//! # 学习模块错误定义
//! 
//! 定义与深度强化学习训练过程相关的各种错误类型。

use thiserror::Error;

/// 学习模块错误类型
#[derive(Debug, Error)]
pub enum LearnError {
    /// 模型初始化错误
    #[error("模型初始化失败: {0}")]
    ModelInitialization(String),

    /// 模型加载错误
    #[error("模型加载失败: {0}")]
    ModelLoad(String),

    /// 模型保存错误
    #[error("模型保存失败: {0}")]
    ModelSave(String),

    /// 训练数据错误
    #[error("训练数据错误: {0}")]
    TrainingData(String),

    /// 经验回放缓冲区错误
    #[error("经验回放缓冲区错误: {0}")]
    ReplayBuffer(String),

    /// 神经网络前向传播错误
    #[error("神经网络前向传播错误: {0}")]
    Forward(String),

    /// 神经网络反向传播错误
    #[error("神经网络反向传播错误: {0}")]
    Backward(String),

    /// 优化器错误
    #[error("优化器错误: {0}")]
    Optimizer(String),

    /// 超参数配置错误
    #[error("超参数配置错误: {0}")]
    Configuration(String),

    /// 维度不匹配错误
    #[error("张量维度不匹配: 期望 {expected}，实际 {actual}")]
    DimensionMismatch { expected: String, actual: String },

    /// 动作空间错误
    #[error("动作空间错误: {0}")]
    ActionSpace(String),

    /// 状态空间错误
    #[error("状态空间错误: {0}")]
    StateSpace(String),

    /// 设备错误（GPU/CPU）
    #[error("设备错误: {0}")]
    Device(String),

    /// 数值计算错误
    #[error("数值计算错误: {0}")]
    Numerical(String),

    /// 采样错误
    #[error("采样错误: {0}")]
    Sampling(String),

    /// IO错误
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    /// JSON序列化错误
    #[error("JSON序列化错误: {0}")]
    Json(#[from] serde_json::Error),

    /// 其他错误
    #[error("其他错误: {0}")]
    Other(String),
}

/// 学习模块结果类型别名
pub type LearnResult<T> = Result<T, LearnError>;

impl LearnError {
    /// 创建模型初始化错误
    pub fn model_init<S: Into<String>>(msg: S) -> Self {
        Self::ModelInitialization(msg.into())
    }

    /// 创建维度不匹配错误
    pub fn dimension_mismatch<S: Into<String>>(expected: S, actual: S) -> Self {
        Self::DimensionMismatch {
            expected: expected.into(),
            actual: actual.into(),
        }
    }

    /// 创建配置错误
    pub fn config<S: Into<String>>(msg: S) -> Self {
        Self::Configuration(msg.into())
    }

    /// 创建数值计算错误
    pub fn numerical<S: Into<String>>(msg: S) -> Self {
        Self::Numerical(msg.into())
    }
} 