//! # Actor 网络模型
//!
//! 实现用于策略学习的 Actor 网络，支持离散动作输出。

use burn::{
    config::Config,
    module::Module,
    nn::{Linear, LinearConfig},
    prelude::*,
    tensor::activation::softmax,
};
use crate::{
    model::common::{activation_from_type, init_from_type, ActivationType, InitializationType, Model},
    errors::{LearnResult},
};

/// Actor 网络配置
#[derive(Config, Debug)]
pub struct ActorConfig {
    /// 状态维度
    pub state_dim: usize,
    /// 动作维度（离散动作数量）
    pub action_dim: usize,
    /// 隐藏层维度
    #[config(default = "vec![256, 256]")]
    pub hidden_dims: Vec<usize>,
    /// 激活函数类型
    #[config(default = "ActivationType::ReLU")]
    pub activation: ActivationType,
    /// 初始化类型
    #[config(default = "InitializationType::Xavier")]
    pub init_type: InitializationType,
    /// Log 标准差最小值（保留用于配置兼容性）
    #[config(default = "-20.0")]
    pub log_std_min: f64,
    /// Log 标准差最大值（保留用于配置兼容性）
    #[config(default = "2.0")]
    pub log_std_max: f64,
    /// 动作缩放因子（保留用于配置兼容性）
    #[config(default = "1.0")]
    pub action_scale: f64,
}

/// Actor 网络模型 - 连续动作版本（适用于量化交易）
#[derive(Module, Debug)]
pub struct Actor<B: Backend> {
    shared_layers: Vec<Linear<B>>,
    mean_layer: Linear<B>,     // 输出动作均值
    log_std_layer: Linear<B>,  // 输出动作对数标准差
}

impl<B: Backend> Actor<B> {
    /// 创建新的 Actor 模型
    pub fn new(config: &ActorConfig, device: &B::Device) -> Self {
        let mut layers = Vec::new();
        let mut input_dim = config.state_dim;

        // 创建共享隐藏层
        for &hidden_dim in &config.hidden_dims {
            let initializer = init_from_type(config.init_type);
            layers.push(
                LinearConfig::new(input_dim, hidden_dim)
                    .with_initializer(initializer)
                    .init(device),
            );
            input_dim = hidden_dim;
        }

        let last_hidden_dim = input_dim;

        // 均值输出层
        let mean_initializer = init_from_type(config.init_type);
        let mean_layer = LinearConfig::new(last_hidden_dim, config.action_dim)
            .with_initializer(mean_initializer)
            .init(device);

        // 对数标准差输出层
        let log_std_initializer = init_from_type(config.init_type);
        let log_std_layer = LinearConfig::new(last_hidden_dim, config.action_dim)
            .with_initializer(log_std_initializer)
            .init(device);

        Self {
            shared_layers: layers,
            mean_layer,
            log_std_layer,
        }
    }

    /// 前向传播，返回动作均值和对数标准差
    pub fn forward(&self, state: Tensor<B, 2>, activation: ActivationType) -> (Tensor<B, 2>, Tensor<B, 2>) {
        let mut x = state;

        // 通过共享层
        for layer in &self.shared_layers {
            x = layer.forward(x);
            x = activation_from_type(activation, x);
        }

        // 输出动作均值和对数标准差
        let mean = self.mean_layer.forward(x.clone());
        let log_std = self.log_std_layer.forward(x);

        (mean, log_std)
    }

    /// 采样动作（用于训练时的探索）
    pub fn sample_action(
        &self,
        state: Tensor<B, 2>,
        activation: ActivationType,
        log_std_min: f64,
        log_std_max: f64,
        action_scale: f64,
    ) -> (Tensor<B, 2>, Tensor<B, 2>) {
        let (mean, log_std) = self.forward(state, activation);

        // 限制对数标准差范围
        let log_std_clamped = log_std.clamp(log_std_min, log_std_max);
        let std = log_std_clamped.clone().exp();

        // 重参数化采样：action = mean + std * noise
        let noise = Tensor::random_like(&mean, burn::tensor::Distribution::Normal(0.0, 1.0));
        let raw_action = mean.clone() + std.clone() * noise;

        // 应用tanh激活并缩放到目标范围
        let action = raw_action.clone().tanh() * action_scale;

        // 计算对数概率
        let log_prob = self.compute_log_prob(&mean, &log_std_clamped, &raw_action);

        (action, log_prob)
    }

    /// 确定性动作（用于评估）
    pub fn deterministic_action(
        &self,
        state: Tensor<B, 2>,
        activation: ActivationType,
        action_scale: f64,
    ) -> Tensor<B, 2> {
        let (mean, _) = self.forward(state, activation);
        mean.tanh() * action_scale
    }

    /// 计算动作的对数概率
    fn compute_log_prob(
        &self,
        mean: &Tensor<B, 2>,
        log_std: &Tensor<B, 2>,
        raw_action: &Tensor<B, 2>,
    ) -> Tensor<B, 2> {
        let std = log_std.clone().exp();
        let var = std.clone() * std.clone();

        // 高斯分布的对数概率密度
        let diff = raw_action.clone() - mean.clone();
        let pi_term = Tensor::ones_like(&mean) * (2.0 * std::f64::consts::PI).ln();
        let log_prob_gaussian = (diff.powf_scalar(2.0) / var +
                                log_std.clone() * 2.0 +
                                pi_term) * (-0.5);

        // tanh变换的雅可比行列式修正
        let tanh_action = raw_action.clone().tanh();
        let ones = Tensor::ones_like(&tanh_action);
        let log_prob_correction = (ones - tanh_action.powf_scalar(2.0) + 1e-6).log();

        log_prob_gaussian + log_prob_correction
    }

    /// 获取动作概率分布（为兼容性保留）
    pub fn get_action_probabilities(&self, state: Tensor<B, 2>, activation: ActivationType) -> Tensor<B, 2> {
        let (mean, _) = self.forward(state, activation);
        // 对于连续动作，返回均值作为"概率"
        softmax(mean, 1)
    }

    /// 获取动作维度
    pub fn action_dim(&self) -> usize {
        1 // 连续动作维度为1
    }
}

impl<B: Backend> Model<B> for Actor<B> {
    type Record = <Self as Module<B>>::Record;

    fn state(&self) -> Self::Record {
        // 优化：避免不必要的克隆操作
        self.clone().into_record()
    }

    fn load(&mut self, _record: &Self::Record) -> LearnResult<()> {
        // TODO: 实现适当的记录加载
        Ok(())
    }

    fn soft_update(&mut self, _tau: f64) -> LearnResult<()> {
        // Actor 通常不需要软更新，这由外部管理
        Ok(())
    }
}