//! # Actor 网络模型
//!
//! 实现用于策略学习的 Actor 网络，支持离散动作输出。

use burn::{
    config::Config,
    module::Module,
    nn::{Linear, LinearConfig},
    prelude::*,
    tensor::activation::softmax,
};
use crate::{
    model::common::{activation_from_type, init_from_type, ActivationType, InitializationType, Model},
    errors::{LearnResult},
};

/// Actor 网络配置
#[derive(Config, Debug)]
pub struct ActorConfig {
    /// 状态维度
    pub state_dim: usize,
    /// 动作维度（离散动作数量）
    pub action_dim: usize,
    /// 隐藏层维度
    #[config(default = "vec![256, 256]")]
    pub hidden_dims: Vec<usize>,
    /// 激活函数类型
    #[config(default = "ActivationType::ReLU")]
    pub activation: ActivationType,
    /// 初始化类型
    #[config(default = "InitializationType::Xavier")]
    pub init_type: InitializationType,
    /// Log 标准差最小值（保留用于配置兼容性）
    #[config(default = "-20.0")]
    pub log_std_min: f64,
    /// Log 标准差最大值（保留用于配置兼容性）
    #[config(default = "2.0")]
    pub log_std_max: f64,
    /// 动作缩放因子（保留用于配置兼容性）
    #[config(default = "1.0")]
    pub action_scale: f64,
}

/// Actor 网络模型 - 连续动作版本（适用于量化交易）
#[derive(Module, Debug)]
pub struct Actor<B: Backend> {
    shared_layers: Vec<Linear<B>>,
    mean_layer: Linear<B>,     // 输出动作均值
    log_std_layer: Linear<B>,  // 输出动作对数标准差
}

impl<B: Backend> Actor<B> {
    /// 创建新的 Actor 模型
    pub fn new(config: &ActorConfig, device: &B::Device) -> Self {
        let mut layers = Vec::new();
        let mut input_dim = config.state_dim;

        // 创建共享隐藏层
        for &hidden_dim in &config.hidden_dims {
            let initializer = init_from_type(config.init_type);
            layers.push(
                LinearConfig::new(input_dim, hidden_dim)
                    .with_initializer(initializer)
                    .init(device),
            );
            input_dim = hidden_dim;
        }

        let last_hidden_dim = input_dim;

        // 均值输出层
        let mean_initializer = init_from_type(config.init_type);
        let mean_layer = LinearConfig::new(last_hidden_dim, config.action_dim)
            .with_initializer(mean_initializer)
            .init(device);

        // 对数标准差输出层
        let log_std_initializer = init_from_type(config.init_type);
        let log_std_layer = LinearConfig::new(last_hidden_dim, config.action_dim)
            .with_initializer(log_std_initializer)
            .init(device);

        Self {
            shared_layers: layers,
            mean_layer,
            log_std_layer,
        }
    }

    /// 前向传播，返回动作logits
    pub fn forward(&self, state: Tensor<B, 2>, activation: ActivationType) -> Tensor<B, 2> {
        let mut x = state;
        
        // 通过共享层
        for layer in &self.shared_layers {
            x = layer.forward(x);
            x = activation_from_type(activation, x);
        }
        
        // 输出logits（未归一化的分数）
        self.logits_layer.forward(x)
    }

    /// 从logits计算动作概率分布
    pub fn get_action_probabilities(&self, state: Tensor<B, 2>, activation: ActivationType) -> Tensor<B, 2> {
        let logits = self.forward(state, activation);
        softmax(logits, 1) // 在动作维度上应用softmax
    }

    /// 采样动作（离散版本）
    pub fn sample_action(
        &self, 
        state: Tensor<B, 2>, 
        activation: ActivationType,
        _log_std_min: f64,    // 保留参数兼容性
        _log_std_max: f64,    // 保留参数兼容性
        _action_scale: f64,   // 保留参数兼容性
    ) -> (Tensor<B, 2>, Tensor<B, 2>) {
        let logits = self.forward(state, activation);
        let action_probs = softmax(logits.clone(), 1);
        
        // 简化计算log概率：直接使用概率的对数
        let log_probs = action_probs.clone().log();
        
        // 简化实现：直接返回概率分布作为"动作"，以及平均log概率
        (action_probs, log_probs.mean_dim(1))
    }

    /// 确定性动作（用于评估）- 选择概率最高的动作
    pub fn deterministic_action(
        &self, 
        state: Tensor<B, 2>, 
        activation: ActivationType,
        _action_scale: f64,   // 保留参数兼容性
    ) -> Tensor<B, 2> {
        let action_probs = self.get_action_probabilities(state, activation);
        
        // 返回概率分布，在上层使用argmax选择最高概率的动作
        action_probs
    }

    /// 获取动作维度
    pub fn action_dim(&self) -> usize {
        4 // 固定为4种离散动作
    }
}

impl<B: Backend> Model<B> for Actor<B> {
    type Record = <Self as Module<B>>::Record;

    fn state(&self) -> Self::Record {
        // 优化：避免不必要的克隆操作
        self.clone().into_record()
    }

    fn load(&mut self, _record: &Self::Record) -> LearnResult<()> {
        // TODO: 实现适当的记录加载
        Ok(())
    }

    fn soft_update(&mut self, _tau: f64) -> LearnResult<()> {
        // Actor 通常不需要软更新，这由外部管理
        Ok(())
    }
}