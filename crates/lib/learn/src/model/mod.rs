//! # 模型模块
//!
//! 包含基于 Burn 框架的 Actor 和 Critic 网络实现。

pub mod actor;
pub mod critic;
pub mod common;
pub mod transformer_encoder;

// 重新导出核心类型
pub use actor::{Actor, ActorConfig};
pub use critic::{Critic, CriticConfig, QNetwork};
pub use common::{Model, ActivationType, InitializationType};
pub use transformer_encoder::{
    TransformerEncoder,
    TransformerEncoderConfig,
    PositionalEncoding,
};

/// 模型参数初始化工具
pub struct ModelInitializer;

impl ModelInitializer {
    /// Xavier/Glorot 初始化
    pub fn xavier_uniform(fan_in: usize, fan_out: usize) -> f64 {
        let limit = (6.0 / (fan_in + fan_out) as f64).sqrt();
        rand::random::<f64>() * 2.0 * limit - limit
    }

    /// He 初始化
    pub fn he_uniform(fan_in: usize) -> f64 {
        let limit = (6.0 / fan_in as f64).sqrt();
        rand::random::<f64>() * 2.0 * limit - limit
    }

    /// 正态分布初始化
    pub fn normal(mean: f64, std: f64) -> f64 {
        use rand_distr::{Distribution, Normal};
        let normal = Normal::new(mean, std).unwrap();
        normal.sample(&mut rand::rng())
    }

    /// 均匀分布初始化
    pub fn uniform(low: f64, high: f64) -> f64 {
        rand::random::<f64>() * (high - low) + low
    }
} 