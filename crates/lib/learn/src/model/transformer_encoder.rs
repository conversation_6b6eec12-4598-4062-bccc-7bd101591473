//! # Transformer状态编码器
//!
//! 基于Vaswani等人的原始Transformer论文实现的高性能状态编码器，
//! 用于深度强化学习交易系统中的序列状态编码。

use burn::{
    config::Config,
    module::Module,
    nn::{
        Dropout, DropoutConfig, 
        LayerNorm, LayerNormConfig,
        Linear, LinearConfig,
        transformer::{TransformerEncoder as BurnTransformerEncoder, TransformerEncoderConfig as BurnTransformerEncoderConfig, TransformerEncoderInput}
    },
    tensor::{backend::Backend, Tensor},
};
use std::marker::PhantomData;

use crate::model::common::ActivationType;

/// Transformer编码器配置
/// 
/// 此结构体定义了Transformer编码器的所有超参数，
/// 遵循标准Transformer架构的设计原则。
#[derive(Config, Debug)]
pub struct TransformerEncoderConfig {
    /// 输入状态特征的原始维度
    /// 
    /// 这是来自环境观测的原始特征数量，例如价格、成交量等指标的总数。
    pub feature_dim: usize,
    
    /// Transformer的核心维度（模型维度）
    /// 
    /// 所有层（包括嵌入层）的隐藏维度，必须能被nhead整除。
    /// 这是Transformer内部所有计算使用的统一维度。
    #[config(default = 256)]
    pub d_model: usize,
    
    /// 多头注意力机制中的"头"数
    /// 
    /// 注意力头的数量，d_model必须能被此值整除。
    /// 更多的头可以捕获不同类型的依赖关系。
    #[config(default = 8)]
    pub nhead: usize,
    
    /// 编码器层的堆叠数量
    /// 
    /// Transformer编码器层的数量，更深的网络可以学习更复杂的模式，
    /// 但也需要更多的计算资源和可能过拟合。
    #[config(default = 6)]
    pub num_encoder_layers: usize,
    
    /// 前馈网络（FFN）的隐藏层维度
    /// 
    /// 通常是d_model的2-4倍，提供非线性变换能力。
    /// 更大的维度可以增强表达能力但增加计算成本。
    #[config(default = 1024)]
    pub dim_feedforward: usize,
    
    /// Dropout的比率
    /// 
    /// 用于防止过拟合的正则化技术，在0.0-1.0之间。
    /// 较高的dropout可以提高泛化能力但可能影响训练效率。
    #[config(default = 0.1)]
    pub dropout: f64,
    
    /// 前馈网络中使用的激活函数
    /// 
    /// 常用的选择包括ReLU、GELU等，GELU在Transformer中表现通常更好。
    #[config(default = "ActivationType::GELU")]
    pub activation: ActivationType,
    
    /// 位置编码的最大序列长度
    /// 
    /// 预分配的位置编码长度，应该大于等于实际输入序列的最大长度。
    #[config(default = 1000)]
    pub max_seq_len: usize,
}

impl TransformerEncoderConfig {
    /// 创建一个新的配置实例，需要指定feature_dim
    pub fn with_feature_dim(feature_dim: usize) -> Self {
        Self {
            feature_dim,
            d_model: 256,
            nhead: 8,
            num_encoder_layers: 6,
            dim_feedforward: 1024,
            dropout: 0.1,
            activation: ActivationType::GELU,
            max_seq_len: 1000,
        }
    }
}

/// 正弦位置编码模块
/// 
/// 实现Vaswani等人提出的原始位置编码方案，使用不同频率的
/// sin和cos函数为序列中的每个位置生成唯一的编码。
#[derive(Module, Debug)]
pub struct PositionalEncoding<B: Backend> {
    /// 预计算的位置编码矩阵，形状为 [max_len, d_model]
    /// 
    /// 此矩阵在模块初始化时预计算，包含所有可能位置的编码，
    /// 在forward过程中根据实际序列长度进行切片使用。
    pe: Tensor<B, 2>,
    
    /// Dropout层，用于位置编码的正则化
    dropout: Dropout,
    
    /// 模型维度
    d_model: usize,
    
    /// Backend类型标记
    _phantom: PhantomData<B>,
}

impl<B: Backend> PositionalEncoding<B> {
    /// 创建新的位置编码模块
    /// 
    /// # 参数
    /// * `d_model` - 模型维度
    /// * `max_len` - 最大序列长度
    /// * `dropout_rate` - Dropout比率
    /// * `device` - 计算设备
    /// 
    /// # 返回
    /// 初始化的位置编码模块
    pub fn new(d_model: usize, max_len: usize, dropout_rate: f64, device: &B::Device) -> Self {
        // 创建位置编码矩阵
        let mut pe = Tensor::zeros([max_len, d_model], device);
        
        // 生成位置索引 [0, 1, 2, ..., max_len-1]
        let position = Tensor::arange(0..max_len as i64, device)
            .float()
            .unsqueeze_dim(1); // 形状变为 [max_len, 1]
        
        // 计算分母项：10000^(2i/d_model)
        let div_term = Tensor::arange_step(0..d_model as i64, 2, device)
            .float()
            .mul_scalar(-std::f64::consts::LN_10 * 4.0 / d_model as f64) // -ln(10000) * 2i / d_model
            .exp(); // 10000^(-2i/d_model)
        
        // 计算sin和cos位置编码
        // 偶数索引使用sin，奇数索引使用cos
        let sin_encoding = position.clone().matmul(div_term.clone().unsqueeze_dim(0)).sin();
        let cos_encoding = position.matmul(div_term.unsqueeze_dim(0)).cos();
        
        // 交替填充sin和cos值
        for i in 0..(d_model / 2) {
            let sin_slice = sin_encoding.clone().slice([0..max_len, i..i+1]);
            let cos_slice = cos_encoding.clone().slice([0..max_len, i..i+1]);
            
            // 偶数位置填充sin
            pe = pe.slice_assign([0..max_len, (2*i)..(2*i+1)], sin_slice);
            
            // 奇数位置填充cos（如果d_model是奇数，最后一个位置可能不存在）
            if 2*i + 1 < d_model {
                pe = pe.slice_assign([0..max_len, (2*i+1)..(2*i+2)], cos_slice);
            }
        }
        
        // 创建Dropout层
        let dropout = DropoutConfig::new(dropout_rate).init();
        
        Self {
            pe,
            dropout,
            d_model,
            _phantom: PhantomData,
        }
    }
    
    /// 前向传播
    /// 
    /// # 参数
    /// * `x` - 输入张量，形状为 [batch_size, seq_len, d_model]
    /// 
    /// # 返回
    /// 添加位置编码后的张量，形状保持不变
    pub fn forward(&self, x: Tensor<B, 3>) -> Tensor<B, 3> {
        let [batch_size, seq_len, d_model] = x.dims();
        
        // 确保输入维度与模型维度匹配
        assert_eq!(d_model, self.d_model, 
                   "输入张量的d_model维度({})与位置编码器的d_model({})不匹配", 
                   d_model, self.d_model);
        
        // 从预计算的位置编码中提取对应长度的编码
        let position_encoding = self.pe
            .clone()
            .slice([0..seq_len, 0..d_model]) // 提取 [seq_len, d_model]
            .unsqueeze_dim(0) // 扩展为 [1, seq_len, d_model]
            .repeat_dim(0, batch_size); // 重复到 [batch_size, seq_len, d_model]
        
        // 将位置编码加到输入上
        let x_with_pos = x.add(position_encoding);
        
        // 应用Dropout
        self.dropout.forward(x_with_pos)
    }
}

/// Transformer编码器模块
/// 
/// 这是核心的Transformer编码器实现，包含输入投影、位置编码、
/// 多层Transformer编码器和可选的最终层归一化。
/// 
/// 整个模块的数据流如下：
/// 输入 -> 线性投影 -> 位置编码 -> Transformer编码器 -> 聚合 -> 输出
#[derive(Module, Debug)]
pub struct TransformerEncoder<B: Backend> {
    /// 输入投影层：将原始特征维度投影到模型维度
    /// 
    /// 将 [batch_size, seq_len, feature_dim] 转换为 [batch_size, seq_len, d_model]
    input_projection: Linear<B>,
    
    /// 位置编码模块
    /// 
    /// 为输入序列添加位置信息，使Transformer能够理解序列顺序
    pos_encoder: PositionalEncoding<B>,
    
    /// 核心Transformer编码器
    /// 
    /// 使用Burn框架提供的标准TransformerEncoder实现
    transformer_encoder: BurnTransformerEncoder<B>,
    
    /// 最终层归一化（可选）
    /// 
    /// 在输出前进行层归一化，有助于训练稳定性
    layer_norm: LayerNorm<B>,
    
    /// 模型维度（用于输出）
    d_model: usize,
    
    /// 原始特征维度（用于验证）
    feature_dim: usize,
}

impl<B: Backend> TransformerEncoder<B> {
    /// 创建新的Transformer编码器
    /// 
    /// # 参数
    /// * `config` - 编码器配置
    /// * `device` - 计算设备
    /// 
    /// # 返回
    /// 初始化的Transformer编码器
    pub fn new(config: &TransformerEncoderConfig, device: &B::Device) -> Self {
        // 验证配置参数
        assert!(config.d_model % config.nhead == 0,
                "d_model({}) 必须能被 nhead({}) 整除",
                config.d_model, config.nhead);
        
        assert!(config.feature_dim > 0, "feature_dim 必须大于 0");
        assert!(config.d_model > 0, "d_model 必须大于 0");
        assert!(config.num_encoder_layers > 0, "num_encoder_layers 必须大于 0");
        
        // 创建输入投影层
        let input_projection = LinearConfig::new(config.feature_dim, config.d_model)
            .init(device);
        
        // 创建位置编码器
        let pos_encoder = PositionalEncoding::new(
            config.d_model,
            config.max_seq_len,
            config.dropout,
            device
        );
        
        // 创建Transformer编码器配置
        let transformer_config = BurnTransformerEncoderConfig::new(
            config.d_model,
            config.nhead,
            config.num_encoder_layers,
            config.dim_feedforward,
        )
        .with_dropout(config.dropout)
        .with_norm_first(false); // 使用post-norm（标准配置）
        
        // 创建Transformer编码器
        let transformer_encoder = transformer_config.init(device);
        
        // 创建层归一化
        let layer_norm = LayerNormConfig::new(config.d_model).init(device);
        
        Self {
            input_projection,
            pos_encoder,
            transformer_encoder,
            layer_norm,
            d_model: config.d_model,
            feature_dim: config.feature_dim,
        }
    }
    
    /// 前向传播
    /// 
    /// # 参数
    /// * `x` - 输入张量，形状为 [batch_size, seq_len, feature_dim]
    /// 
    /// # 返回
    /// 编码后的状态向量，形状为 [batch_size, d_model]
    /// 
    /// # 数据流说明
    /// 1. 输入投影：feature_dim -> d_model
    /// 2. 位置编码：添加序列位置信息
    /// 3. Transformer编码：多层自注意力和前馈网络
    /// 4. 序列聚合：将序列输出聚合为单一向量
    /// 5. 层归一化：最终输出标准化
    pub fn forward(&self, x: Tensor<B, 3>) -> Tensor<B, 2> {
        let [batch_size, _seq_len, feature_dim] = x.dims();
        
        // 验证输入维度
        assert_eq!(feature_dim, self.feature_dim,
                   "输入特征维度({})与配置的feature_dim({})不匹配",
                   feature_dim, self.feature_dim);
        
        // 1. 输入投影：[batch_size, seq_len, feature_dim] -> [batch_size, seq_len, d_model]
        let x_projected = self.input_projection.forward(x);
        
        // 2. 位置编码：添加位置信息
        let x_with_pos = self.pos_encoder.forward(x_projected);
        
        // 3. Transformer编码
        // 创建TransformerEncoderInput
        let transformer_input = TransformerEncoderInput::new(x_with_pos);
        let transformer_output = self.transformer_encoder.forward(transformer_input);
        
        // 4. 序列聚合：取第一个时间步的输出作为最终表示
        // 类似于BERT中的[CLS] token的处理方式
        let aggregated_output = transformer_output
            .slice([0..batch_size, 0..1, 0..self.d_model]) // 取第一个时间步
            .squeeze(1); // 移除seq_len维度：[batch_size, d_model]
        
        // 5. 最终层归一化
        let final_output = self.layer_norm.forward(aggregated_output);
        
        final_output
    }
    
    /// 获取输出维度
    /// 
    /// # 返回
    /// 编码器输出的维度（等于d_model）
    pub fn output_dim(&self) -> usize {
        self.d_model
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use burn::backend::ndarray::NdArrayDevice;
    use burn::backend::NdArray;
    
    type TestBackend = NdArray<f32>;
    
    #[test]
    fn test_transformer_encoder_config() {
        let config = TransformerEncoderConfig::with_feature_dim(20);
        
        // 测试默认值
        assert_eq!(config.d_model, 256);
        assert_eq!(config.nhead, 8);
        assert_eq!(config.num_encoder_layers, 6);
        assert_eq!(config.dim_feedforward, 1024);
        assert_eq!(config.dropout, 0.1);
        assert_eq!(config.max_seq_len, 1000);
        assert_eq!(config.feature_dim, 20);
    }
    
    #[test]
    fn test_positional_encoding() {
        let device = NdArrayDevice::default();
        let d_model = 16;
        let max_len = 100;
        let dropout_rate = 0.0; // 测试时关闭dropout
        
        let pos_encoder = PositionalEncoding::<TestBackend>::new(
            d_model, max_len, dropout_rate, &device
        );
        
        // 测试前向传播
        let batch_size = 2;
        let seq_len = 10;
        let input = Tensor::zeros([batch_size, seq_len, d_model], &device);
        
        let output = pos_encoder.forward(input.clone());
        
        // 验证输出形状
        assert_eq!(output.dims(), [batch_size, seq_len, d_model]);
        
        // 验证位置编码已被添加（输出不应该全为零）
        let output_sum = output.sum().into_scalar();
        assert_ne!(output_sum, 0.0);
    }
    
    #[test]
    fn test_transformer_encoder_creation() {
        let device = NdArrayDevice::default();
        let config = TransformerEncoderConfig {
            feature_dim: 20,
            d_model: 32, // 较小的值用于测试
            nhead: 4,
            num_encoder_layers: 2,
            dim_feedforward: 64,
            dropout: 0.1,
            activation: ActivationType::GELU,
            max_seq_len: 100,
        };
        
        let encoder = TransformerEncoder::<TestBackend>::new(&config, &device);
        
        // 验证输出维度
        assert_eq!(encoder.output_dim(), 32);
    }
    
    #[test]
    fn test_transformer_encoder_forward() {
        let device = NdArrayDevice::default();
        let config = TransformerEncoderConfig {
            feature_dim: 20,
            d_model: 32,
            nhead: 4,
            num_encoder_layers: 2,
            dim_feedforward: 64,
            dropout: 0.0, // 测试时关闭dropout
            activation: ActivationType::GELU,
            max_seq_len: 100,
        };
        
        let encoder = TransformerEncoder::<TestBackend>::new(&config, &device);
        
        // 创建测试输入
        let batch_size = 3;
        let seq_len = 15;
        let input = Tensor::random([batch_size, seq_len, config.feature_dim], 
                                  burn::tensor::Distribution::Normal(0.0, 1.0), &device);
        
        // 前向传播
        let output = encoder.forward(input);
        
        // 验证输出形状
        assert_eq!(output.dims(), [batch_size, config.d_model]);
    }
    
    #[test]
    #[should_panic(expected = "d_model(17) 必须能被 nhead(8) 整除")]
    fn test_invalid_config() {
        let device = NdArrayDevice::default();
        let config = TransformerEncoderConfig {
            feature_dim: 20,
            d_model: 17, // 不能被8整除
            nhead: 8,
            num_encoder_layers: 2,
            dim_feedforward: 64,
            dropout: 0.1,
            activation: ActivationType::GELU,
            max_seq_len: 100,
        };
        
        // 这应该panic
        TransformerEncoder::<TestBackend>::new(&config, &device);
    }
} 