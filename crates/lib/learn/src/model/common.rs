//! # 通用模型组件
//!
//! 包含在 Actor 和 Critic 之间共享的特征、辅助函数等。

use burn::{
    prelude::*,
    nn::Initializer,
    tensor::activation::{relu, tanh, sigmoid, gelu},
};
use serde::{Deserialize, Serialize};
use crate::errors::LearnResult;

/// 模型状态管理特征。
///
/// 提供保存、加载和软更新功能。
pub trait Model<B: Backend> {
    type Record;

    fn state(&self) -> Self::Record;
    fn load(&mut self, record: &Self::Record) -> LearnResult<()>;
    fn soft_update(&mut self, tau: f64) -> LearnResult<()>;
}

/// 支持的激活函数类型。
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, Default)]
pub enum ActivationType {
    #[default]
    ReLU,
    GELU,
    Sigmoid,
    <PERSON>h,
    LeakyReLU,
    ELU,
    <PERSON>wish,
    Mish,
}

/// 支持的权重初始化类型。
#[derive(Debug, <PERSON><PERSON>, Copy, Serialize, Deserialize, PartialEq, Eq, Default)]
pub enum InitializationType {
    #[default]
    Xavier,
    Kaiming,
    Normal,
    Uniform,
}

/// 根据类型获取激活函数。
pub fn activation_from_type<B: Backend, const D: usize>(
    activation_type: ActivationType,
    tensor: Tensor<B, D>,
) -> Tensor<B, D> {
    match activation_type {
        ActivationType::ReLU => relu(tensor),
        ActivationType::GELU => gelu(tensor),
        ActivationType::Sigmoid => sigmoid(tensor),
        ActivationType::Tanh => tanh(tensor),
        ActivationType::LeakyReLU => {
            // 手动实现 LeakyReLU
            let zero = Tensor::zeros_like(&tensor);
            let positive = tensor.clone().max_pair(zero.clone());
            let negative = tensor.min_pair(zero) * 0.01;
            positive + negative
        },
        ActivationType::ELU => {
            // 手动实现 ELU
            let zero = Tensor::zeros_like(&tensor);
            let positive = tensor.clone().max_pair(zero.clone());
            let negative = (tensor.min_pair(zero).exp() - 1.0) * 1.0;
            positive + negative
        },
        ActivationType::Swish => {
            let sigmoid_val = sigmoid(tensor.clone());
            tensor * sigmoid_val
        },
        ActivationType::Mish => {
            // 简化的 Mish 实现：x * tanh(softplus(x))
            let softplus = (tensor.clone().exp() + 1.0).log();
            let tanh_val = tanh(softplus);
            tensor * tanh_val
        },
    }
}

/// 根据类型获取初始化器。
pub fn init_from_type(_init_type: InitializationType) -> Initializer {
    // 使用 Xavier Uniform 初始化器
    Initializer::XavierUniform { gain: 1.0 }
}

/// 辅助函数：创建零张量
pub fn zeros<B: Backend, const D: usize>(shape: [usize; D], device: &B::Device) -> Tensor<B, D> {
    Tensor::zeros(shape, device)
}

/// 辅助函数：张量最小值
pub fn tensor_min<B: Backend, const D: usize>(
    lhs: Tensor<B, D>,
    rhs: Tensor<B, D>,
) -> Tensor<B, D> {
    lhs.min_pair(rhs)
} 