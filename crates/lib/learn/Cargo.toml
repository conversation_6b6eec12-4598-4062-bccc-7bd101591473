[package]
name = "drl-trading-learn"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
homepage.workspace = true
repository.workspace = true
publish.workspace = true

[dependencies]
# 内部依赖
drl-trading-core = { workspace = true }

# 机器学习和深度学习
burn = { workspace = true }
# 数值计算
rust_decimal = { workspace = true }
rand = { workspace = true }
rand_distr = { workspace = true }

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 异步和并发
tokio = { workspace = true }
async-trait = { workspace = true }

# 错误处理
thiserror = { workspace = true }
anyhow = { workspace = true }

# 日志
log = { workspace = true }

# 数据结构
dashmap = { workspace = true }

# 工具
once_cell = { workspace = true }
toml = { workspace = true }

[features]
default = ["gpu"]
gpu = []
cpu-only = []

[dev-dependencies]
tempfile = { workspace = true }
tokio-test = "0.4"
hex = "0.4"