[package]
name = "drl-trading"
version.workspace = true
edition.workspace = true

[[bin]]
name = "drl-trading"
path = "src/main.rs"



[dependencies]
# 外部依赖
clap.workspace = true
tokio.workspace = true
serde.workspace = true
serde_json.workspace = true
toml.workspace = true
log.workspace = true
env_logger.workspace = true
anyhow.workspace = true
rust_decimal.workspace = true
rand.workspace = true
chrono.workspace = true
async-trait.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
console.workspace = true
burn.workspace = true

# 配置和环境
dotenvy.workspace = true

# 内部依赖
drl-trading-core.workspace = true
drl-trading-data.workspace = true
drl-trading-account.workspace = true
drl-trading-env.workspace = true
drl-trading-learn.workspace = true 