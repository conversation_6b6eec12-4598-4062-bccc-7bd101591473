//! # 回测模式
//! 
//! 使用历史数据进行策略回测

use anyhow::{Context, Result};
use drl_trading_core::env::EnvConfig;
use drl_trading_core::reward::RewardCalculationConfig;
use crate::config::AppConfig;
use drl_trading_env::{TradingEnvironment, Env, StateEncoder, ActionType, StatisticalSequenceEncoder};

// 导入各模块组件
use crate::modes::common::{create_account, create_agent, create_data_provider};

/// 回测结果
#[derive(Debug, Clone)]
pub struct BacktestResult {
    /// 总收益率
    pub total_return: f64,
    /// 夏普比率
    pub sharpe_ratio: f64,
    /// 最大回撤
    pub max_drawdown: f64,
    /// 交易次数
    pub total_trades: u64,
    /// 胜率
    pub win_rate: f64,
    /// 平均盈利
    pub avg_profit: f64,
    /// 平均亏损
    pub avg_loss: f64,
    /// 运行时间（秒）
    pub duration_seconds: f64,
}

/// 运行回测模式
pub async fn run_backtest(config: AppConfig) -> Result<()> {
    log::info!("开始回测模式");
    
    // 初始化组件
    let data_provider = create_data_provider(&config)
        .context("创建数据提供者失败")?;
    
    let account = create_account(&config)
        .context("创建账户失败")?;
    
    let _agent = create_agent(&config)
        .context("创建智能体失败")?;
    
    // 创建状态编码器（根据配置选择）
    let state_encoder: Box<dyn StateEncoder> = if let Some(encoder_config) = &config.learn.network.encoder {
        match encoder_config.encoder_type {
            drl_trading_learn::config::EncoderType::Transformer => {
                // 从配置文件读取窗口大小
                let window_size = if let Ok(params) = serde_json::from_value::<serde_json::Map<String, serde_json::Value>>(encoder_config.params.clone()) {
                    params.get("window_size")
                        .and_then(|v| v.as_u64())
                        .unwrap_or(50) as usize
                } else {
                    50 // 默认值
                };
                
                log::info!("使用StatisticalSequenceEncoder状态编码器，窗口大小: {}, 输出维度: {}", window_size, encoder_config.output_dim);
                Box::new(StatisticalSequenceEncoder::new(window_size, Some(encoder_config.output_dim)).map_err(|e| {
                    log::error!("创建统计序列状态编码器失败: {}", e);
                    std::io::Error::new(std::io::ErrorKind::Other, format!("创建统计序列状态编码器失败: {}", e))
                })?)
            }
            _ => {
                log::info!("配置项未指定Transformer类型，使用默认StatisticalSequenceEncoder，窗口大小: 50, 输出维度: {}", encoder_config.output_dim);
                Box::new(StatisticalSequenceEncoder::new(50, Some(encoder_config.output_dim)).map_err(|e| {
                    log::error!("创建StatisticalSequenceEncoder状态编码器失败: {}", e);
                    std::io::Error::new(std::io::ErrorKind::Other, format!("创建StatisticalSequenceEncoder状态编码器失败: {}", e))
                })?)
            }
        }
    } else {
        log::info!("未配置编码器，使用默认StatisticalSequenceEncoder状态编码器，窗口大小: 50, 输出维度: 256");
        Box::new(StatisticalSequenceEncoder::new(50, Some(256)).map_err(|e| {
            log::error!("创建StatisticalSequenceEncoder状态编码器失败: {}", e);
            std::io::Error::new(std::io::ErrorKind::Other, format!("创建StatisticalSequenceEncoder状态编码器失败: {}", e))
        })?)
    };
    
    // 创建环境配置
    let env_config = EnvConfig {
        initial_balance: config.account.initial_balance,
        max_steps: Some(config.learn.training.episodes * config.learn.training.steps_per_episode),
        record_history: true,
        history_capacity: 1000,
        seed: Some(42),
        max_drawdown_percent: 0.2, // 使用固定值，20%最大回撤
    };
    
    // 创建高级奖励配置（从配置文件读取）
    let reward_config = if let Some(reward_config) = &config.environment.reward {
        reward_config.to_reward_calculation_config()
    } else {
        // 使用默认配置
        log::warn!("未找到奖励配置，使用默认配置");
        RewardCalculationConfig::default()
    };

    // 创建带有高级奖励系统的交易环境
    let mut environment = TradingEnvironment::new(
        data_provider,
        account,
        state_encoder,
        env_config,
        reward_config,
    )?;

    log::info!("✅ 回测已启用高级奖励系统 - 与训练环境保持一致的奖励计算");
    
    // 运行回测（同步版本）
    let result = run_backtest_loop(&mut environment, &config)
        .context("回测运行失败")?;
    
    // 保存结果
    save_backtest_result(&result, &config)
        .context("保存回测结果失败")?;
    
    // 生成报告
    generate_backtest_report(&result, &config)
        .context("生成回测报告失败")?;
    
    log::info!("回测完成");
    log::info!("总收益率: {:.2}%", result.total_return * 100.0);
    log::info!("夏普比率: {:.2}", result.sharpe_ratio);
    log::info!("最大回撤: {:.2}%", result.max_drawdown * 100.0);
    log::info!("交易次数: {}", result.total_trades);
    log::info!("胜率: {:.2}%", result.win_rate * 100.0);
    
    Ok(())
}

/// 运行回测主循环（同步版本）
fn run_backtest_loop(
    environment: &mut TradingEnvironment,
    config: &AppConfig,
) -> Result<BacktestResult> {
    log::info!("开始回测主循环");
    
    let start_time = std::time::Instant::now();
    let mut step_count = 0u64;
    let mut total_trades = 0u64;
    let mut total_profit = 0.0;
    let mut total_loss = 0.0;
    let mut winning_trades = 0u64;
    let mut losing_trades = 0u64;
    
    let mut portfolio_values = Vec::new();
    let initial_value = 100000.0; // 假设初始价值
    let mut current_value = initial_value;
    let mut peak_value = initial_value;
    let mut max_drawdown = 0.0;
    
    // 重置环境（同步版本）
    let mut _current_state = environment.reset()?;
    portfolio_values.push(current_value);
    
    loop {
        step_count += 1;
        
        // 智能体选择动作
        // 环境不负责选择动作，应该由外部的Agent负责
        // 这里暂时使用持有动作
        let action = ActionType::Hold;
        
        // 执行一步（同步版本）- 修正返回值处理
        let (_next_state, reward, done, _info) = environment.step(action)?;
        
        // 更新统计
        if reward != 0.0 {
            total_trades += 1;
            if reward > 0.0 {
                total_profit += reward;
                winning_trades += 1;
            } else {
                total_loss += reward;
                losing_trades += 1;
            }
        }
        
        // 更新组合价值（简化计算）
        current_value += reward;
        portfolio_values.push(current_value);
        
        // 更新最大回撤
        if current_value > peak_value {
            peak_value = current_value;
        }
        let current_drawdown = (peak_value - current_value) / peak_value;
        if current_drawdown > max_drawdown {
            max_drawdown = current_drawdown;
        }
        
        // 检查终止条件
        if done {
            log::info!("回测结束：环境终止条件触发");
            break;
        }
        
        // 定期输出进度
        if step_count % config.app.metrics.update_frequency == 0 {
            log::info!("步数: {}, 当前价值: {:.2}, 累计收益: {:.2}%", 
                step_count, current_value, (current_value - initial_value) / initial_value * 100.0);
        }
    }
    
    let duration = start_time.elapsed().as_secs_f64();
    
    // 计算最终指标
    let total_return = (current_value - initial_value) / initial_value;
    let win_rate = if total_trades > 0 {
        winning_trades as f64 / total_trades as f64
    } else {
        0.0
    };
    let avg_profit = if winning_trades > 0 {
        total_profit / winning_trades as f64
    } else {
        0.0
    };
    let avg_loss = if losing_trades > 0 {
        total_loss / losing_trades as f64
    } else {
        0.0
    };
    
    // 计算夏普比率（简化计算）
    let returns: Vec<f64> = portfolio_values.windows(2)
        .map(|w| (w[1] - w[0]) / w[0])
        .collect();
    
    let mean_return = returns.iter().sum::<f64>() / returns.len() as f64;
    let variance = returns.iter()
        .map(|&r| (r - mean_return).powi(2))
        .sum::<f64>() / returns.len() as f64;
    let std_dev = variance.sqrt();
    let sharpe_ratio = if std_dev > 0.0 {
        mean_return / std_dev * (252.0_f64).sqrt() // 年化
    } else {
        0.0
    };
    
    let result = BacktestResult {
        total_return,
        sharpe_ratio,
        max_drawdown,
        total_trades,
        win_rate,
        avg_profit,
        avg_loss,
        duration_seconds: duration,
    };
    
    log::info!("回测主循环完成，总步数: {}", step_count);
    Ok(result)
}

/// 保存回测结果
fn save_backtest_result(result: &BacktestResult, config: &AppConfig) -> Result<()> {
    let output_path = config.get_output_path("backtest_result.json");
    
    let result_json = serde_json::to_string_pretty(result)
        .context("序列化回测结果失败")?;
    
    std::fs::write(&output_path, result_json)
        .with_context(|| format!("写入回测结果文件失败: {:?}", output_path))?;
    
    log::info!("回测结果已保存到: {:?}", output_path);
    Ok(())
}

/// 生成回测报告
fn generate_backtest_report(result: &BacktestResult, config: &AppConfig) -> Result<()> {
    let report_path = config.get_output_path("backtest_report.md");
    
    let report = format!(
        r#"# 回测报告

## 基本信息
- 配置文件: {}
- 运行时间: {:.2} 秒
- 生成时间: {}

## 收益指标
- 总收益率: {:.2}%
- 夏普比率: {:.2}
- 最大回撤: {:.2}%

## 交易指标
- 总交易次数: {}
- 胜率: {:.2}%
- 平均盈利: {:.4}
- 平均亏损: {:.4}
- 盈亏比: {:.2}

## 详细说明
本报告基于历史数据回测生成，用于评估交易策略的历史表现。
请注意，历史表现不代表未来收益，实际交易中可能面临更多风险。
"#,
        config.app.mode,
        result.duration_seconds,
        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
        result.total_return * 100.0,
        result.sharpe_ratio,
        result.max_drawdown * 100.0,
        result.total_trades,
        result.win_rate * 100.0,
        result.avg_profit,
        result.avg_loss,
        if result.avg_loss != 0.0 { -result.avg_profit / result.avg_loss } else { 0.0 },
    );
    
    std::fs::write(&report_path, report)
        .with_context(|| format!("写入回测报告失败: {:?}", report_path))?;
    
    log::info!("回测报告已生成: {:?}", report_path);
    Ok(())
}

impl serde::Serialize for BacktestResult {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        use serde::ser::SerializeStruct;
        let mut state = serializer.serialize_struct("BacktestResult", 8)?;
        state.serialize_field("total_return", &self.total_return)?;
        state.serialize_field("sharpe_ratio", &self.sharpe_ratio)?;
        state.serialize_field("max_drawdown", &self.max_drawdown)?;
        state.serialize_field("total_trades", &self.total_trades)?;
        state.serialize_field("win_rate", &self.win_rate)?;
        state.serialize_field("avg_profit", &self.avg_profit)?;
        state.serialize_field("avg_loss", &self.avg_loss)?;
        state.serialize_field("duration_seconds", &self.duration_seconds)?;
        state.end()
    }
} 