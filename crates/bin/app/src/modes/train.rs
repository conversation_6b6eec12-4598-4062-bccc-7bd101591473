//! # 训练模式
//!
//! 训练深度强化学习智能体

use anyhow::{Context, Result};
use log::{debug, info, warn};
use std::fs;
use std::time::Instant;
use drl_trading_core::env::EnvConfig;
use drl_trading_core::reward::RewardCalculationConfig;
use drl_trading_core::train::TrainingResult;
// 内部依赖
use crate::config::AppConfig;
use crate::modes::common::{create_account, create_data_provider};

// 外部依赖
use drl_trading_env::{TradingEnvironment, Env, StatisticalSequenceEncoder, StateEncoder, ActionType};
use drl_trading_learn::{SacAgentConfig, SacTrainer, trainer::Experience};

/// 奖励归一化器
#[derive(Debug, Clone)]
struct RewardNormalizer {
    /// 奖励历史（用于计算统计量）
    reward_history: std::collections::VecDeque<f64>,
    /// 历史窗口大小
    window_size: usize,
    /// 当前均值
    mean: f64,
    /// 当前标准差
    std: f64,
    /// 最小标准差（避免除零）
    min_std: f64,
}

impl RewardNormalizer {
    fn new(window_size: usize) -> Self {
        Self {
            reward_history: std::collections::VecDeque::with_capacity(window_size),
            window_size,
            mean: 0.0,
            std: 1.0,
            min_std: 1e-8,
        }
    }
    
    /// 更新统计量并归一化奖励
    fn normalize(&mut self, reward: f64) -> f64 {
        // 添加新奖励到历史
        self.reward_history.push_back(reward);
        if self.reward_history.len() > self.window_size {
            self.reward_history.pop_front();
        }
        
        // 计算当前统计量
        if self.reward_history.len() >= 2 {
            let sum: f64 = self.reward_history.iter().sum();
            self.mean = sum / self.reward_history.len() as f64;
            
            let variance: f64 = self.reward_history
                .iter()
                .map(|r| (r - self.mean).powi(2))
                .sum::<f64>() / (self.reward_history.len() - 1) as f64;
            
            self.std = variance.sqrt().max(self.min_std);
        }
        
        // 归一化奖励
        (reward - self.mean) / self.std
    }
    
    /// 重置归一化器（新回合开始时）
    fn reset(&mut self) {
        self.reward_history.clear();
        self.mean = 0.0;
        self.std = 1.0;
    }
}

/// 执行训练模式
pub fn run_train(config: AppConfig) -> Result<()> {
    info!("开始训练模式");

    // 创建环境
    let mut environment = create_training_environment(&config)?;

    // 创建训练器（基于环境自动计算维度）
    let mut trainer = create_trainer(&config, &mut environment)?;

    // 运行训练
    let result = run_training_loop(&mut environment, &mut trainer, &config)?;

    // 保存训练结果
    save_training_result(&result, &config).context("保存训练结果失败")?;

    // 输出训练摘要
    print_training_summary(&result);

    Ok(())
}

fn print_training_summary(result: &TrainingResult) {
    info!("=== 训练完成摘要 ===");
    info!("总训练步数: {}", result.total_steps);
    info!("总训练回合数: {}", result.total_episodes);
    info!("平均奖励: {:.4}", result.average_reward);
    info!("最佳回合奖励: {:.4}", result.best_episode_reward);
    info!("训练时长: {:.2}秒", result.training_duration);
    info!("最终Critic损失: {:.6}", result.final_critic_loss);
    info!("最终Actor损失: {:.6}", result.final_actor_loss);
}

/// 创建训练环境（基于backtest模式的实现）
fn create_training_environment(config: &AppConfig) -> Result<TradingEnvironment> {
    info!("创建训练环境");

    // 创建数据提供器
    let data_provider = create_data_provider(config).context("创建数据提供器失败")?;

    // 创建账户（已移除Arc和Mutex包装）
    let account = create_account(config).context("创建账户失败")?;

    // 创建状态编码器（统一使用StatisticalSequenceEncoder）
    let state_encoder: Box<dyn StateEncoder> = if let Some(encoder_config) = &config.learn.network.encoder {
        // 从配置文件读取窗口大小
        let window_size = if let Ok(params) = serde_json::from_value::<serde_json::Map<String, serde_json::Value>>(encoder_config.params.clone()) {
            params.get("window_size")
                .and_then(|v| v.as_u64())
                .unwrap_or(50) as usize
        } else {
            50 // 默认值
        };
        
        info!("使用StatisticalSequenceEncoder状态编码器，窗口大小: {}, 输出维度: {}", window_size, encoder_config.output_dim);
        Box::new(StatisticalSequenceEncoder::new(window_size, Some(encoder_config.output_dim)).map_err(|e| {
            log::error!("创建StatisticalSequenceEncoder状态编码器失败: {}", e);
            std::io::Error::new(std::io::ErrorKind::Other, format!("创建StatisticalSequenceEncoder状态编码器失败: {}", e))
        })?)
    } else {
        info!("未配置编码器，使用默认StatisticalSequenceEncoder状态编码器，窗口大小: 50, 输出维度: 256");
        Box::new(StatisticalSequenceEncoder::new(50, Some(256)).map_err(|e| {
            log::error!("创建StatisticalSequenceEncoder状态编码器失败: {}", e);
            std::io::Error::new(std::io::ErrorKind::Other, format!("创建StatisticalSequenceEncoder状态编码器失败: {}", e))
        })?)
    };

    // 创建环境配置
    let env_config = EnvConfig {
        initial_balance: config.account.initial_balance.clone(),
        max_steps: Some(config.learn.training.episodes * config.learn.training.steps_per_episode),
        record_history: true,
        history_capacity: 1000,
        seed: Some(42),
        max_drawdown_percent: 0.2, // 使用固定值，20%最大回撤
    };

    // 创建高级奖励配置（从配置文件读取）
    let reward_config = if let Some(reward_config) = &config.environment.reward {
        reward_config.to_reward_calculation_config()
    } else {
        // 使用默认配置
        warn!("未找到奖励配置，使用默认配置");
        RewardCalculationConfig::default()
    };

    // 创建带有高级奖励系统的交易环境
    let environment = TradingEnvironment::new(
        data_provider, 
        account,
        state_encoder, 
        env_config,
        reward_config
    )?;
    info!("训练环境创建完成");
    Ok(environment)
}

/// 创建训练器（基于环境自动计算维度）
fn create_trainer(config: &AppConfig, environment: &mut TradingEnvironment) -> Result<SacTrainer<drl_trading_learn::DefaultBackend>> {
    info!("创建SAC训练器");
    // 从环境安全地获取维度信息，不改变环境状态
    let dim_info = environment.get_dim_info();
    let state_dim = dim_info.state_dim;
    let action_dim = dim_info.action_dim;

    info!("自动检测维度: state_dim={}, action_dim={}", state_dim, action_dim);
    
    // 创建修正后的LearnConfig
    let mut learn_config = config.learn.clone();
    learn_config.common.state_dim = state_dim;
    learn_config.common.action_dim = action_dim;

    // 将修正后的LearnConfig转换为SacAgentConfig
    let sac_config = SacAgentConfig::from_learn_config(&learn_config);

    // 使用最佳设备创建训练器
    let device = drl_trading_learn::create_best_device();
    let trainer = SacTrainer::new(&sac_config, &device, config.learn.replay.capacity, config.learn.training.grad_clip)?;

    info!("SAC训练器创建完成");
    Ok(trainer)
}

/// 主训练循环
pub fn run_training_loop(
    environment: &mut TradingEnvironment, 
    trainer: &mut SacTrainer<drl_trading_learn::DefaultBackend>, 
    config: &AppConfig
) -> Result<TrainingResult> {
    info!("开始训练循环");

    let episodes = config.learn.training.episodes;
    let batch_size = config.learn.training.batch_size;
    let steps_per_episode = config.learn.training.steps_per_episode;
    let warmup_steps = config.learn.training.warmup_steps;

    // 训练统计
    let mut episode_rewards = Vec::new();
    let mut episode_lengths = Vec::new();
    let mut total_steps = 0;
    let mut best_reward = f64::NEG_INFINITY;
    let mut total_actor_loss = 0.0;
    let mut total_critic_loss = 0.0;

    let training_start = Instant::now();

    // 奖励归一化器
    let mut reward_normalizer = RewardNormalizer::new(100);

    // 开始训练
    for episode in 0..episodes {
        // 重置环境
        let mut current_observation = environment.reset()?;
        
        // 在第一个episode验证维度一致性
        if episode == 0 {
            let test_vector = current_observation.to_vector();
            let expected_state_dim = environment.get_dim_info().state_dim;
            info!("✅ 维度验证: 观测空间定义={}维, 实际向量={}维, 是否编码={}", 
                  expected_state_dim, test_vector.len(), current_observation.is_encoded());
            
            if expected_state_dim != test_vector.len() {
                return Err(anyhow::anyhow!(
                    "❌ 维度不匹配: 观测空间定义{}维，但实际向量{}维", 
                    expected_state_dim, test_vector.len()
                ));
            }
            info!("✅ 维度验证通过！");
        }
        let mut episode_reward = 0.0;
        let mut episode_length = 0;
        
        // 每个回合重置奖励归一化器
        reward_normalizer.reset();

        // 单个回合训练
        // 在循环外获取观测空间的克隆，避免借用冲突
        let observation_space = environment.state_encoder().observation_space().clone();
        
        for _step in 0..steps_per_episode {
            total_steps += 1;
            episode_length += 1;

            // 对当前观测进行安全的标准化处理
            let normalized_current_observation = current_observation.create_normalized_copy(&observation_space);
            let current_state_vector = normalized_current_observation.to_vector();
            
            // 选择动作 - 连续动作版本
            let action = if total_steps < warmup_steps {
                // 预热阶段：随机连续动作
                generate_random_continuous_action()
            } else {
                // 正常训练：使用策略选择连续动作
                select_continuous_action(trainer, &current_state_vector)?
            };

            // 环境步骤
            let (next_observation, raw_reward, done, _info) = environment.step(action.clone())?;

            // 归一化奖励
            let normalized_reward = reward_normalizer.normalize(raw_reward);

            // 存储经验（对下一个观测也进行安全的标准化）
            let normalized_next_observation = next_observation.create_normalized_copy(&observation_space);
            let next_state_vector = normalized_next_observation.to_vector();
            let action_vector = action_to_vector(&action);

            let experience = Experience {
                state: current_state_vector.into_iter().map(|x| x as f32).collect(),
                action: action_vector.into_iter().map(|x| x as f32).collect(),
                reward: normalized_reward as f32, // 使用归一化奖励
                next_state: next_state_vector.into_iter().map(|x| x as f32).collect(),
            };
            trainer.add_experience(&experience);
            // 累积原始奖励用于统计
            episode_reward += raw_reward;
            // 训练智能体（如果有足够的经验且不在预热阶段）
            if total_steps >= warmup_steps 
                && total_steps % config.learn.training.train_frequency == 0 
            {
                let training_metrics = trainer.train_step(&SacAgentConfig::from_learn_config(&config.learn), batch_size)?;
                total_actor_loss += training_metrics.actor_loss as f64;
                total_critic_loss += training_metrics.critic_loss as f64;
            }
            
            // 更新状态
            current_observation = next_observation;
            
            // 检查环境是否结束（风控终止等）
            if done {
                warn!("环境指示回合结束，提前终止当前回合");
                break;
            }
        }

        episode_rewards.push(episode_reward);
        episode_lengths.push(episode_length);

        if episode_reward > best_reward {
            best_reward = episode_reward;
        }

        // 输出训练进度
        if episode % 1 == 0 {
            let avg_reward = episode_rewards.iter().sum::<f64>() / episode_rewards.len() as f64;

            let avg_actor_loss = total_actor_loss / total_steps as f64;
            let avg_critic_loss = total_critic_loss / total_steps as f64;
            
            
            info!(
                "Episode {}/{}: 奖励={:.4}, 平均奖励={:.4}, avg_actor_loss={:0.4}, avg_critic_loss={:.4}",
                episode + 1,
                episodes,
                episode_reward,
                avg_reward,
                avg_actor_loss,
                avg_critic_loss
            );
            environment.summary();
        }

        // 定期保存检查点
        if episode % config.learn.training.eval_frequency == 0 && episode > 0 {
            let checkpoint_path = format!("checkpoints/sac_episode_{}.safetensors", episode);
            debug!("保存检查点: {}", checkpoint_path);
        }
    }

    let training_duration = training_start.elapsed().as_secs_f64();
    let average_reward = episode_rewards.iter().sum::<f64>() / episode_rewards.len() as f64;

    info!("=== 训练完成 ===");
    info!("总训练时间: {:.2}秒", training_duration);
    info!("平均奖励: {:.4}", average_reward);
    info!("最佳奖励: {:.4}", best_reward);

    Ok(TrainingResult {
        total_steps,
        total_episodes: episodes,
        average_reward,
        best_episode_reward: best_reward,
        training_duration,
        final_critic_loss: total_critic_loss / episodes as f64,
        final_actor_loss: total_actor_loss / episodes as f64,
        episode_rewards,
        episode_lengths,
    })
}

/// 生成随机连续动作
fn generate_random_continuous_action() -> ActionType {
    use rand::Rng;
    let mut rng = rand::rng();
    let position_ratio = rng.random_range(-1.0..1.0); // -1到1之间的随机仓位比例
    ActionType::ContinuousPosition { position_ratio }
}

/// 从合法动作掩码中生成随机动作索引（动作掩码版本）
fn generate_random_masked_action_index(action_mask: &[bool]) -> usize {
    use rand::Rng;
    
    // 收集所有合法的动作索引
    let valid_actions: Vec<usize> = action_mask
        .iter()
        .enumerate()
        .filter_map(|(i, &is_valid)| if is_valid { Some(i) } else { None })
        .collect();
    
    if valid_actions.is_empty() {
        // 如果没有合法动作，强制返回Hold动作（索引0）
        log::warn!("没有合法动作可选，强制选择Hold动作");
        0
    } else {
        // 从合法动作中随机选择
        let mut rng = rand::rng();
        let random_index = rng.random_range(0..valid_actions.len());
        valid_actions[random_index]
    }
}

/// 将动作索引转换为环境动作
fn convert_index_to_action(action_index: usize) -> Result<ActionType> {
    match action_index {
        0 => Ok(ActionType::Hold),
        1 => Ok(ActionType::Buy),
        2 => Ok(ActionType::Sell),
        3 => Ok(ActionType::Close),
        _ => Ok(ActionType::Hold), // 默认持有
    }
}

/// 从神经网络选择连续动作
fn select_continuous_action(
    trainer: &SacTrainer<drl_trading_learn::DefaultBackend>,
    state_vector: &[f64]
) -> Result<ActionType> {
    // 使用Actor网络生成连续动作
    let position_ratio = trainer.agent().select_continuous_action(state_vector, true)?;
    Ok(ActionType::ContinuousPosition { position_ratio })
}

/// 将动作转换为向量（用于经验存储）
fn action_to_vector(action: &ActionType) -> Vec<f64> {
    match action {
        ActionType::ContinuousPosition { position_ratio } => vec![*position_ratio],
        ActionType::Hold => vec![0.0],
        ActionType::Buy => vec![1.0],
        ActionType::Sell => vec![-0.5],
        ActionType::Close => vec![0.0],
        ActionType::Complex { .. } => vec![0.0],
    }
}

fn save_training_result(result: &TrainingResult, _config: &AppConfig) -> Result<()> {
    // 确保checkpoints目录存在
    fs::create_dir_all("checkpoints")?;

    // 保存训练结果到JSON文件
    let result_path = "checkpoints/training_result.json";
    let json_str = serde_json::to_string_pretty(result).context("序列化训练结果失败")?;
    fs::write(result_path, json_str).context("写入训练结果文件失败")?;

    info!("训练结果已保存到: {}", result_path);
    Ok(())
}
