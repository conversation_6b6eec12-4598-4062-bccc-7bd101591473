//! # 评估模式
//! 
//! 评估已训练智能体的性能

use anyhow::{Context, Result};
use crate::config::AppConfig;
use drl_trading_learn::SacAgentConfig;

/// 运行评估模式
pub async fn run_evaluate(config: AppConfig) -> Result<()> {
    log::info!("开始评估模式");
    
    // 创建智能体
    let sac_config = SacAgentConfig::from_learn_config(&config.learn);
    let device = drl_trading_learn::create_best_device();
    let mut agent: drl_trading_learn::DefaultSacAgent = drl_trading_learn::SacAgent::new(&sac_config, &device)
        .context("创建智能体失败")?;
    
    agent.initialize()
        .context("初始化智能体失败")?;
    
    // 检查是否有模型文件可以加载
    let model_path = config.app.checkpoint.save_dir.join("final_model.json");
    if model_path.exists() {
        agent.load_from_file(model_path.to_str().unwrap())
            .context("加载模型失败")?;
        log::info!("已加载模型：{:?}", model_path);
    } else {
        log::warn!("未找到预训练模型，使用随机初始化的智能体");
    }
    
    // 运行评估（使用训练配置中的评估参数）
    let num_episodes = 5; // 固定评估回合数
    let eval_metrics = agent.evaluate(num_episodes)
        .context("评估失败")?;
    
    // 输出评估结果
    log::info!("评估完成");
    log::info!("平均奖励: {:.4}", eval_metrics.average_reward);
    log::info!("评估回合数: {}", eval_metrics.total_episodes);
    log::info!("成功率: {:.2}%", eval_metrics.success_rate * 100.0);
    
    // 保存评估结果
    let eval_result_path = config.app.output_dir.join("evaluation_result.json");
    let eval_json = serde_json::to_string_pretty(&eval_metrics)
        .context("序列化评估结果失败")?;
    
    std::fs::write(&eval_result_path, eval_json)
        .with_context(|| format!("写入评估结果失败: {:?}", eval_result_path))?;
    
    log::info!("评估结果已保存到: {:?}", eval_result_path);
    
    Ok(())
} 