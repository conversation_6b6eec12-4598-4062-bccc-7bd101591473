//! # 公共模式工具
//! 
//! 提供各种模式共用的组件创建函数

use anyhow::Result;

use crate::config::AppConfig;
// 导入env模块，包含所有需要的组件


// 导入各模块组件
use drl_trading_data::{DataProvider, HistoricalProvider};
use drl_trading_account::Account;
use drl_trading_learn::{DefaultSacAgent, SacAgentConfig};

/// 创建历史数据提供者（同步版本）
pub fn create_data_provider(config: &AppConfig) -> Result<Box<dyn DataProvider + Send + Sync>> {
    log::info!("创建历史数据提供者");
    
    // 根据配置创建不同类型的数据提供者
    let file_path = config.data.data_path.clone().unwrap_or_else(|| std::path::PathBuf::from("data/sample.csv"));
    let provider = HistoricalProvider::from_csv(
        file_path,
        drl_trading_core::types::Symbol::new(&config.data.symbol),
        config.learn.training.steps_per_episode
    )?;
    
    log::info!("历史数据提供者创建完成");
    Ok(Box::new(provider))
}

/// 创建模拟账户
pub fn create_account(config: &AppConfig) -> Result<Account> {
    log::info!("创建模拟账户");
    
    // 创建账户配置
    let account_config = drl_trading_account::AccountConfig {
        initial_balance: rust_decimal::Decimal::from(config.account.initial_balance),
        fee_rate: 0.001,
        slippage: 0.0001,
        leverage: 1.0,
        min_trade_value: rust_decimal::Decimal::from(10),
        max_position_ratio: 1.0,
        base_currency: "USDT".to_string(),
    };
    let account = Account::new(account_config)?;
    
    log::info!("模拟账户创建完成，初始余额: {}", account.state.total_balance);
    Ok(account)
}

/// 创建智能体
pub fn create_agent(config: &AppConfig) -> Result<DefaultSacAgent> {
    log::info!("创建SAC智能体");
    
    // 将 LearnConfig 转换为 SacAgentConfig
    let sac_config = SacAgentConfig::from_learn_config(&config.learn);
    
    // 使用最佳设备创建智能体
    let device = drl_trading_learn::create_best_device();
    let mut agent = drl_trading_learn::SacAgent::new(&sac_config, &device)?;
    agent.initialize()?;
    
    log::info!("SAC智能体创建完成");
    Ok(agent)
}
