//! # 公共模式工具
//! 
//! 提供各种模式共用的组件创建函数

use anyhow::Result;

use crate::config::AppConfig;
// 导入env模块，包含所有需要的组件


// 导入各模块组件
use drl_trading_data::{DataProvider, HistoricalProvider, SineWaveDataProvider, SineWaveConfig};
use drl_trading_account::Account;
use drl_trading_learn::{DefaultSacAgent, SacAgentConfig};

/// 创建数据提供者（支持历史数据和合成数据）
pub fn create_data_provider(config: &AppConfig) -> Result<Box<dyn DataProvider + Send + Sync>> {
    log::info!("创建数据提供者，类型: {}", config.data.provider_type);

    match config.data.provider_type.as_str() {
        "historical" => {
            // 创建历史数据提供者
            let file_path = config.data.data_path.clone().unwrap_or_else(|| std::path::PathBuf::from("data/sample.csv"));
            let provider = HistoricalProvider::from_csv(
                file_path,
                drl_trading_core::types::Symbol::new(&config.data.symbol),
                config.learn.training.steps_per_episode
            )?;
            log::info!("历史数据提供者创建完成");
            Ok(Box::new(provider))
        },
        "synthetic" => {
            // 创建合成数据提供者（正弦波）
            let sine_config = create_sine_wave_config(config)?;
            let provider = SineWaveDataProvider::new(
                sine_config,
                drl_trading_core::types::Symbol::new(&config.data.symbol)
            )?;
            log::info!("合成数据提供者创建完成");
            Ok(Box::new(provider))
        },
        _ => {
            anyhow::bail!("不支持的数据提供器类型: {}", config.data.provider_type);
        }
    }
}

/// 从配置创建正弦波配置
fn create_sine_wave_config(config: &AppConfig) -> Result<SineWaveConfig> {
    // 如果配置文件中有正弦波配置，使用它
    if let Some(sine_config_inner) = &config.data.sine_wave {
        let sine_config = SineWaveConfig {
            base_price: sine_config_inner.base_price,
            amplitude: sine_config_inner.amplitude,
            period: sine_config_inner.period,
            phase: sine_config_inner.phase,
            trend_slope: sine_config_inner.trend_slope,
            noise_level: sine_config_inner.noise_level,
            total_points: sine_config_inner.total_points,
            time_interval_ms: sine_config_inner.time_interval_ms,
        };
        Ok(sine_config)
    } else {
        // 使用默认配置
        let sine_config = SineWaveConfig {
            base_price: 100.0,
            amplitude: 15.0,
            period: 200.0,
            phase: 0.0,
            trend_slope: 0.01,
            noise_level: 0.02,
            total_points: 10000,
            time_interval_ms: 60000,
        };
        log::warn!("配置文件中未找到正弦波配置，使用默认参数");
        Ok(sine_config)
    }
}

/// 创建模拟账户
pub fn create_account(config: &AppConfig) -> Result<Account> {
    log::info!("创建模拟账户");
    
    // 创建账户配置
    let account_config = drl_trading_account::AccountConfig {
        initial_balance: rust_decimal::Decimal::from(config.account.initial_balance),
        fee_rate: 0.001,
        slippage: 0.0001,
        leverage: 1.0,
        min_trade_value: rust_decimal::Decimal::from(10),
        max_position_ratio: 1.0,
        base_currency: "USDT".to_string(),
    };
    let account = Account::new(account_config)?;
    
    log::info!("模拟账户创建完成，初始余额: {}", account.state.total_balance);
    Ok(account)
}

/// 创建智能体
pub fn create_agent(config: &AppConfig) -> Result<DefaultSacAgent> {
    log::info!("创建SAC智能体");
    
    // 将 LearnConfig 转换为 SacAgentConfig
    let sac_config = SacAgentConfig::from_learn_config(&config.learn);
    
    // 使用最佳设备创建智能体
    let device = drl_trading_learn::create_best_device();
    let mut agent = drl_trading_learn::SacAgent::new(&sac_config, &device)?;
    agent.initialize()?;
    
    log::info!("SAC智能体创建完成");
    Ok(agent)
}
