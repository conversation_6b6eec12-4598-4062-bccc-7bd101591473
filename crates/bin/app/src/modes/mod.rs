//! # 运行模式模块
//! 
//! 定义不同的运行模式：回测、实盘、训练、评估

pub mod backtest;
pub mod live;
pub mod train;
pub mod evaluate;
mod common;

// 重新导出主要功能
pub use backtest::run_backtest;
pub use live::run_live;
pub use train::run_train;

pub use evaluate::run_evaluate;

use crate::config::AppConfig;
use anyhow::Result;

/// 运行模式枚举
#[derive(Debug, Clone, PartialEq)]
pub enum Mode {
    Backtest,
    Live,
    Train,
    Evaluate,
}

impl std::str::FromStr for Mode {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "backtest" => Ok(Mode::Backtest),
            "live" => Ok(Mode::Live),
            "train" => Ok(Mode::Train),
            "evaluate" => Ok(Mode::Evaluate),
            _ => Err(anyhow::anyhow!("未知的运行模式: {}", s)),
        }
    }
}

impl std::fmt::Display for Mode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Mode::Backtest => write!(f, "backtest"),
            Mode::Live => write!(f, "live"),
            Mode::Train => write!(f, "train"),
            Mode::Evaluate => write!(f, "evaluate"),
        }
    }
}

/// 运行指定模式
pub fn run_mode(mode: Mode, config: AppConfig) -> Result<()> {
    log::info!("开始运行模式: {}", mode);
    
    match mode {
        Mode::Train => run_train(config),
        _ => {
            log::info!("不支持的模式: {}", mode);
            Ok(())
        }
    }
} 