#![recursion_limit = "256"] 
mod cli;
mod config;
mod modes;

use anyhow::{Context, Result};
use std::path::PathBuf;

use cli::{Cli, Commands, DataCommands, RunMode};
use config::AppConfig;
use modes::Mode;

/// 主程序入口
#[tokio::main]
async fn main() -> Result<()> {
    // 解析命令行参数
    let cli = Cli::parse_args();
    
    // 初始化日志系统
    cli.init_logging()
        .context("初始化日志系统失败")?;
    
    log::info!("DRL Trading System 启动");
    log::info!("版本: {}", env!("CARGO_PKG_VERSION"));
    
    // 处理命令
    match cli.command {
        Commands::Run { mode, config, verbose, output } => {
            handle_run_command(mode, config, verbose, output).await?;
        }
        Commands::Validate { config } => {
            handle_validate_command(config).await?;
        }
        Commands::InitConfig { output, mode } => {
            handle_init_config_command(output, mode).await?;
        }
        Commands::Data { action } => {
            handle_data_command(action).await?;
        }
    }
    log::info!("DRL Trading System 退出");
    Ok(())
}

/// 处理运行命令
async fn handle_run_command(
    mode: RunMode,
    config_path: PathBuf,
    verbose: bool,
    output: Option<PathBuf>,
) -> Result<()> {
    log::info!("运行模式: {}", mode);
    log::info!("配置文件: {:?}", config_path);
    
    // 加载配置
    let mut config = AppConfig::from_file(&config_path)
        .with_context(|| format!("加载配置文件失败: {:?}", config_path))?;
    
    // 应用命令行覆盖
    if verbose {
        config.app.verbose = true;
    }
    
    if let Some(output_dir) = output {
        config.app.output_dir = output_dir;
    }
    
    // 确保输出目录存在
    std::fs::create_dir_all(&config.app.output_dir)
        .with_context(|| format!("创建输出目录失败: {:?}", config.app.output_dir))?;
    
    // 转换运行模式
    let run_mode = match mode {
        RunMode::Backtest => Mode::Backtest,
        RunMode::Live => Mode::Live,
        RunMode::Train => Mode::Train,
        RunMode::Evaluate => Mode::Evaluate,
    };
    
    // 设置信号处理器
    setup_signal_handlers();
    // 运行指定模式
    let start_time = std::time::Instant::now();
    let result = modes::run_mode(run_mode, config.clone());
    let duration = start_time.elapsed();
    log::info!("运行完成，耗时: {:.2}秒", duration.as_secs_f64());
    match result {
        Ok(()) => {
            log::info!("运行成功完成");
            Ok(())
        }
        Err(e) => {
            log::error!("运行失败: {}", e);
            
            // 保存错误信息
            let error_path = config.get_output_path("error.log");
            let error_info = format!(
                "时间: {}\n模式: {}\n错误: {:#}\n",
                chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"),
                mode,
                e
            );
            if let Err(write_err) = std::fs::write(&error_path, error_info) {
                log::warn!("无法写入错误日志: {}", write_err);
            } else {
                log::info!("错误信息已保存到: {:?}", error_path);
            }
            Err(e)
        }
    }
}

/// 处理配置验证命令
async fn handle_validate_command(config_path: PathBuf) -> Result<()> {
    log::info!("验证配置文件: {:?}", config_path);
    
    match AppConfig::from_file(&config_path) {
        Ok(config) => {
            log::info!("配置验证成功");
            log::info!("应用名称: {}", config.app.name);
            log::info!("运行模式: {}", config.app.mode);
            log::info!("输出目录: {:?}", config.app.output_dir);
            
            // 显示详细配置信息
            if log::log_enabled!(log::Level::Debug) {
                let config_json = serde_json::to_string_pretty(&config)
                    .context("序列化配置失败")?;
                log::debug!("完整配置:\n{}", config_json);
            }
            
            Ok(())
        }
        Err(e) => {
            log::error!("配置验证失败: {}", e);
            Err(e)
        }
    }
}

/// 处理初始化配置命令
async fn handle_init_config_command(output_path: PathBuf, mode: RunMode) -> Result<()> {
    log::info!("生成示例配置文件: {:?}", output_path);
    log::info!("配置类型: {}", mode);
    
    // 生成示例配置
    let config = AppConfig::generate_example(&mode.to_string());
    
    // 保存配置文件
    config.save_to_file(&output_path)
        .with_context(|| format!("保存配置文件失败: {:?}", output_path))?;
    
    log::info!("示例配置文件已生成: {:?}", output_path);
    
    // 显示配置内容
    println!("\n生成的配置文件内容:");
    println!("========================");
    let config_content = std::fs::read_to_string(&output_path)?;
    println!("{}", config_content);
    println!("========================");
    
    println!("\n使用方法:");
    println!("1. 根据需要修改配置文件");
    println!("2. 运行: drl-trading run --mode {} --config {:?}", mode, output_path);
    
    Ok(())
}

/// 处理数据命令
async fn handle_data_command(action: DataCommands) -> Result<()> {
    match action {
        DataCommands::Download { symbol, from, to, interval, output } => {
            log::info!("下载历史数据");
            log::info!("交易对: {}", symbol);
            log::info!("时间范围: {} 到 {}", from, to);
            log::info!("时间间隔: {}", interval);
            log::info!("输出文件: {:?}", output);
            
            // TODO: 实现数据下载功能
            log::warn!("数据下载功能尚未实现");
        }
        DataCommands::Validate { file } => {
            log::info!("验证数据文件: {:?}", file);
            
            // TODO: 实现数据验证功能
            if file.exists() {
                log::info!("数据文件存在");
                // 可以添加更多验证逻辑
            } else {
                log::error!("数据文件不存在");
                return Err(anyhow::anyhow!("数据文件不存在: {:?}", file));
            }
        }
        DataCommands::Info { file } => {
            log::info!("数据文件信息: {:?}", file);
            
            if file.exists() {
                let metadata = std::fs::metadata(&file)?;
                let size = metadata.len();
                let modified = metadata.modified()?;
                
                log::info!("文件大小: {} 字节", size);
                log::info!("修改时间: {:?}", modified);
                
                // TODO: 添加更多数据统计信息
            } else {
                log::error!("数据文件不存在");
                return Err(anyhow::anyhow!("数据文件不存在: {:?}", file));
            }
        }
    }
    
    Ok(())
}

/// 设置信号处理器
fn setup_signal_handlers() {
    // 设置 Ctrl+C 处理器
    tokio::spawn(async {
        match tokio::signal::ctrl_c().await {
            Ok(()) => {
                log::info!("收到中断信号，正在优雅退出...");
                // TODO: 实现优雅退出逻辑
                // 保存检查点、关闭连接等
                std::process::exit(0);
            }
            Err(err) => {
                log::error!("监听中断信号失败: {}", err);
            }
        }
    });
}
