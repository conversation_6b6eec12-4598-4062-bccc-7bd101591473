use clap::{Parser, Subcommand, ValueEnum};
use std::path::PathBuf;

/// DRL Trading System - 深度强化学习交易系统
#[derive(Parser)]
#[command(
    name = "drl-trading",
    version = "0.1.0",
    author = "DRL Trading Team",
    about = "A deep reinforcement learning trading system",
    long_about = "一个基于深度强化学习的自动化交易系统，支持回测和实盘交易"
)]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,

    /// 设置日志级别
    #[arg(short, long, default_value = "info")]
    pub log_level: String,

    /// 日志配置文件路径
    #[arg(long)]
    pub log_config: Option<PathBuf>,

    /// 是否启用详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

#[derive(Subcommand)]
pub enum Commands {
    /// 运行交易系统
    Run {
        /// 指定运行模式
        #[arg(short, long, value_enum)]
        mode: RunMode,

        /// 指定配置文件路径
        #[arg(short, long, value_name = "FILE")]
        config: PathBuf,

        /// 是否启用详细输出
        #[arg(short, long)]
        verbose: bool,

        /// 输出目录（用于保存结果、日志等）
        #[arg(short, long)]
        output: Option<PathBuf>,
    },

    /// 验证配置文件
    Validate {
        /// 配置文件路径
        #[arg(short, long, value_name = "FILE")]
        config: PathBuf,
    },

    /// 生成示例配置文件
    InitConfig {
        /// 输出配置文件路径
        #[arg(short, long, default_value = "config.toml")]
        output: PathBuf,

        /// 配置类型
        #[arg(short, long, value_enum, default_value = "backtest")]
        mode: RunMode,
    },

    /// 数据相关操作
    Data {
        #[command(subcommand)]
        action: DataCommands,
    },
}

#[derive(Subcommand)]
pub enum DataCommands {
    /// 下载历史数据
    Download {
        /// 交易对
        #[arg(short, long)]
        symbol: String,

        /// 开始日期 (YYYY-MM-DD)
        #[arg(short = 'f', long)]
        from: String,

        /// 结束日期 (YYYY-MM-DD)
        #[arg(short = 't', long)]
        to: String,

        /// 时间间隔
        #[arg(short, long, default_value = "1m")]
        interval: String,

        /// 输出文件路径
        #[arg(short, long)]
        output: PathBuf,
    },

    /// 验证数据文件
    Validate {
        /// 数据文件路径
        #[arg(short, long)]
        file: PathBuf,
    },

    /// 数据统计信息
    Info {
        /// 数据文件路径
        #[arg(short, long)]
        file: PathBuf,
    },
}

#[derive(Copy, Clone, PartialEq, Eq, PartialOrd, Ord, ValueEnum)]
pub enum RunMode {
    /// 回测模式
    Backtest,
    /// 实盘交易模式
    Live,
    /// 训练模式
    Train,
    /// 评估模式
    Evaluate,
}

impl std::fmt::Display for RunMode {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RunMode::Backtest => write!(f, "backtest"),
            RunMode::Live => write!(f, "live"),
            RunMode::Train => write!(f, "train"),
            RunMode::Evaluate => write!(f, "evaluate"),
        }
    }
}

impl Cli {
    /// 解析命令行参数
    pub fn parse_args() -> Self {
        Self::parse()
    }

    /// 初始化日志系统
    pub fn init_logging(&self) -> anyhow::Result<()> {
        match &self.log_config {
            Some(log_config) => {
                // 使用env_logger替代log4rs
                let mut builder = env_logger::Builder::from_default_env();
                
                // 设置日志级别
                if self.verbose {
                    builder.filter_level(log::LevelFilter::Debug);
                } else {
                    builder.filter_level(log::LevelFilter::Info);
                }
                
                // 设置日志格式（包含时间戳）
                builder.format(|buf, record| {
                    use std::io::Write;
                    let now = chrono::Local::now();
                    writeln!(buf, "[{}] [{}] {}: {}", 
                            now.format("%Y-%m-%d %H:%M:%S%.3f"),
                            record.level(), 
                            record.target(), 
                            record.args())
                });
                
                builder.init();
                
                log::info!("日志系统已初始化：配置文件 {:?}", log_config);
            }
            None => {
                // 使用默认日志配置
                let mut builder = env_logger::Builder::from_default_env();
                
                if self.verbose {
                    builder.filter_level(log::LevelFilter::Debug);
                } else {
                    builder.filter_level(log::LevelFilter::Info);
                }
                
                builder.format(|buf, record| {
                    use std::io::Write;
                    let now = chrono::Local::now();
                    writeln!(buf, "[{}] [{}] {}: {}", 
                            now.format("%Y-%m-%d %H:%M:%S%.3f"),
                            record.level(), 
                            record.target(), 
                            record.args())
                });
                
                builder.init();
                
                log::info!("日志系统已初始化：默认配置");
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_run_mode_display() {
        assert_eq!(RunMode::Backtest.to_string(), "backtest");
        assert_eq!(RunMode::Live.to_string(), "live");
        assert_eq!(RunMode::Train.to_string(), "train");
        assert_eq!(RunMode::Evaluate.to_string(), "evaluate");
    }

    #[test]
    fn test_cli_parse() {
        // 这个测试需要实际的命令行参数，在单元测试中不太适用
        // 但可以用来验证结构定义是否正确
        assert_eq!(std::mem::size_of::<Cli>(), std::mem::size_of::<Cli>());
    }
} 