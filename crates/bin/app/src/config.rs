use std::path::{Path, PathBuf};
use serde::{Deserialize, Serialize};
use anyhow::{Context, Result};
use drl_trading_core::reward::RewardCalculationConfig;
// 导入学习模块的配置
use drl_trading_learn::config::LearnConfig;

/// 应用总配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 应用基础配置
    pub app: AppBaseConfig,
    
    /// 数据源配置
    pub data: DataSourceConfig,
    
    /// 账户配置
    pub account: AccountConfigInner,
    
    /// 执行配置
    pub execution: ExecutionConfigInner,
    
    /// 环境配置
    pub environment: EnvironmentConfigInner,
    
    /// 学习模块配置
    pub learn: LearnConfig,
}

/// 数据源配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSourceConfig {
    /// 数据提供者类型
    pub provider_type: String,
    /// 数据文件路径（历史数据模式）
    pub data_path: Option<PathBuf>,
    /// 交易对
    pub symbol: String,
    /// 时间间隔
    pub interval: String,
    /// 开始时间
    pub start_time: Option<String>,
    /// 结束时间
    pub end_time: Option<String>,
}

/// 账户配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountConfigInner {
    /// 账户类型
    pub account_type: String,
    /// 初始余额
    pub initial_balance: i64,
    /// 交易手续费率
    pub fee_rate: f64,
    /// 最大杠杆
    pub max_leverage: f64,
}

/// 执行配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionConfigInner {
    /// 执行处理器类型
    pub handler_type: String,
    /// 滑点设置
    pub slippage: f64,
    /// 延迟设置
    pub latency_ms: u64,
}

/// 环境配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnvironmentConfigInner {
    /// 环境类型
    pub env_type: String,
    /// 最大步数
    pub max_steps: Option<u64>,
    /// 奖励函数类型
    pub reward_function: String,
    /// 奖励配置
    pub reward: Option<RewardConfigInner>,
}

/// 奖励配置（内部配置结构）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RewardConfigInner {
    /// 主要权重配置
    pub portfolio_change_weight: f64,
    pub transaction_cost_weight: f64,
    
    /// 持仓状态奖励配置
    pub profitable_holding_weight: f64,
    pub losing_holding_weight: f64,
    pub holding_reward_cap: f64,
    
    /// 风险控制配置
    pub max_position_concentration: f64,
    pub concentration_penalty_coeff: f64,
    pub max_drawdown_threshold: f64,
    pub drawdown_penalty_coeff: f64,
    pub max_volatility_threshold: f64,
    pub volatility_penalty_coeff: f64,
}

impl RewardConfigInner {
    /// 转换为RewardCalculationConfig
    pub fn to_reward_calculation_config(&self) -> RewardCalculationConfig {
        RewardCalculationConfig {
            portfolio_change_weight: self.portfolio_change_weight,
            transaction_cost_weight: self.transaction_cost_weight,
            profitable_holding_weight: self.profitable_holding_weight,
            losing_holding_weight: self.losing_holding_weight,
            holding_reward_cap: self.holding_reward_cap,
            max_position_concentration: self.max_position_concentration,
            concentration_penalty_coeff: self.concentration_penalty_coeff,
            max_drawdown_threshold: self.max_drawdown_threshold,
            drawdown_penalty_coeff: self.drawdown_penalty_coeff,
            max_volatility_threshold: self.max_volatility_threshold,
            volatility_penalty_coeff: self.volatility_penalty_coeff,
        }
    }
}

/// 应用基础配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppBaseConfig {
    /// 应用名称
    pub name: String,
    
    /// 应用版本
    pub version: String,
    
    /// 运行模式
    pub mode: String,
    
    /// 输出目录
    pub output_dir: PathBuf,
    
    /// 是否启用详细日志
    pub verbose: bool,
    
    /// 最大运行时间（秒），0表示无限制
    pub max_runtime_seconds: u64,
    
    /// 性能指标配置
    pub metrics: MetricsConfig,
    
    /// 检查点配置
    pub checkpoint: CheckpointConfig,
}

/// 性能指标配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricsConfig {
    /// 是否启用指标收集
    pub enabled: bool,
    
    /// 指标输出文件路径
    pub output_file: Option<PathBuf>,
    
    /// 指标更新频率（步数）
    pub update_frequency: u64,
    
    /// 是否保存详细指标
    pub save_detailed: bool,
}

/// 检查点配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckpointConfig {
    /// 是否启用检查点
    pub enabled: bool,
    
    /// 检查点保存目录
    pub save_dir: PathBuf,
    
    /// 检查点保存频率（步数）
    pub save_frequency: u64,
    
    /// 最大保留检查点数量
    pub max_checkpoints: usize,
    
    /// 是否在程序退出时自动保存
    pub auto_save_on_exit: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            app: AppBaseConfig::default(),
            data: DataSourceConfig::default(),
            account: AccountConfigInner::default(),
            execution: ExecutionConfigInner::default(),
            environment: EnvironmentConfigInner::default(),
            learn: LearnConfig::default(),
        }
    }
}

impl Default for DataSourceConfig {
    fn default() -> Self {
        Self {
            provider_type: "historical".to_string(),
            data_path: Some(PathBuf::from("data/sample.csv")),
            symbol: "BTCUSDT".to_string(),
            interval: "1m".to_string(),
            start_time: None,
            end_time: None,
        }
    }
}

impl Default for AccountConfigInner {
    fn default() -> Self {
        Self {
            account_type: "simulated".to_string(),
            initial_balance: 10000,
            fee_rate: 0.001,
            max_leverage: 1.0,
        }
    }
}

impl Default for ExecutionConfigInner {
    fn default() -> Self {
        Self {
            handler_type: "simulated".to_string(),
            slippage: 0.0001,
            latency_ms: 10,
        }
    }
}

impl Default for EnvironmentConfigInner {
    fn default() -> Self {
        Self {
            env_type: "trading".to_string(),
            max_steps: None,
            reward_function: "profit_based".to_string(),
            reward: None,
        }
    }
}

impl Default for AppBaseConfig {
    fn default() -> Self {
        Self {
            name: "DRL Trading System".to_string(),
            version: "0.1.0".to_string(),
            mode: "backtest".to_string(),
            output_dir: PathBuf::from("./output"),
            verbose: false,
            max_runtime_seconds: 0,
            metrics: MetricsConfig::default(),
            checkpoint: CheckpointConfig::default(),
        }
    }
}

impl Default for MetricsConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            output_file: Some(PathBuf::from("metrics.json")),
            update_frequency: 100,
            save_detailed: false,
        }
    }
}

impl Default for CheckpointConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            save_dir: PathBuf::from("./checkpoints"),
            save_frequency: 1000,
            max_checkpoints: 5,
            auto_save_on_exit: true,
        }
    }
}

impl AppConfig {
    /// 从文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = std::fs::read_to_string(path.as_ref())
            .with_context(|| format!("无法读取配置文件: {:?}", path.as_ref()))?;
        
        let config: AppConfig = toml::from_str(&content)
            .with_context(|| format!("无法解析配置文件: {:?}", path.as_ref()))?;
        
        // 验证配置
        config.validate()
            .with_context(|| "配置验证失败")?;
        
        log::info!("已加载配置文件: {:?}", path.as_ref());
        Ok(config)
    }
    
    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .with_context(|| "无法序列化配置")?;
        
        // 确保目录存在
        if let Some(parent) = path.as_ref().parent() {
            std::fs::create_dir_all(parent)
                .with_context(|| format!("无法创建目录: {:?}", parent))?;
        }
        
        std::fs::write(path.as_ref(), content)
            .with_context(|| format!("无法写入配置文件: {:?}", path.as_ref()))?;
        
        log::info!("已保存配置文件: {:?}", path.as_ref());
        Ok(())
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证输出目录
        if !self.app.output_dir.exists() {
            std::fs::create_dir_all(&self.app.output_dir)
                .with_context(|| format!("无法创建输出目录: {:?}", self.app.output_dir))?;
        }
        
        // 验证检查点目录
        if self.app.checkpoint.enabled && !self.app.checkpoint.save_dir.exists() {
            std::fs::create_dir_all(&self.app.checkpoint.save_dir)
                .with_context(|| format!("无法创建检查点目录: {:?}", self.app.checkpoint.save_dir))?;
        }
        
        // 验证学习模块配置
        self.learn.validate()
            .with_context(|| "学习模块配置验证失败")?;
        
        log::info!("配置验证通过");
        Ok(())
    }
    
    /// 生成示例配置
    pub fn generate_example(mode: &str) -> Self {
        let mut config = Self::default();
        config.app.mode = mode.to_string();
        
        match mode {
            "backtest" => {
                // 回测模式配置
                config.app.max_runtime_seconds = 3600; // 1小时
                config.app.metrics.update_frequency = 100;
                config.app.checkpoint.save_frequency = 1000;
                
                // 配置历史数据源
                config.data.provider_type = "historical".to_string();
                
                // 配置模拟账户
                config.account.account_type = "simulated".to_string();
                config.account.initial_balance = 10000; // $100,000
                
                // 配置模拟执行
                config.execution.handler_type = "simulated".to_string();
            },
            "live" => {
                // 实盘模式配置
                config.app.max_runtime_seconds = 0; // 无限制
                config.app.metrics.update_frequency = 10;
                config.app.checkpoint.save_frequency = 100;
                
                // 配置实时数据源
                config.data.provider_type = "live".to_string();
                
                // 配置真实账户
                config.account.account_type = "live".to_string();
                
                // 配置真实执行
                config.execution.handler_type = "live".to_string();
            },
            "train" => {
                // 训练模式配置
                config.app.max_runtime_seconds = 0; // 无限制
                config.app.metrics.update_frequency = 50;
                config.app.checkpoint.save_frequency = 500;
                
                // 配置训练数据源
                config.data.provider_type = "historical".to_string();
                
                // 配置模拟账户
                config.account.account_type = "simulated".to_string();
                config.account.initial_balance = 10000;
                
                // 配置训练参数
                config.learn.training.episodes = 100;
                config.learn.training.steps_per_episode = 1000;
            },
            _ => {
                // 默认回测模式
                config.app.mode = "backtest".to_string();
            }
        }
        
        config
    }
    
    /// 获取完整的输出路径
    pub fn get_output_path(&self, filename: &str) -> PathBuf {
        self.app.output_dir.join(filename)
    }
    
    /// 获取检查点路径
    pub fn get_checkpoint_path(&self, filename: &str) -> PathBuf {
        self.app.checkpoint.save_dir.join(filename)
    }
    
    /// 获取指标输出路径
    pub fn get_metrics_path(&self) -> Option<PathBuf> {
        self.app.metrics.output_file.as_ref().map(|filename| {
            if filename.is_absolute() {
                filename.clone()
            } else {
                self.app.output_dir.join(filename)
            }
        })
    }
}

// 简化配置验证
impl DataSourceConfig {
    pub fn validate(&self) -> Result<()> {
        Ok(())
    }
}

impl AccountConfigInner {
    pub fn validate(&self) -> Result<()> {
        Ok(())
    }
}

impl ExecutionConfigInner {
    pub fn validate(&self) -> Result<()> {
        Ok(())
    }
}

impl EnvironmentConfigInner {
    pub fn validate(&self) -> Result<()> {
        Ok(())
    }
}

/// 配置构建器，用于程序化创建配置
pub struct AppConfigBuilder {
    config: AppConfig,
}

impl AppConfigBuilder {
    pub fn new() -> Self {
        Self {
            config: AppConfig::default(),
        }
    }
    
    pub fn mode(mut self, mode: &str) -> Self {
        self.config.app.mode = mode.to_string();
        self
    }
    
    pub fn output_dir<P: Into<PathBuf>>(mut self, path: P) -> Self {
        self.config.app.output_dir = path.into();
        self
    }
    
    pub fn verbose(mut self, verbose: bool) -> Self {
        self.config.app.verbose = verbose;
        self
    }
    
    pub fn max_runtime(mut self, seconds: u64) -> Self {
        self.config.app.max_runtime_seconds = seconds;
        self
    }
    
    pub fn build(self) -> AppConfig {
        self.config
    }
}

impl Default for AppConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}
