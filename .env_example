# nacos环境变量
NACOS_SERVER_ADDR=***********:8848
# NACOS_NAMESPACE=public
NACOS_GROUP=DEFAULT_GROUP
NACOS_USERNAME=admin
NACOS_PASSWORD=admin
NACOS_SERVICE_NAME=data_server

# 日志级别
LOG_LEVEL=info
RUST_LOG=info
# gRPC服务器配置
GRPC_SERVER_HOST=0.0.0.0
GRPC_SERVER_PORT=50051

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/crypto_data

# Redis配置
REDIS_URL=redis://localhost:6379

# Binance API配置
BINANCE_API_URL=https://api.binance.com

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=market_data
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_CONNECT_TIMEOUT=30
DB_IDLE_TIMEOUT=600

# 服务配置
GRPC_PORT=50051
LOG_LEVEL=info
