services:
  data_server:
    build:
      context: .
      dockerfile: ./Dockerfile
      target: data-grpc
    container_name: data-grpc
    restart: always
    logging:
      driver: json-file
      options:
        max-size: 500m
    ports:
      - "50051:50051"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    environment:
      - NACOS_SERVER_ADDR=***********:8848
      #- NACOS_NAMESPACE=
      - NACOS_GROUP=DEFAULT_GROUP
      - NACOS_USERNAME=admin
      - NACOS_PASSWORD=admin
      - NACOS_SERVICE_NAME=sb-exchange
      - NACOS_CLIENT_IP=***********
      - NACOS_CLIENT_PORT=8091
      - DB_HOST=***********
      - DB_PORT=15432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres_password
      - DB_DATABASE=market_data
      - DB_MAX_CONNECTIONS=20
      - DB_MIN_CONNECTIONS=5
      - DB_CONNECT_TIMEOUT=30
      - DB_IDLE_TIMEOUT=600
      - LOG_LEVEL=info
