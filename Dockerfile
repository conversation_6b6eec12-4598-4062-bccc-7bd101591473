#FROM m.daocloud.io/docker.io/library/ubuntu:latest AS data_grpc
FROM ubuntu:latest AS data-grpc
#RUN apt-get update && apt-get install -y openssl && rm -rf /var/lib/apt/LISTS/*
#FROM alpine:latest AS opc-mqtt
#ENV TZ=Asia/Shanghai
#RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime && echo $TimeZone > /etc/timezone


# 解决代理认证的问题
RUN apt-get update && \
    apt-get install -y ca-certificates && \
    update-ca-certificates && \
    rm -rf /var/lib/apt/lists/*

ENV http_proxy 192.168.3.51:7897
ENV https_proxy 192.168.3.51:7897

WORKDIR /app
COPY   ./target/release/data-grpc ./
ENTRYPOINT ./data-grpc -e prod
