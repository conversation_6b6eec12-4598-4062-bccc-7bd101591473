use drl_trading_account::Account;
use drl_trading_core::trade::Trade;
use drl_trading_core::types::common::Symbol;
use drl_trading_core::types::execution::OrderSide;
use rust_decimal::Decimal;

fn main() {
    println!("测试修复后的指标计算功能...");
    
    // 创建账户
    let mut account = Account::new(10000).expect("创建账户失败");
    
    // 创建一些模拟交易
    let symbol = "BTC-USDT".to_string();
    
    // 买入交易
    let buy_trade = Trade {
        id: "trade1".to_string(),
        order_id: "order1".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Buy,
        quantity: Decimal::new(1, 0), // 1.0
        price: Decimal::new(50000, 0), // 50000.0
        fee: Some(Decimal::new(50, 0)), // 50.0 手续费
        timestamp: *************,
    };
    
    // 卖出交易（盈利）
    let sell_trade1 = Trade {
        id: "trade2".to_string(),
        order_id: "order2".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Sell,
        quantity: Decimal::new(5, 1), // 0.5
        price: Decimal::new(55000, 0), // 55000.0
        fee: Some(Decimal::new(27, 0)), // 27.5 手续费
        timestamp: *************,
    };
    
    // 卖出交易（亏损）
    let sell_trade2 = Trade {
        id: "trade3".to_string(),
        order_id: "order3".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Sell,
        quantity: Decimal::new(5, 1), // 0.5
        price: Decimal::new(48000, 0), // 48000.0
        fee: Some(Decimal::new(24, 0)), // 24.0 手续费
        timestamp: *************,
    };
    
    // 执行交易
    if let Err(e) = account.handler_trade(&buy_trade) {
        println!("买入交易失败: {}", e);
        return;
    }
    
    if let Err(e) = account.handler_trade(&sell_trade1) {
        println!("卖出交易1失败: {}", e);
        return;
    }
    
    if let Err(e) = account.handler_trade(&sell_trade2) {
        println!("卖出交易2失败: {}", e);
        return;
    }
    
    // 测试交易统计计算
    let (winning_trades, losing_trades, total_profit, total_loss, largest_win, largest_loss) = 
        account.calculate_trade_stats();
    
    println!("\n=== 交易统计结果 ===");
    println!("盈利交易数: {}", winning_trades);
    println!("亏损交易数: {}", losing_trades);
    println!("总盈利: {:.2}", total_profit);
    println!("总亏损: {:.2}", total_loss);
    println!("最大盈利: {:.2}", largest_win);
    println!("最大亏损: {:.2}", largest_loss);
    
    // 测试性能指标
    let performance_metrics = account.get_performance_metrics();
    println!("\n=== 性能指标 ===");
    println!("总交易次数: {}", performance_metrics.total_trades);
    println!("盈利交易: {}", performance_metrics.winning_trades);
    println!("亏损交易: {}", performance_metrics.losing_trades);
    println!("平均盈利: {:.2}", performance_metrics.average_win);
    println!("平均亏损: {:.2}", performance_metrics.average_loss);
    println!("最大盈利: {:.2}", performance_metrics.largest_win);
    println!("最大亏损: {:.2}", performance_metrics.largest_loss);
    
    // 测试风险指标
    let risk_metrics = account.get_risk_metrics();
    println!("\n=== 风险指标 ===");
    println!("胜率: {:.2}%", risk_metrics.win_rate * 100.0);
    println!("盈利因子: {:.2}", risk_metrics.profit_factor);
    println!("持仓集中度: {:.2}%", risk_metrics.position_concentration * 100.0);
    
    println!("\n修复验证完成！");
} 