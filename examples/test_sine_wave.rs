//! # 正弦波数据测试示例
//! 
//! 验证正弦波数据生成和连续动作空间的基本功能

use drl_trading_data::{SineWaveDataProvider, SineWaveConfig, DataProvider};
use drl_trading_core::{Symbol, ActionType};
use drl_trading_env::{SimpleRewardCalculator, ActionExecutor, ActionExecutorConfig};
use drl_trading_account::{Account, AccountConfig};
use rust_decimal::Decimal;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();
    
    println!("🧪 正弦波数据和连续动作测试");
    println!("================================");
    
    // 1. 测试正弦波数据生成
    test_sine_wave_generation().await?;
    
    // 2. 测试连续动作类型
    test_continuous_actions();
    
    // 3. 测试简化奖励计算
    test_simple_reward_calculation().await?;
    
    // 4. 测试动作执行器
    test_action_executor().await?;
    
    println!("\n✅ 所有测试完成！");
    
    Ok(())
}

/// 测试正弦波数据生成
async fn test_sine_wave_generation() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📊 测试正弦波数据生成...");
    
    // 创建正弦波配置
    let config = SineWaveConfig {
        base_price: 100.0,
        amplitude: 20.0,
        period: 50.0,
        total_points: 200,
        noise_level: 0.1,
        ..Default::default()
    };
    
    // 创建数据提供器
    let mut provider = SineWaveDataProvider::new(config, Symbol::new("TESTUSDT"))?;
    
    // 获取统计信息
    let stats = provider.get_stats();
    println!("   数据点数: {}", stats.total_points);
    println!("   价格范围: {:.2} - {:.2}", stats.min_price, stats.max_price);
    println!("   平均价格: {:.2}", stats.avg_price);
    println!("   价格波动: {:.2}", stats.price_range);
    
    // 获取前10个数据点
    println!("\n   前10个数据点:");
    for i in 0..10 {
        if let Some(tick) = provider.next_tick().await? {
            println!("   #{}: 价格={:.2}, 成交量={:.0}", 
                    i + 1, tick.close, tick.volume);
        }
    }
    
    println!("✅ 正弦波数据生成测试完成");
    Ok(())
}

/// 测试连续动作类型
fn test_continuous_actions() {
    println!("\n🎯 测试连续动作类型...");
    
    // 测试不同的仓位比例
    let test_ratios = vec![0.0, 0.5, 1.0, -0.5, -1.0];
    
    for ratio in test_ratios {
        let action = ActionType::ContinuousPosition { 
            position_ratio: ratio 
        };
        
        println!("   仓位比例: {:.1} -> 权重: {:.1}, 是否连续: {}", 
                ratio, action.weight(), action.is_continuous_position());
    }
    
    // 测试传统动作类型
    let traditional_actions = vec![
        ActionType::Hold,
        ActionType::Buy,
        ActionType::Sell,
        ActionType::Close,
    ];
    
    println!("\n   传统动作类型:");
    for action in traditional_actions {
        println!("   {:?} -> 仓位比例: {:.1}", action, action.position_ratio());
    }
    
    println!("✅ 连续动作类型测试完成");
}

/// 测试简化奖励计算
async fn test_simple_reward_calculation() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🎁 测试简化奖励计算...");
    
    // 创建奖励计算器
    let mut calculator = SimpleRewardCalculator::new(1.0, 0.1);
    
    // 创建测试账户
    let config = AccountConfig::default();
    let prev_account = Account::new(config.clone());
    let current_account = Account::new(config);
    
    // 创建测试数据
    let mut provider = SineWaveDataProvider::simple_test()?;
    let tick = provider.next_tick().await?.unwrap();
    
    // 测试不同动作的奖励
    let test_actions = vec![
        ActionType::Hold,
        ActionType::Buy,
        ActionType::ContinuousPosition { position_ratio: 0.5 },
        ActionType::ContinuousPosition { position_ratio: -0.3 },
    ];
    
    for action in test_actions {
        let (reward, components) = calculator.calculate_reward(
            &prev_account,
            &current_account,
            &action,
            &tick,
        );
        
        println!("   动作: {:?}", action);
        println!("     总奖励: {:.4}", reward);
        println!("     权益奖励: {:.4}", components.equity_reward);
        println!("     交易成本: {:.4}", components.transaction_cost);
        println!("     超额收益: {:.4}", components.excess_return_reward);
        println!("     方向奖励: {:.4}", components.direction_reward);
        println!();
    }
    
    println!("✅ 简化奖励计算测试完成");
    Ok(())
}

/// 测试动作执行器
async fn test_action_executor() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n⚙️ 测试动作执行器...");
    
    // 创建动作执行器配置
    let config = ActionExecutorConfig {
        base_capital: Decimal::new(100000, 0), // 10万
        min_position_change: 0.05, // 5%最小变化
        ..Default::default()
    };
    
    let mut executor = ActionExecutor::new(config);
    
    // 创建测试账户
    let account_config = AccountConfig::default();
    let account = Account::new(account_config);
    
    // 创建测试数据
    let mut provider = SineWaveDataProvider::simple_test()?;
    let tick = provider.next_tick().await?.unwrap();
    
    // 测试连续仓位动作转换
    let test_actions = vec![
        ActionType::ContinuousPosition { position_ratio: 0.5 },
        ActionType::ContinuousPosition { position_ratio: -0.3 },
        ActionType::ContinuousPosition { position_ratio: 0.0 },
    ];
    
    for action in test_actions {
        match executor.convert_action_to_order(
            &action,
            &account,
            &tick.symbol,
            tick.close,
            1,
        ) {
            Ok(Some(order)) => {
                println!("   动作: {:?}", action);
                println!("     生成订单: {:?} {} @ {}", 
                        order.side, order.quantity, order.price.unwrap_or_default());
            }
            Ok(None) => {
                println!("   动作: {:?} -> 无需订单", action);
            }
            Err(e) => {
                println!("   动作: {:?} -> 错误: {}", action, e);
            }
        }
    }
    
    // 获取执行器统计
    let stats = executor.get_stats();
    println!("\n   执行器统计:");
    println!("     生成订单数: {}", stats.total_orders_generated);
    println!("     基础资金: {}", stats.config.base_capital);
    println!("     最小变化阈值: {:.1}%", stats.config.min_position_change * 100.0);
    
    println!("✅ 动作执行器测试完成");
    Ok(())
}
