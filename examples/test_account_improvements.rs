//! # Account模块改进测试
//! 
//! 测试重构后的Account模块，验证连续仓位比例计算和多空盈亏计算的准确性

use drl_trading_account::{Account, AccountConfig};
use drl_trading_core::{Symbol, ActionType, Tick};
use drl_trading_core::types::execution::{Trade, OrderSide};
use rust_decimal::Decimal;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    env_logger::init();
    
    println!("🧪 Account模块改进测试");
    println!("========================");
    
    // 1. 测试账户配置和创建
    test_account_creation()?;
    
    // 2. 测试连续仓位比例计算
    test_position_ratio_calculation()?;
    
    // 3. 测试多空交易和盈亏计算
    test_long_short_trading()?;
    
    // 4. 测试仓位转换（多空切换）
    test_position_conversion()?;
    
    println!("\n✅ 所有Account测试完成！");
    
    Ok(())
}

/// 测试账户配置和创建
fn test_account_creation() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📊 测试账户配置和创建...");
    
    // 测试默认配置
    let default_account = Account::with_default_config()?;
    let config = default_account.get_config();
    
    println!("   默认配置:");
    println!("     初始资金: {}", config.initial_balance);
    println!("     手续费率: {:.3}%", config.fee_rate * 100.0);
    println!("     滑点: {:.4}%", config.slippage * 100.0);
    println!("     杠杆倍数: {}", config.leverage);
    println!("     最小交易价值: {}", config.min_trade_value);
    println!("     最大仓位比例: {:.1}%", config.max_position_ratio * 100.0);
    
    // 测试自定义配置
    let custom_config = AccountConfig {
        initial_balance: Decimal::new(50000, 0), // 5万
        fee_rate: 0.0005,                        // 0.05%
        leverage: 2.0,                           // 2倍杠杆
        max_position_ratio: 0.8,                 // 最大80%仓位
        ..Default::default()
    };
    
    let custom_account = Account::new(custom_config)?;
    println!("\n   自定义配置账户创建成功");
    println!("     初始权益: {}", custom_account.get_equity());
    
    println!("✅ 账户配置和创建测试完成");
    Ok(())
}

/// 测试连续仓位比例计算
fn test_position_ratio_calculation() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🎯 测试连续仓位比例计算...");
    
    let account = Account::with_default_config()?;
    let symbol = Symbol::new("BTCUSDT");
    let current_price = Decimal::new(50000, 0); // 50000 USDT
    
    // 测试不同目标仓位比例
    let test_ratios = vec![0.0, 0.3, 0.5, 0.8, 1.0, -0.3, -0.5, -1.0];
    
    println!("   目标仓位比例测试:");
    for ratio in test_ratios {
        let trade_quantity = account.calculate_trade_quantity_for_ratio(
            &symbol,
            ratio,
            current_price,
        );
        
        let trade_value = trade_quantity * current_price;
        let should_trade = account.should_execute_trade(trade_quantity, current_price);
        
        println!("     比例: {:>5.1}% -> 交易量: {:>8.4}, 交易价值: {:>10.2}, 应执行: {}", 
                ratio * 100.0, trade_quantity, trade_value, should_trade);
    }
    
    // 测试当前仓位比例（空仓状态）
    let current_ratio = account.get_position_ratio(&symbol, current_price);
    println!("\n   当前仓位比例: {:.1}%", current_ratio * 100.0);
    
    println!("✅ 连续仓位比例计算测试完成");
    Ok(())
}

/// 测试多空交易和盈亏计算
fn test_long_short_trading() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📈 测试多空交易和盈亏计算...");
    
    let mut account = Account::with_default_config()?;
    let symbol = Symbol::new("BTCUSDT");
    
    // 创建测试价格序列
    let prices = vec![50000.0, 52000.0, 48000.0, 55000.0, 45000.0];
    let mut timestamp = 1000000000000i64;
    
    println!("   交易序列测试:");
    
    // 1. 做多开仓
    let buy_trade = Trade {
        id: "trade_1".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Buy,
        quantity: Decimal::new(1, 0), // 1 BTC
        price: Decimal::try_from(prices[0]).unwrap(),
        timestamp,
    };
    
    account.handler_trade(&buy_trade)?;
    println!("   1. 做多开仓: {} BTC @ {}", buy_trade.quantity, buy_trade.price);
    println!("      账户权益: {}", account.get_equity());
    
    // 2. 价格上涨，查看未实现盈亏
    timestamp += 60000;
    let tick = Tick {
        symbol: symbol.clone(),
        timestamp,
        open: prices[1],
        high: prices[1] * 1.01,
        low: prices[1] * 0.99,
        close: prices[1],
        volume: 1000.0,
    };
    
    account.on_tick(&tick, None)?;
    println!("   2. 价格上涨到 {}: 权益={}, 收益率={:.2}%", 
            prices[1], account.get_equity(), account.get_total_return_f64() * 100.0);
    
    // 3. 部分平仓
    let sell_trade = Trade {
        id: "trade_2".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Sell,
        quantity: Decimal::new(5, 1), // 0.5 BTC
        price: Decimal::try_from(prices[1]).unwrap(),
        timestamp: timestamp + 60000,
    };
    
    account.handler_trade(&sell_trade)?;
    println!("   3. 部分平仓: {} BTC @ {}", sell_trade.quantity, sell_trade.price);
    println!("      账户权益: {}, 已实现盈亏: {}", 
            account.get_equity(), account.state.realized_pnl);
    
    // 4. 价格下跌
    timestamp += 120000;
    let tick2 = Tick {
        symbol: symbol.clone(),
        timestamp,
        open: prices[2],
        high: prices[2] * 1.01,
        low: prices[2] * 0.99,
        close: prices[2],
        volume: 1000.0,
    };
    
    account.on_tick(&tick2, None)?;
    println!("   4. 价格下跌到 {}: 权益={}, 收益率={:.2}%", 
            prices[2], account.get_equity(), account.get_total_return_f64() * 100.0);
    
    println!("✅ 多空交易和盈亏计算测试完成");
    Ok(())
}

/// 测试仓位转换（多空切换）
fn test_position_conversion() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔄 测试仓位转换（多空切换）...");
    
    let mut account = Account::with_default_config()?;
    let symbol = Symbol::new("ETHUSDT");
    let base_price = 3000.0;
    let mut timestamp = 2000000000000i64;
    
    println!("   仓位转换序列:");
    
    // 1. 建立多头仓位
    let long_trade = Trade {
        id: "long_1".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Buy,
        quantity: Decimal::new(10, 0), // 10 ETH
        price: Decimal::try_from(base_price).unwrap(),
        timestamp,
    };
    
    account.handler_trade(&long_trade)?;
    let long_ratio = account.get_position_ratio(&symbol, Decimal::try_from(base_price).unwrap());
    println!("   1. 建立多头: {} ETH @ {}, 仓位比例: {:.1}%", 
            long_trade.quantity, long_trade.price, long_ratio * 100.0);
    
    // 2. 转换为空头（需要卖出20 ETH：平掉10 ETH + 开空10 ETH）
    timestamp += 60000;
    let short_trade = Trade {
        id: "short_1".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Sell,
        quantity: Decimal::new(20, 0), // 20 ETH
        price: Decimal::try_from(base_price * 1.05).unwrap(), // 价格上涨5%
        timestamp,
    };
    
    account.handler_trade(&short_trade)?;
    let short_ratio = account.get_position_ratio(&symbol, Decimal::try_from(base_price * 1.05).unwrap());
    println!("   2. 转换空头: 卖出 {} ETH @ {}, 仓位比例: {:.1}%", 
            short_trade.quantity, short_trade.price, short_ratio * 100.0);
    println!("      已实现盈亏: {}, 当前权益: {}", 
            account.state.realized_pnl, account.get_equity());
    
    // 3. 价格下跌，空头盈利
    timestamp += 60000;
    let tick = Tick {
        symbol: symbol.clone(),
        timestamp,
        open: base_price * 0.95,
        high: base_price * 0.96,
        low: base_price * 0.94,
        close: base_price * 0.95,
        volume: 1000.0,
    };
    
    account.on_tick(&tick, None)?;
    println!("   3. 价格下跌到 {}: 权益={}, 总收益率={:.2}%", 
            tick.close, account.get_equity(), account.get_total_return_f64() * 100.0);
    
    // 4. 平仓
    timestamp += 60000;
    let close_trade = Trade {
        id: "close_1".to_string(),
        symbol: symbol.clone(),
        side: OrderSide::Buy,
        quantity: Decimal::new(10, 0), // 买入10 ETH平空
        price: Decimal::try_from(base_price * 0.95).unwrap(),
        timestamp,
    };
    
    account.handler_trade(&close_trade)?;
    let final_ratio = account.get_position_ratio(&symbol, Decimal::try_from(base_price * 0.95).unwrap());
    println!("   4. 平仓: 买入 {} ETH @ {}, 仓位比例: {:.1}%", 
            close_trade.quantity, close_trade.price, final_ratio * 100.0);
    println!("      最终权益: {}, 总收益率: {:.2}%, 已实现盈亏: {}", 
            account.get_equity(), account.get_total_return_f64() * 100.0, account.state.realized_pnl);
    
    println!("✅ 仓位转换测试完成");
    Ok(())
}
