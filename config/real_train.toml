# ==============================================================================
# DRL Trading System - 正式训练配置
# ==============================================================================
# 此配置用于正式的端到端训练
# 特点：大批次、长时间训练、模型保存、GPU加速

# ==============================================================================
# 应用程序基础配置
# ==============================================================================
[app]
name = "DRL Trading System - 正式端到端训练"
version = "0.1.0"
mode = "train"                          # 运行模式：train/backtest/live
output_dir = "./output/real_train"      # 结果输出目录
verbose = true                          # 是否启用详细日志
max_runtime_seconds = 0                 # 最大运行时间（秒），0=无限制

# 性能指标配置
[app.metrics]
enabled = true                          # 是否启用指标收集
output_file = "real_train_metrics.json" # 指标输出文件
update_frequency = 500                  # 指标更新频率（每N步）
save_detailed = true                    # 是否保存详细指标

# 检查点配置（模型保存）
[app.checkpoint]
enabled = true                          # 是否启用检查点（正式训练必须开启）
save_dir = "./checkpoints/real_train"   # 检查点保存目录
save_frequency = 2000                   # 保存频率（每N步）
max_checkpoints = 10                    # 最大保留检查点数量
auto_save_on_exit = true                # 退出时是否自动保存

# ==============================================================================
# 数据源配置
# ==============================================================================
[data]
provider_type = "historical"            # 数据提供器类型：historical/live
data_path = "data/sol_usdt_3m_30days.csv" # 历史数据文件路径
symbol = "SOLUSDT"                      # 交易品种
interval = "3m"                         # 数据时间间隔

# ==============================================================================
# 账户配置
# ==============================================================================
[account]
account_type = "simulated"              # 账户类型：simulated/live
initial_balance = 20000            # 初始资金（正式训练用标准金额）
fee_rate = 0.001                        # 交易手续费率（0.1%）
max_leverage = 1.0                      # 最大杠杆倍数

# ==============================================================================
# 执行配置
# ==============================================================================
[execution]
handler_type = "simulated"              # 执行处理器：simulated/live
slippage = 0.0001                       # 滑点设置（0.01%）
latency_ms = 0                          # 执行延迟（毫秒），训练时设为0

# ==============================================================================
# 环境配置
# ==============================================================================
[environment]
env_type = "trading"                    # 环境类型
reward_function = "profit_based"        # 奖励函数类型

# ==============================================================================
# 奖励配置（正式训练用，权重标准）
# ==============================================================================
[environment.reward]
# 主要权重配置
portfolio_change_weight = 100.0         # 主干奖励权重（正式训练用标准值）
transaction_cost_weight = 20.0          # 交易成本惩罚权重

# 持仓状态奖励配置  
profitable_holding_weight = 2.0         # 盈利持仓奖励权重（让利润奔跑）
losing_holding_weight = 3.0             # 亏损持仓惩罚权重（及时止损）
holding_reward_cap = 2.0                # 持仓奖励上限

# 风险控制配置
max_position_concentration = 0.8        # 最大持仓集中度阈值
concentration_penalty_coeff = 5.0       # 集中度惩罚系数（正式训练用标准值）
max_drawdown_threshold = 0.1            # 最大回撤阈值（正式训练用严格值）
drawdown_penalty_coeff = 20.0           # 回撤惩罚系数（正式训练用较大值）
max_volatility_threshold = 0.05         # 最大波动率阈值
volatility_penalty_coeff = 1.0          # 波动率惩罚系数

# ==============================================================================
# 学习算法通用配置
# ==============================================================================
[learn.common]
state_dim = 13                          # 状态维度：观测空间大小（动态获取）
action_dim = 4                          # 动作维度：4种离散动作(Hold/Buy/Sell/Close)
max_action = 1.0                        # 动作空间上界
min_action = 0                       # 动作空间下界
seed = 42                               # 随机种子，确保结果可复现

# ==============================================================================
# SAC算法特定配置
# ==============================================================================
[learn.sac]
gamma = 0.98                            # 折扣因子：未来奖励的重要性(0.99=更重视长期收益)
tau = 0.01                             # 软更新系数：目标网络更新速度(0.005=标准值)
alpha_init = 0.2                        # 初始熵系数：探索vs利用平衡(0.2=较多探索)
auto_entropy_tuning = true              # 是否自动调整熵系数（正式训练建议开启）
target_entropy = -4.0                   # 目标熵值（当auto_entropy_tuning=true时作为初始值）
num_q_networks = 2                      # Q网络数量（SAC标准为2）
policy_delay = 2                        # 策略网络更新延迟（相对于Q网络）

# ==============================================================================
# 神经网络架构配置
# ==============================================================================

# Actor网络（策略网络）配置
[learn.network.actor]
hidden_dims = [256, 512, 64]            # 隐藏层维度：[第1层, 第2层, 第3层]（更大网络）
activation = "relu"                     # 激活函数：relu/tanh/sigmoid
output_activation = "tanh"              # 输出层激活函数
init_type = "xavier"                    # 权重初始化：xavier/he/normal
log_std_min = -20.0                     # 对数标准差下界（连续动作用）
log_std_max = 2.0                       # 对数标准差上界（连续动作用）

# Critic网络（价值网络）配置
[learn.network.critic]
hidden_dims = [256, 512, 64]                # 隐藏层维度（正式训练用标准网络）
activation = "relu"                     # 激活函数
init_type = "xavier"                    # 权重初始化方法

# ==============================================================================
# 状态编码器配置
# ==============================================================================
[learn.network.encoder]
encoder_type = "transformer"           # 编码器类型: "transformer" | "mlp" | "gru" | "lstm"
output_dim = 512                        # 编码器输出维度（正式训练用标准维度）

# Transformer编码器专用参数
[learn.network.encoder.params]
d_model = 512                           # Transformer核心维度，必须与output_dim一致
nhead = 8                               # 多头注意力的头数，d_model必须能被此值整除
num_encoder_layers = 6                  # Transformer编码器层数（正式训练用标准层数）
dim_feedforward = 2048                  # 前馈网络维度（通常是d_model的4倍）
dropout = 0.1                           # Dropout比率
activation = "gelu"                     # 激活函数类型
max_seq_len = 1000                      # 最大序列长度
window_size = 100                        # 历史窗口大小

# ==============================================================================
# 训练配置 - 核心参数（标准强化学习流程）
# ==============================================================================
[learn.training]
# === 回合控制（用户明确指定）===
episodes = 100                     # 总回合数：训练将进行50个完整回合
steps_per_episode = 3000                # 每回合步数：每个回合固定执行2000步
                                        # 总步数 = episodes × steps_per_episode = 50 × 1000 = 50000步

# === 训练参数 ===
batch_size = 512                         # 批次大小：每次训练使用的样本数（正式训练用较大批次）
warmup_steps = 2000                    # 预热步数：开始训练前的随机探索步数
train_frequency = 10                     # 训练频率：每N步执行一次模型训练
eval_frequency = 10                     # 评估频率：每N个回合输出一次详细统计
grad_clip = 1.0                         # 梯度裁剪：防止梯度爆炸
weight_decay = 0.0001                   # 权重衰减：L2正则化系数

# 学习率配置
[learn.training.learning_rates]
actor = 0.001                          # Actor网络学习率（标准值）
critic = 0.001                         # Critic网络学习率
alpha = 0.0001                          # 熵系数学习率
scheduler_params = {}                   # 学习率调度器参数（空=不使用调度器）

# ==============================================================================
# 经验回放配置
# ==============================================================================
[learn.replay]
capacity = 50000                       # 回放缓冲区容量：存储的最大经验数（正式训练用大容量）
prioritized = true                     # 是否使用优先级回放（稳定后可尝试开启）

# ==============================================================================
# 设备配置
# ==============================================================================
[learn.device]
use_gpu = true                         # 是否尝试使用GPU（WSL环境建议关闭）
mixed_precision = true                 # 是否使用混合精度训练（实验性功能）

# ==============================================================================
# 检查点配置（学习模块专用）
# ==============================================================================
[learn.checkpoint]
save_dir = "./checkpoints/real_train"   # 模型保存目录
save_frequency = 10000                   # 保存频率（每N步）
keep_checkpoints = 10                   # 保留的检查点数量
save_best = true                        # 是否保存最佳模型
best_metric = "episode_reward"          # 最佳模型评估指标
