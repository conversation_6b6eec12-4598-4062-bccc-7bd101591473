# ==============================================================================
# DRL Trading System - 优化训练配置 v2.0
# ==============================================================================
# 此配置专为连续动作空间SAC算法优化
# 特点：连续仓位控制、高级奖励系统、GPU加速、完整风控

# ==============================================================================
# 应用程序基础配置
# ==============================================================================
[app]
name = "DRL Trading System - 连续动作优化训练"
version = "0.1.0"
mode = "train"                          # 运行模式：train/backtest/live
output_dir = "./output/optimized_train" # 结果输出目录
verbose = true                          # 是否启用详细日志
max_runtime_seconds = 0                 # 最大运行时间（秒），0=无限制

# 性能指标配置
[app.metrics]
enabled = true                          # 是否启用指标收集
output_file = "optimized_train_metrics.json" # 指标输出文件
update_frequency = 100                  # 指标更新频率（每N步）
save_detailed = true                    # 是否保存详细指标

# 检查点配置（模型保存）
[app.checkpoint]
enabled = true                          # 是否启用检查点
save_dir = "./checkpoints/optimized"    # 检查点保存目录
save_frequency = 1000                   # 保存频率（每N步）
max_checkpoints = 5                     # 最大保留检查点数量
auto_save_on_exit = true                # 退出时是否自动保存

# ==============================================================================
# 数据源配置
# ==============================================================================
[data]
provider_type = "historical"            # 数据提供器类型：historical/live
data_path = "data/sol_usdt_3m_30days.csv" # 历史数据文件路径
symbol = "SOLUSDT"                      # 交易品种
interval = "3m"                         # 数据时间间隔

# ==============================================================================
# 账户配置 - 使用新的AccountConfig结构
# ==============================================================================
[account]
initial_balance = 50000             # 初始资金：5万（适中金额）
base_currency = "USDT"                  # 基础货币
fee_rate = 0.001                        # 手续费率：0.1%
slippage = 0.0001                       # 滑点：0.01%
leverage = 1.0                          # 杠杆倍数：无杠杆
min_trade_value = "100.0"               # 最小交易价值：100元
max_position_ratio = 1.0                # 最大仓位比例：100%

# ==============================================================================
# 环境配置 - 使用高级奖励系统
# ==============================================================================
[environment]
env_type = "trading"                    # 环境类型
reward_function = "advanced"            # 使用高级奖励函数

# 高级奖励配置 - 优化权重平衡
[environment.advanced_reward]
# 核心奖励权重（平衡设计）
portfolio_change_weight = 10.0          # 权益变化权重（主要驱动力）
transaction_cost_weight = 2.0           # 交易成本权重（适度惩罚）
holding_reward_weight = 1.0             # 持仓奖励权重（鼓励持仓）
risk_penalty_weight = 5.0               # 风险惩罚权重（重要的风控）

# 风险控制参数
max_drawdown_threshold = 0.15           # 最大回撤阈值：15%
max_position_concentration = 0.8        # 最大持仓集中度：80%
volatility_threshold = 0.05             # 波动率阈值：5%

# 奖励调节参数
reward_scale = 0.01                     # 奖励缩放因子（避免过大奖励）
normalize_rewards = true                # 是否归一化奖励

# ==============================================================================
# 学习算法通用配置 - 连续动作空间优化
# ==============================================================================
[learn.common]
state_dim = 512                         # 状态维度：与编码器输出维度一致
action_dim = 1                          # 动作维度：1维连续动作（仓位比例）
max_action = 1.0                        # 动作空间上界（100%多仓）
min_action = -1.0                       # 动作空间下界（100%空仓）
seed = 42                               # 随机种子，确保结果可复现

# ==============================================================================
# SAC算法特定配置 - 连续动作优化
# ==============================================================================
[learn.sac]
gamma = 0.99                            # 折扣因子：0.99（标准值，重视长期收益）
tau = 0.005                             # 软更新系数：0.005（标准值）
alpha_init = 0.2                        # 初始熵系数：0.2（平衡探索与利用）
auto_entropy_tuning = true              # 自动调整熵系数（推荐开启）
target_entropy = -1.0                   # 目标熵值：-action_dim（连续动作标准）
num_q_networks = 2                      # Q网络数量：2（SAC标准）
policy_delay = 1                        # 策略更新延迟：1（连续动作推荐）

# ==============================================================================
# 神经网络架构配置 - 连续动作优化
# ==============================================================================

# Actor网络（策略网络）配置 - 连续动作专用
[learn.network.actor]
hidden_dims = [512, 256, 128]           # 隐藏层维度：递减设计，适合连续动作
activation = "relu"                     # 激活函数：ReLU（标准选择）
output_activation = "tanh"              # 输出激活：tanh（连续动作必须）
init_type = "xavier"                    # 权重初始化：Xavier（推荐）
log_std_min = -20.0                     # 对数标准差下界（防止退化）
log_std_max = 2.0                       # 对数标准差上界（控制探索）

# Critic网络（价值网络）配置
[learn.network.critic]
hidden_dims = [512, 256, 128]           # 隐藏层维度：与Actor对称
activation = "relu"                     # 激活函数：ReLU
init_type = "xavier"                    # 权重初始化：Xavier

# ==============================================================================
# 状态编码器配置 - 统计编码器（稳定可靠）
# ==============================================================================
[learn.network.encoder]
encoder_type = "statistical"           # 编码器类型：统计编码器（更稳定）
output_dim = 512                        # 编码器输出维度：512
window_size = 100                       # 历史窗口大小：100个时间步

# 统计编码器参数
[learn.network.encoder.params]
include_returns = true                  # 包含收益率特征
include_volatility = true              # 包含波动率特征
include_volume = true                   # 包含成交量特征
include_technical = true               # 包含技术指标
normalization = "zscore"               # 归一化方法：Z-score

# ==============================================================================
# 训练配置 - 连续动作优化参数
# ==============================================================================
[learn.training]
# 回合控制 - 适度训练量
episodes = 200                          # 总回合数：200个回合（充分训练）
steps_per_episode = 1000                # 每回合步数：1000步（适中长度）
                                        # 总步数 = 200 × 1000 = 200,000步

# 训练参数 - 连续动作优化
batch_size = 256                        # 批次大小：256（平衡效率与稳定性）
warmup_steps = 1000                     # 预热步数：1000步（充分探索）
train_frequency = 4                     # 训练频率：每4步训练一次（高频更新）
eval_frequency = 10                     # 评估频率：每10回合评估
grad_clip = 0.5                         # 梯度裁剪：0.5（连续动作推荐）
weight_decay = 1e-5                     # 权重衰减：较小值（避免过度正则化）

# 学习率配置 - 连续动作优化
[learn.training.learning_rates]
actor = 3e-4                            # Actor学习率：3e-4（连续动作标准）
critic = 3e-4                           # Critic学习率：3e-4（与Actor一致）
alpha = 3e-4                            # 熵系数学习率：3e-4（自动调整用）

# ==============================================================================
# 经验回放配置 - 连续动作优化
# ==============================================================================
[learn.replay]
capacity = 100000                       # 回放缓冲区容量：10万（大容量提升稳定性）
prioritized = false                     # 优先级回放：关闭（连续动作先用标准回放）

# ==============================================================================
# 设备配置 - GPU加速
# ==============================================================================
[learn.device]
use_gpu = true                          # 使用GPU：开启（Apple M1 Pro支持）
mixed_precision = false                 # 混合精度：关闭（稳定性优先）

# ==============================================================================
# 检查点配置 - 模型保存
# ==============================================================================
[learn.checkpoint]
save_dir = "./checkpoints/optimized"    # 模型保存目录
save_frequency = 5000                   # 保存频率：每5000步
keep_checkpoints = 5                    # 保留检查点数量：5个
save_best = true                        # 保存最佳模型：开启
best_metric = "episode_reward"          # 最佳模型指标：回合奖励

# ==============================================================================
# 日志配置
# ==============================================================================
[logging]
level = "info"                          # 日志级别：info
console = true                          # 控制台输出：开启
file_enabled = true                     # 文件日志：开启
file_path = "./logs/optimized_train.log" # 日志文件路径
