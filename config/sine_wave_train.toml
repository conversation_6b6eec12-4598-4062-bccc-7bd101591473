# ==============================================================================
# 正弦波数据训练配置 v2.0 - 连续动作优化版本
# 专门用于验证连续动作SAC算法的学习能力
# ==============================================================================

# ==============================================================================
# 应用程序配置
# ==============================================================================
[app]
name = "DRL Trading System - 正弦波验证训练"
version = "0.1.0"
mode = "train"
output_dir = "./output/sine_wave_train"
verbose = true
max_runtime_seconds = 0

# 性能指标配置
[app.metrics]
enabled = true
output_file = "sine_wave_train_metrics.json"
update_frequency = 50
save_detailed = true

# 检查点配置
[app.checkpoint]
enabled = true
save_dir = "./checkpoints/sine_wave"
save_frequency = 1000
max_checkpoints = 3
auto_save_on_exit = true

# ==============================================================================
# 数据配置 - 优化的正弦波数据
# ==============================================================================
[data]
provider_type = "synthetic"             # 数据提供器类型：synthetic（合成数据）
symbol = "SINEUSDT"                     # 交易对符号
data_path = ""                          # 合成数据不需要文件路径
interval = "1m"                         # 时间间隔（正弦波数据用）

# 正弦波参数 - 优化设计
[data.sine_wave]
base_price = 100.0                      # 基础价格：100
amplitude = 15.0                        # 振幅：±15（适中波动）
period = 200.0                          # 周期：200步（更长周期，便于学习）
phase = 0.0                             # 相位偏移：0
trend_slope = 0.01                      # 趋势斜率：轻微上升趋势
noise_level = 0.02                      # 噪声水平：2%（降低噪声）
total_points = 10000                    # 总数据点数：1万（充足数据）
time_interval_ms = 60000                # 时间间隔：1分钟

# ==============================================================================
# 账户配置 - 正弦波测试专用
# ==============================================================================
[account]
account_type = "simulated"              # 账户类型：simulated
initial_balance = 10000                 # 初始资金：1万（测试用）
fee_rate = 0.0005                       # 手续费率：0.05%（降低成本）
max_leverage = 1.0                      # 最大杠杆倍数：无杠杆

# ==============================================================================
# 执行配置
# ==============================================================================
[execution]
handler_type = "simulated"              # 执行处理器：simulated
slippage = 0.00005                      # 滑点：0.005%（降低滑点）
latency_ms = 0                          # 执行延迟

# ==============================================================================
# 环境配置 - 高级奖励系统（测试版）
# ==============================================================================
[environment]
env_type = "trading"                    # 环境类型
reward_function = "advanced"            # 使用高级奖励函数

# 高级奖励配置 - 正弦波优化
[environment.advanced_reward]
portfolio_change_weight = 100.0         # 权益变化权重（主要信号）
transaction_cost_weight = 1.0           # 交易成本权重（轻微惩罚）
holding_reward_weight = 0.5             # 持仓奖励权重（鼓励持仓）
risk_penalty_weight = 0.1               # 风险惩罚权重（降低）

# 风险控制参数（宽松设置）
max_drawdown_threshold = 0.3            # 最大回撤阈值：30%
max_position_concentration = 1.0        # 最大持仓集中度：100%
volatility_threshold = 0.2              # 波动率阈值：20%

# 奖励调节参数
reward_scale = 0.001                    # 奖励缩放因子（小值）
normalize_rewards = true                # 归一化奖励

# ==============================================================================
# 学习算法配置 - 正弦波连续动作优化
# ==============================================================================
[learn.common]
state_dim = 512                         # 状态维度：512（与编码器一致）
action_dim = 1                          # 动作维度：1维连续动作
max_action = 1.0                        # 最大动作值：100%多仓
min_action = -1.0                       # 最小动作值：100%空仓
seed = 123                              # 随机种子：不同于实盘训练

# ==============================================================================
# SAC算法配置 - 正弦波优化
# ==============================================================================
[learn.sac]
gamma = 0.95                            # 折扣因子：0.95（短期导向，适合正弦波）
tau = 0.01                              # 软更新系数：0.01（快速更新）
alpha_init = 0.1                        # 初始熵系数：0.1（较少探索）
auto_entropy_tuning = true              # 自动调整熵系数
target_entropy = -1.0                   # 目标熵值：-1（标准值）
num_q_networks = 2                      # Q网络数量：2
policy_delay = 1                        # 策略更新延迟：1

# ==============================================================================
# 神经网络配置 - 正弦波优化版本
# ==============================================================================

# Actor网络配置 - 正弦波专用
[learn.network.actor]
hidden_dims = [256, 128, 64]            # 隐藏层维度：递减设计
activation = "relu"                     # 激活函数：ReLU
output_activation = "tanh"              # 输出激活：tanh（必须）
init_type = "xavier"                    # 权重初始化：Xavier
log_std_min = -10.0                     # 对数标准差下界（放宽）
log_std_max = 2.0                       # 对数标准差上界

# Critic网络配置
[learn.network.critic]
hidden_dims = [256, 128, 64]            # 隐藏层维度：与Actor一致
activation = "relu"                     # 激活函数：ReLU
init_type = "xavier"                    # 权重初始化：Xavier

# ==============================================================================
# 状态编码器配置 - 正弦波优化
# ==============================================================================
[learn.network.encoder]
encoder_type = "m_l_p"                 # MLP编码器（稳定）
output_dim = 512                        # 编码器输出维度：512
window_size = 50                        # 历史窗口大小：50（适中）

# MLP编码器参数
[learn.network.encoder.params]
hidden_dims = [256, 512]               # 隐藏层维度
activation = "relu"                    # 激活函数
dropout = 0.1                          # Dropout比率

# ==============================================================================
# 训练配置 - 正弦波快速验证
# ==============================================================================
[learn.training]
# 回合控制 - 快速验证
episodes = 50                           # 总回合数：50（充分验证）
steps_per_episode = 500                 # 每回合步数：500（适中）

# 训练参数 - 正弦波优化
batch_size = 128                        # 批次大小：128（平衡）
warmup_steps = 200                      # 预热步数：200（充分探索）
train_frequency = 2                     # 训练频率：每2步训练（高频）
eval_frequency = 5                      # 评估频率：每5回合
grad_clip = 0.5                         # 梯度裁剪：0.5
weight_decay = 1e-6                     # 权重衰减：很小值

# 学习率配置 - 正弦波优化
[learn.training.learning_rates]
actor = 1e-3                            # Actor学习率：1e-3（较高）
critic = 1e-3                           # Critic学习率：1e-3
alpha = 1e-4                            # 熵系数学习率：1e-4
scheduler_params = {}                   # 学习率调度器参数

# ==============================================================================
# 经验回放配置 - 正弦波优化
# ==============================================================================
[learn.replay]
capacity = 25000                        # 回放缓冲区容量：2.5万
prioritized = false                     # 不使用优先级回放

# ==============================================================================
# 设备配置 - 快速训练
# ==============================================================================
[learn.device]
use_gpu = true                          # 使用GPU：开启（加速）
mixed_precision = false                 # 混合精度：关闭（稳定性）

# ==============================================================================
# 检查点配置 - 正弦波专用
# ==============================================================================
[learn.checkpoint]
save_dir = "./checkpoints/sine_wave"    # 保存目录
save_frequency = 2000                   # 保存频率：每2000步
keep_checkpoints = 3                    # 保留检查点：3个
save_best = true                        # 保存最佳模型
best_metric = "episode_reward"          # 最佳指标：回合奖励

# ==============================================================================
# 日志配置
# ==============================================================================
[logging]
level = "info"                          # 日志级别：info
console = true                          # 控制台输出：开启
file_enabled = true                     # 文件日志：开启
file_path = "./logs/sine_wave_train.log" # 日志文件路径
