# ==============================================================================
# 正弦波数据训练配置
# 专门用于验证模型学习能力的简化配置
# ==============================================================================

# ==============================================================================
# 数据配置 - 使用合成正弦波数据
# ==============================================================================
[data]
source = "synthetic"                    # 数据源类型：synthetic（合成数据）
symbol = "SINEUSDT"                     # 交易对符号
data_path = ""                          # 合成数据不需要文件路径

# 正弦波参数
[data.sine_wave]
base_price = 100.0                      # 基础价格
amplitude = 20.0                        # 振幅（±20的价格波动）
period = 100.0                          # 周期（100个时间步完成一个周期）
phase = 0.0                             # 相位偏移
trend_slope = 0.0                       # 趋势斜率（0=无趋势）
noise_level = 0.05                      # 噪声水平（5%）
total_points = 5000                     # 总数据点数
time_interval_ms = 60000                # 时间间隔（1分钟）

# ==============================================================================
# 账户配置 - 使用新的AccountConfig结构
# ==============================================================================
[account]
initial_balance = "100000.0"            # 初始资金：10万
base_currency = "USDT"                  # 基础货币
fee_rate = 0.001                        # 手续费率：0.1%
slippage = 0.0001                       # 滑点：0.01%
leverage = 1.0                          # 杠杆倍数：无杠杆
min_trade_value = "50.0"                # 最小交易价值：50元
max_position_ratio = 1.0                # 最大仓位比例：100%

# ==============================================================================
# 环境配置 - 简化版本
# ==============================================================================
[environment]
env_type = "trading"                    # 环境类型
reward_function = "simple"              # 使用简化奖励函数

# 简化奖励配置
[environment.simple_reward]
reward_scale = 1.0                      # 奖励缩放因子
transaction_cost_weight = 0.1           # 交易成本权重（降低）

# ==============================================================================
# 学习算法配置 - 连续动作空间
# ==============================================================================
[learn.common]
state_dim = 256                         # 状态维度（与编码器输出一致）
action_dim = 1                          # 动作维度：1维连续动作（仓位比例）
max_action = 1.0                        # 最大动作值（100%仓位）
min_action = -1.0                       # 最小动作值（100%空仓）
seed = 42                               # 随机种子

# ==============================================================================
# SAC算法配置 - 适合连续动作
# ==============================================================================
[learn.sac]
gamma = 0.99                            # 折扣因子
tau = 0.005                             # 软更新系数
alpha_init = 0.2                        # 初始熵系数
auto_entropy_tuning = true              # 自动调整熵系数
target_entropy = -1.0                   # 目标熵值（负的动作维度）
num_q_networks = 2                      # Q网络数量
policy_delay = 1                        # 策略更新延迟

# ==============================================================================
# 神经网络配置 - 简化版本
# ==============================================================================

# Actor网络配置
[learn.network.actor]
hidden_dims = [128, 128]                # 隐藏层维度（简化）
activation = "relu"                     # 激活函数
output_activation = "tanh"              # 输出激活函数
init_type = "xavier"                    # 权重初始化
log_std_min = -20.0                     # 对数标准差下界
log_std_max = 2.0                       # 对数标准差上界

# Critic网络配置
[learn.network.critic]
hidden_dims = [128, 128]                # 隐藏层维度（简化）
activation = "relu"                     # 激活函数
init_type = "xavier"                    # 权重初始化

# ==============================================================================
# 状态编码器配置 - 简化版本
# ==============================================================================
[learn.network.encoder]
encoder_type = "statistical"           # 使用统计编码器（更简单）
output_dim = 256                        # 编码器输出维度
window_size = 20                        # 历史窗口大小（减小）

# ==============================================================================
# 训练配置 - 快速验证
# ==============================================================================
[learn.training]
# 回合控制
episodes = 20                           # 总回合数（快速测试）
steps_per_episode = 200                 # 每回合步数（减少）

# 训练参数
batch_size = 64                         # 批次大小（减小）
warmup_steps = 100                      # 预热步数（减少）
train_frequency = 5                     # 训练频率
eval_frequency = 5                      # 评估频率
grad_clip = 1.0                         # 梯度裁剪
weight_decay = 0.0001                   # 权重衰减

# 学习率配置
[learn.training.learning_rates]
actor = 0.0003                          # Actor学习率（降低）
critic = 0.0003                         # Critic学习率（降低）
alpha = 0.0001                          # 熵系数学习率

# ==============================================================================
# 经验回放配置
# ==============================================================================
[learn.replay]
capacity = 10000                        # 回放缓冲区容量（减小）
prioritized = false                     # 不使用优先级回放（简化）

# ==============================================================================
# 设备配置
# ==============================================================================
[learn.device]
use_gpu = false                         # 不使用GPU（简化）
mixed_precision = false                 # 不使用混合精度

# ==============================================================================
# 检查点配置
# ==============================================================================
[learn.checkpoint]
save_dir = "./checkpoints/sine_wave"    # 保存目录
save_frequency = 1000                   # 保存频率
keep_checkpoints = 5                    # 保留检查点数量
save_best = true                        # 保存最佳模型
best_metric = "episode_reward"          # 最佳模型指标

# ==============================================================================
# 日志配置
# ==============================================================================
[logging]
level = "info"                          # 日志级别
file = "./output/sine_wave_train.log"   # 日志文件
console = true                          # 控制台输出
