# ==============================================================================
# DRL Trading System - 快速测试配置 v2.0
# ==============================================================================
# 此配置用于快速验证连续动作空间SAC算法的基本功能
# 特点：小规模、快速训练、基本验证

# ==============================================================================
# 应用程序配置
# ==============================================================================
[app]
name = "DRL Trading System - 快速功能测试"
version = "0.1.0"
mode = "train"
output_dir = "./output/test_optimized"
verbose = true
max_runtime_seconds = 300               # 最大运行5分钟

# 性能指标配置
[app.metrics]
enabled = true
output_file = "test_optimized_metrics.json"
update_frequency = 50
save_detailed = true

# 检查点配置
[app.checkpoint]
enabled = true
save_dir = "./checkpoints/test"
save_frequency = 200
max_checkpoints = 2
auto_save_on_exit = true

# ==============================================================================
# 数据源配置 - 使用真实数据
# ==============================================================================
[data]
provider_type = "historical"
data_path = "data/sol_usdt_3m_30days.csv"
symbol = "SOLUSDT"
interval = "3m"

# ==============================================================================
# 账户配置 - 测试用小金额
# ==============================================================================
[account]
account_type = "simulated"              # 账户类型
initial_balance = 1000                  # 初始资金：1000元（测试用）
fee_rate = 0.001                        # 手续费率：0.1%
max_leverage = 1.0                      # 最大杠杆倍数

# ==============================================================================
# 执行配置
# ==============================================================================
[execution]
handler_type = "simulated"              # 执行处理器
slippage = 0.0001                       # 滑点：0.01%
latency_ms = 0                          # 执行延迟

# ==============================================================================
# 环境配置 - 高级奖励系统
# ==============================================================================
[environment]
env_type = "trading"
reward_function = "advanced"

# 高级奖励配置 - 测试优化
[environment.advanced_reward]
portfolio_change_weight = 50.0          # 权益变化权重（主要信号）
transaction_cost_weight = 5.0           # 交易成本权重（适度惩罚）
holding_reward_weight = 1.0             # 持仓奖励权重
risk_penalty_weight = 2.0               # 风险惩罚权重

# 风险控制参数
max_drawdown_threshold = 0.2            # 最大回撤：20%
max_position_concentration = 0.9        # 最大持仓集中度：90%
volatility_threshold = 0.1              # 波动率阈值：10%

# 奖励调节
reward_scale = 0.01                     # 奖励缩放
normalize_rewards = true                # 归一化奖励

# ==============================================================================
# 学习算法配置 - 连续动作
# ==============================================================================
[learn.common]
state_dim = 512                         # 状态维度：512
action_dim = 1                          # 动作维度：1（连续）
max_action = 1.0                        # 最大动作：100%多仓
min_action = -1.0                       # 最小动作：100%空仓
seed = 42                               # 随机种子

# ==============================================================================
# SAC算法配置 - 快速测试
# ==============================================================================
[learn.sac]
gamma = 0.99                            # 折扣因子：0.99
tau = 0.005                             # 软更新：0.005
alpha_init = 0.2                        # 初始熵系数：0.2
auto_entropy_tuning = true              # 自动调整熵系数
target_entropy = -1.0                   # 目标熵值：-1
num_q_networks = 2                      # Q网络数量：2
policy_delay = 1                        # 策略延迟：1

# ==============================================================================
# 神经网络配置 - 轻量级
# ==============================================================================

# Actor网络配置
[learn.network.actor]
hidden_dims = [256, 128]                # 隐藏层：轻量级
activation = "relu"                     # 激活函数：ReLU
output_activation = "tanh"              # 输出激活：tanh
init_type = "xavier"                    # 初始化：Xavier
log_std_min = -20.0                     # 对数标准差下界
log_std_max = 2.0                       # 对数标准差上界

# Critic网络配置
[learn.network.critic]
hidden_dims = [256, 128]                # 隐藏层：与Actor一致
activation = "relu"                     # 激活函数：ReLU
init_type = "xavier"                    # 初始化：Xavier

# ==============================================================================
# 状态编码器配置 - MLP编码器
# ==============================================================================
[learn.network.encoder]
encoder_type = "m_l_p"                 # 编码器类型：MLP（多层感知机）
output_dim = 512                        # 输出维度：512
window_size = 50                        # 窗口大小：50

# MLP编码器参数
[learn.network.encoder.params]
hidden_dims = [256, 512]               # 隐藏层维度
activation = "relu"                    # 激活函数
dropout = 0.1                          # Dropout比率

# ==============================================================================
# 训练配置 - 快速测试
# ==============================================================================
[learn.training]
# 回合控制 - 小规模测试
episodes = 10                           # 总回合数：10（快速测试）
steps_per_episode = 100                 # 每回合步数：100

# 训练参数
batch_size = 64                         # 批次大小：64
warmup_steps = 50                       # 预热步数：50
train_frequency = 2                     # 训练频率：每2步
eval_frequency = 2                      # 评估频率：每2回合
grad_clip = 0.5                         # 梯度裁剪：0.5
weight_decay = 1e-5                     # 权重衰减

# 学习率配置
[learn.training.learning_rates]
actor = 3e-4                            # Actor学习率
critic = 3e-4                           # Critic学习率
alpha = 3e-4                            # 熵系数学习率
scheduler_params = {}                   # 学习率调度器参数

# ==============================================================================
# 经验回放配置
# ==============================================================================
[learn.replay]
capacity = 5000                         # 回放容量：5000
prioritized = false                     # 不使用优先级回放

# ==============================================================================
# 设备配置
# ==============================================================================
[learn.device]
use_gpu = true                          # 使用GPU
mixed_precision = false                 # 不使用混合精度

# ==============================================================================
# 检查点配置
# ==============================================================================
[learn.checkpoint]
save_dir = "./checkpoints/test"         # 保存目录
save_frequency = 500                    # 保存频率：每500步
keep_checkpoints = 2                    # 保留2个检查点
save_best = true                        # 保存最佳模型
best_metric = "episode_reward"          # 最佳指标

# ==============================================================================
# 日志配置
# ==============================================================================
[logging]
level = "info"                          # 日志级别
console = true                          # 控制台输出
file_enabled = true                     # 文件日志
file_path = "./logs/test_optimized.log" # 日志文件
