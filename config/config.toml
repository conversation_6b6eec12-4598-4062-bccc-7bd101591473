[server]
name = "exchange"
version = "0.1.11"
# 服务器ip 端口
ip = "0.0.0.0"
# 服务器端口
port = 8091
ws_port = 8092
# 服务器运行模式
mode = "debug"

debug = true
# api前缀
api_prefix = "/exchange"

[redis]
# url = "redis://:sbxz4014@************:6579/"
url = "redis://:root@************:6379/"


[kafka]
url = "************:9094"
order_command_topic = "order_commands"
order_match_topic = "order_match"
depth_topic = "depth"
kline_topic = "kline"
account_match_topic = "account_match_topic"
group = "ubuntu_group"
producer_timeout_ms = 1000

[nacos]
server_addr  = "************:8848"
# server_addr  = "127.0.0.1:8848"
namespace = "public"
ip = "************"
exchange_service_name = "sb-exchange-dev"
market_service_name = "sb-market-dev"

[http]
exchange_url = "http://************:8091"
url = "http://************:9070"

[database]
link = 'mysql://exchange:sbxz4014@************:3307/exchange'
init_database = true
sync_tables = true

# link = '***********************************************/services'

[tdengine]
url = "ws://************:6041"
database = "kline"
username = "root"
password = "taosdata"
enabled = true
