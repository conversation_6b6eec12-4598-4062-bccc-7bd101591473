# DRL Trading System 回测配置示例

[app]
name = "DRL Trading System"
version = "0.1.0"
mode = "backtest"
output_dir = "./output"
verbose = true
max_runtime_seconds = 3600  # 1小时最大运行时间

[app.metrics]
enabled = true
output_file = "backtest_metrics.json"
update_frequency = 100
save_detailed = true

[app.checkpoint]
enabled = true
save_dir = "./checkpoints"
save_frequency = 1000
max_checkpoints = 5
auto_save_on_exit = true

[data]
provider_type = "historical"
data_path = "data/sol_usdt_3m_30days.csv"
symbol = "SOLUSDT"
interval = "3m"

[account]
account_type = "simulated"
initial_balance = "100000.0"
fee_rate = 0.001
max_leverage = 1.0

[execution]
handler_type = "simulated"
slippage = 0.0001
latency_ms = 10

[environment]
env_type = "trading"
reward_function = "profit_based"

[learn.common]
state_dim = 14
action_dim = 2
max_action = 1.0
min_action = -1.0
seed = 42

[learn.sac]
gamma = 0.99
tau = 0.005
alpha_init = 0.2
auto_entropy_tuning = true
target_entropy = -2.0
num_q_networks = 2
policy_delay = 2

[learn.network.actor]
hidden_dims = [64, 64]
activation = "relu"
output_activation = "tanh"
init_type = "xavier"
log_std_min = -20.0
log_std_max = 2.0

[learn.network.critic]
hidden_dims = [64, 64]
activation = "relu"
init_type = "xavier"

[learn.training]
batch_size = 256
total_steps = 100000
warmup_steps = 1000
eval_frequency = 1000
eval_episodes = 10
grad_clip = 1.0
weight_decay = 0.0001

[learn.training.learning_rates]
actor = 0.0003
critic = 0.0003
alpha = 0.0003
scheduler_params = {}

[learn.replay]
capacity = 100000
prioritized = false

[learn.device]
use_gpu = false
mixed_precision = false

[learn.checkpoint]
save_dir = "./checkpoints"
save_frequency = 5000
keep_checkpoints = 5
save_best = true
best_metric = "episode_reward" 