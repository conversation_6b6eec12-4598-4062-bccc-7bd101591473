refresh_rate: 30 seconds

appenders:
  # Console output [{t}] {l} {M} - {m}{n}  pattern: "{d(%Y-%m-%d %H:%M:%S%.3f)} [{t}] {h({l})} [{T}] {f}:{L} - {m}{n}"
  # pattern: "{d(%Y-%m-%d %H:%M:%S%.3f)} [{t}] {l} {M} - {m}{n}"
  stdout:
    kind: console
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S%.3f)} [{t}] {h({l})} [{T}] {f}:{L} - {m}{n}"
    filters:
      - kind: threshold
        level: debug

  # Application logs
  app_log:
    kind: rolling_file
    path: "log/app.log"
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S%.3f)} [{t}] {h({l})} [{T}] {f}:{L} - {m}{n}"
    policy:
      trigger:
        kind: size
        limit: "100 mb"
      roller:
        kind: fixed_window
        pattern: "log/app_{}.log"
        count: 5
        base: 1

  # Error logs
  error_log:
    kind: rolling_file
    path: "log/error.log"
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S%.3f)} [{t}] {h({l})} [{T}] {f}:{L} - {m}{n}"
    filters:
      - kind: threshold
        level: error
    policy:
      trigger:
        kind: size
        limit: "50 mb"
      roller:
        kind: fixed_window
        pattern: "log/error_{}.log"
        count: 5
        base: 1

  # Access logs
  access_log:
    kind: rolling_file
    path: "log/access.log"
    encoder:
      pattern: "{d(%Y-%m-%d %H:%M:%S%.3f)} [{t}] {h({l})} [{T}] - {m}{n}"
    policy:
      trigger:
        kind: size
        limit: "200 mb"
      roller:
        kind: fixed_window
        pattern: "log/access_{}.log"
        count: 7
        base: 1

loggers:
  # App-specific logging configuration
  app::backend::db:
    level: info
    appenders:
      - app_log
    additive: false

  app::requests:
    level: info
    appenders:
      - access_log
    additive: false

root:
  level: info
  appenders:
    - stdout
    - app_log
    - error_log