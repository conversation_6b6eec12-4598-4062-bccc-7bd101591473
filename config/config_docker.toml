[server]
name = "exchange"
version = "0.1.11"
# 服务器ip 端口
ip = "0.0.0.0"
# 服务器端口
port = 8091
ws_port = 8092

debug = true
# api前缀
api_prefix = "/exchange"

[redis]
# url = "redis://:sbxz4014@************:6579/"
url = "redis://:root@************:6379/"

[kafka]
url = "************:9094"
order_command_topic = "order_commands"
order_match_topic = "order_match"
depth_topic = "depth_topic"
trade_topic = "trade_topic"
account_match_topic = "account_match_topic"
group = "ubuntu_group"
producer_timeout_ms = 1000

[nacos]
server_addr  = "************:8848"
# server_addr  = "127.0.0.1:8848"
namespace = "public"
ip = "************"
exchange_service_name = "sb-exchange"
market_service_name = "sb-market"

[http]
exchange_url = "http://************:8091"
url = "http://************:9070"

[database]
link = 'mysql://exchange:sbxz4014@mysql:3306/exchange'
init_database = true
sync_tables = true

[tdengine]
url = "ws://************:6041"
database = "exchange"
username = "default"
password = "password123"
enabled = true
